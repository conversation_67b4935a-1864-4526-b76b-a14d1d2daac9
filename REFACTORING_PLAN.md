# KnowledgeGraphVisualizer Refactoring Plan

## Phase 1: Critical Structural Improvements (Week 1-2)

### 1.1 Standardize Directory Structure

**Current Structure:**
```
├── 360t-kg-api/          # Inconsistent naming
├── 360t-kg-ui/           # Inconsistent naming  
├── proxy-server/         # Kebab-case
├── llm_abstraction/      # Snake_case
├── config/               # Generic name
└── services/             # Generic name
```

**Proposed Structure:**
```
├── packages/
│   ├── kg-api/           # Renamed from 360t-kg-api
│   ├── kg-ui/            # Renamed from 360t-kg-ui
│   ├── kg-proxy/         # Renamed from proxy-server
│   └── kg-chat/          # New service for Python chat functionality
├── shared/
│   ├── config/           # Centralized configuration
│   ├── types/            # Shared TypeScript/JavaScript types
│   └── utils/            # Shared utilities
├── libs/
│   ├── llm-abstraction/  # Renamed from llm_abstraction
│   └── graph-services/   # Renamed from services
└── tools/
    ├── scripts/          # Build and utility scripts
    └── docs/             # Centralized documentation
```

### 1.2 Consolidate Configuration Management

**Create Unified Configuration System:**

1. **Single source of truth**: `shared/config/`
2. **Environment-specific configs**: Development, staging, production
3. **Type-safe configuration**: Use TypeScript interfaces
4. **Validation**: Runtime configuration validation

**Implementation Steps:**

1. Create `shared/config/base.config.ts`
2. Create environment-specific overrides
3. Migrate existing configurations
4. Update all services to use unified config

### 1.3 Break Down Monolithic Files

**Priority Files to Refactor:**

1. **360t-kg-ui/src/App.jsx (906 lines)**
   - Extract view components
   - Create routing system
   - Separate state management

2. **config/environment.py (500+ lines)**
   - Split into domain-specific configs
   - Create configuration factory
   - Implement validation layer

3. **llm_abstraction/llm_manager.py (826+ lines)**
   - Extract provider management
   - Separate error handling
   - Create strategy pattern for provider selection

## Phase 2: Code Quality Improvements (Week 3-4)

### 2.1 Eliminate Code Duplication

**API Service Consolidation:**
```typescript
// shared/utils/api-client.ts
export class ApiClient {
  constructor(private baseUrl: string) {}
  
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    // Unified GET implementation
  }
  
  async post<T>(endpoint: string, data?: any): Promise<T> {
    // Unified POST implementation
  }
}

// Usage in services
const analysisApi = new ApiClient('/api/analysis');
const chatApi = new ApiClient('/api/chat');
```

**Environment Variable Handling:**
```typescript
// shared/config/env.ts
export class EnvironmentConfig {
  static load(service: string): ServiceConfig {
    // Unified environment loading
  }
}
```

### 2.2 Standardize Error Handling

**Create Unified Error System:**
```typescript
// shared/utils/errors.ts
export class AppError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500
  ) {
    super(message);
  }
}

export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 'VALIDATION_ERROR', 400);
  }
}
```

### 2.3 Implement Consistent Logging

**Unified Logging System:**
```typescript
// shared/utils/logger.ts
export class Logger {
  static create(service: string): LoggerInstance {
    // Service-specific logger with consistent format
  }
}
```

## Phase 3: Architecture Improvements (Week 5-6)

### 3.1 Implement Dependency Injection

**Create Service Container:**
```typescript
// shared/di/container.ts
export class ServiceContainer {
  private services = new Map();
  
  register<T>(token: string, factory: () => T): void {
    this.services.set(token, factory);
  }
  
  resolve<T>(token: string): T {
    const factory = this.services.get(token);
    return factory();
  }
}
```

### 3.2 Standardize API Contracts

**Create OpenAPI Specifications:**
1. Define schemas for all APIs
2. Generate TypeScript types
3. Implement runtime validation
4. Create API documentation

### 3.3 Implement Event-Driven Architecture

**Service Communication:**
```typescript
// shared/events/event-bus.ts
export class EventBus {
  emit(event: string, data: any): void {
    // Event emission logic
  }
  
  on(event: string, handler: (data: any) => void): void {
    // Event subscription logic
  }
}
```

## Phase 4: Testing & Documentation (Week 7-8)

### 4.1 Standardize Testing Approach

**Unified Testing Strategy:**
1. **Unit Tests**: Jest for all JavaScript/TypeScript
2. **Integration Tests**: Supertest for APIs
3. **E2E Tests**: Playwright for UI workflows
4. **Contract Tests**: Pact for service communication

**Test Structure:**
```
packages/
├── kg-api/
│   ├── src/
│   └── tests/
│       ├── unit/
│       ├── integration/
│       └── fixtures/
├── kg-ui/
│   ├── src/
│   └── tests/
│       ├── unit/
│       ├── integration/
│       └── e2e/
```

### 4.2 Consolidate Documentation

**Documentation Structure:**
```
docs/
├── api/              # API documentation
├── architecture/     # System architecture
├── deployment/       # Deployment guides
├── development/      # Development setup
└── user/            # User guides
```

## Implementation Priority Matrix

| Task | Impact | Effort | Priority |
|------|--------|--------|----------|
| Standardize directory structure | High | Medium | 1 |
| Consolidate configuration | High | High | 2 |
| Break down App.jsx | High | Medium | 3 |
| Eliminate API duplication | Medium | Low | 4 |
| Standardize error handling | Medium | Medium | 5 |
| Implement DI container | Medium | High | 6 |
| Standardize testing | High | High | 7 |
| Consolidate documentation | Low | Medium | 8 |

## Success Metrics

1. **Code Quality**
   - Reduce file size > 500 lines by 80%
   - Eliminate code duplication > 50 lines
   - Achieve 90%+ test coverage

2. **Developer Experience**
   - Reduce setup time from 2 hours to 30 minutes
   - Standardize development workflow
   - Improve documentation completeness

3. **Maintainability**
   - Single configuration source
   - Consistent error handling
   - Unified logging and monitoring

## Next Steps

1. **Week 1**: Start with directory restructuring
2. **Week 2**: Implement unified configuration
3. **Week 3**: Begin code quality improvements
4. **Week 4**: Complete duplication elimination
5. **Week 5**: Architecture improvements
6. **Week 6**: Service standardization
7. **Week 7**: Testing implementation
8. **Week 8**: Documentation consolidation

This plan provides a systematic approach to improving the codebase while maintaining functionality and minimizing disruption to ongoing development.
