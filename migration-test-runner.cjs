#!/usr/bin/env node

/**
 * Comprehensive Migration Testing Suite
 * 
 * Executes parallel system testing, contract testing, database comparison,
 * load testing, and integration testing to validate migration readiness.
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');
const { performance } = require('perf_hooks');

// Configuration
const LEGACY_BASE_URL = 'http://localhost:3002';
const NEW_BASE_URL = 'http://localhost:3003';
const TEST_TIMEOUT = 30000;

class MigrationTestRunner {
    constructor() {
        this.results = {
            timestamp: new Date().toISOString(),
            phase1: { completed: true, systems: { legacy: true, new: true } },
            phase2: {
                parallelTesting: { completed: false, results: {} },
                contractTesting: { completed: false, results: {} },
                databaseComparison: { completed: false, results: {} },
                loadTesting: { completed: false, results: {} },
                integrationTesting: { completed: false, results: {} }
            },
            phase3: { completed: false, assessment: {} }
        };
    }

    async runComprehensiveTests() {
        console.log('🚀 Starting Comprehensive Migration Testing Suite');
        console.log('=' * 60);

        try {
            // Phase 2: Execute all testing frameworks
            await this.executeParallelSystemTesting();
            await this.executeContractTesting();
            await this.executeDatabaseComparison();
            await this.executeLoadTesting();
            await this.executeIntegrationTesting();

            // Phase 3: Analyze results and generate assessment
            await this.generateMigrationAssessment();

            console.log('\n✅ All testing phases completed successfully');
            return this.results;

        } catch (error) {
            console.error('❌ Testing suite failed:', error.message);
            this.results.error = error.message;
            return this.results;
        }
    }

    async executeParallelSystemTesting() {
        console.log('\n📊 Phase 2.1: Parallel System Testing');
        console.log('-' * 40);

        const testCases = [
            { endpoint: '/health', method: 'GET', description: 'Health check' },
            { endpoint: '/api/health', method: 'GET', description: 'API health check' },
            { endpoint: '/api/graph/initial', method: 'GET', params: { limit: 5, legacy: 'true' }, description: 'Initial graph data' },
            { endpoint: '/api/graph/initial', method: 'GET', params: { limit: 10, legacy: 'true' }, description: 'Graph data with limit' },
            { endpoint: '/metrics', method: 'GET', description: 'Metrics endpoint' }
        ];

        const results = {
            totalTests: testCases.length,
            passed: 0,
            failed: 0,
            details: []
        };

        for (const testCase of testCases) {
            try {
                const legacyResult = await this.executeRequest(LEGACY_BASE_URL, testCase);
                const newResult = await this.executeRequest(NEW_BASE_URL, testCase);

                const comparison = this.compareResponses(legacyResult, newResult, testCase);
                results.details.push(comparison);

                if (comparison.compatible) {
                    results.passed++;
                    console.log(`✅ ${testCase.description}: Compatible`);
                } else {
                    results.failed++;
                    console.log(`❌ ${testCase.description}: Incompatible - ${comparison.reason}`);
                }

            } catch (error) {
                results.failed++;
                results.details.push({
                    test: testCase.description,
                    compatible: false,
                    reason: `Test execution failed: ${error.message}`,
                    error: error.message
                });
                console.log(`❌ ${testCase.description}: Failed - ${error.message}`);
            }
        }

        results.successRate = (results.passed / results.totalTests * 100).toFixed(2);
        this.results.phase2.parallelTesting = { completed: true, results };

        console.log(`\n📈 Parallel Testing Results: ${results.passed}/${results.totalTests} passed (${results.successRate}%)`);
    }

    async executeContractTesting() {
        console.log('\n📋 Phase 2.2: Contract Testing');
        console.log('-' * 40);

        const contracts = [
            {
                endpoint: '/api/health',
                expectedSchema: {
                    status: 'string',
                    timestamp: 'string'
                }
            },
            {
                endpoint: '/api/graph/initial',
                expectedSchema: {
                    success: 'boolean',
                    data: {
                        nodes: 'array',
                        relationships: 'array'
                    }
                }
            }
        ];

        const results = {
            totalContracts: contracts.length,
            passed: 0,
            failed: 0,
            details: []
        };

        for (const contract of contracts) {
            try {
                const response = await this.executeRequest(NEW_BASE_URL, { endpoint: contract.endpoint, method: 'GET' });
                const validation = this.validateSchema(response.data, contract.expectedSchema);

                results.details.push({
                    endpoint: contract.endpoint,
                    valid: validation.valid,
                    errors: validation.errors
                });

                if (validation.valid) {
                    results.passed++;
                    console.log(`✅ ${contract.endpoint}: Contract valid`);
                } else {
                    results.failed++;
                    console.log(`❌ ${contract.endpoint}: Contract invalid - ${validation.errors.join(', ')}`);
                }

            } catch (error) {
                results.failed++;
                results.details.push({
                    endpoint: contract.endpoint,
                    valid: false,
                    errors: [`Request failed: ${error.message}`]
                });
                console.log(`❌ ${contract.endpoint}: Failed - ${error.message}`);
            }
        }

        results.successRate = (results.passed / results.totalContracts * 100).toFixed(2);
        this.results.phase2.contractTesting = { completed: true, results };

        console.log(`\n📈 Contract Testing Results: ${results.passed}/${results.totalContracts} passed (${results.successRate}%)`);
    }

    async executeDatabaseComparison() {
        console.log('\n🗄️ Phase 2.3: Database Comparison Testing');
        console.log('-' * 40);

        const queries = [
            { description: 'Node count', endpoint: '/api/graph/initial', params: { limit: 1, legacy: 'true' } },
            { description: 'Graph structure', endpoint: '/api/graph/initial', params: { limit: 5, legacy: 'true' } }
        ];

        const results = {
            totalQueries: queries.length,
            identical: 0,
            different: 0,
            details: []
        };

        for (const query of queries) {
            try {
                const legacyResult = await this.executeRequest(LEGACY_BASE_URL, query);
                const newResult = await this.executeRequest(NEW_BASE_URL, query);

                const comparison = this.compareDataStructures(legacyResult.data, newResult.data);
                results.details.push({
                    query: query.description,
                    identical: comparison.identical,
                    differences: comparison.differences
                });

                if (comparison.identical) {
                    results.identical++;
                    console.log(`✅ ${query.description}: Data identical`);
                } else {
                    results.different++;
                    console.log(`⚠️ ${query.description}: Data differs - ${comparison.differences.length} differences`);
                }

            } catch (error) {
                results.different++;
                results.details.push({
                    query: query.description,
                    identical: false,
                    differences: [`Query failed: ${error.message}`]
                });
                console.log(`❌ ${query.description}: Failed - ${error.message}`);
            }
        }

        results.identicalRate = (results.identical / results.totalQueries * 100).toFixed(2);
        this.results.phase2.databaseComparison = { completed: true, results };

        console.log(`\n📈 Database Comparison Results: ${results.identical}/${results.totalQueries} identical (${results.identicalRate}%)`);
    }

    async executeLoadTesting() {
        console.log('\n⚡ Phase 2.4: Load Testing');
        console.log('-' * 40);

        const loadTests = [
            { concurrent: 5, requests: 20, endpoint: '/api/health' },
            { concurrent: 3, requests: 10, endpoint: '/api/graph/initial' }
        ];

        const results = {
            totalTests: loadTests.length,
            passed: 0,
            failed: 0,
            details: []
        };

        for (const test of loadTests) {
            try {
                const legacyPerf = await this.executeLoadTest(LEGACY_BASE_URL, test);
                const newPerf = await this.executeLoadTest(NEW_BASE_URL, test);

                const comparison = {
                    test: `${test.concurrent} concurrent, ${test.requests} requests to ${test.endpoint}`,
                    legacy: legacyPerf,
                    new: newPerf,
                    performanceRatio: (newPerf.averageResponseTime / legacyPerf.averageResponseTime).toFixed(2),
                    acceptable: newPerf.averageResponseTime < legacyPerf.averageResponseTime * 2 // Within 2x performance
                };

                results.details.push(comparison);

                if (comparison.acceptable) {
                    results.passed++;
                    console.log(`✅ ${test.endpoint}: Performance acceptable (${comparison.performanceRatio}x)`);
                } else {
                    results.failed++;
                    console.log(`❌ ${test.endpoint}: Performance degraded (${comparison.performanceRatio}x)`);
                }

            } catch (error) {
                results.failed++;
                results.details.push({
                    test: `${test.concurrent} concurrent, ${test.requests} requests to ${test.endpoint}`,
                    acceptable: false,
                    error: error.message
                });
                console.log(`❌ ${test.endpoint}: Load test failed - ${error.message}`);
            }
        }

        results.successRate = (results.passed / results.totalTests * 100).toFixed(2);
        this.results.phase2.loadTesting = { completed: true, results };

        console.log(`\n📈 Load Testing Results: ${results.passed}/${results.totalTests} passed (${results.successRate}%)`);
    }

    async executeIntegrationTesting() {
        console.log('\n🔗 Phase 2.5: Integration Testing');
        console.log('-' * 40);

        const integrationTests = [
            { name: 'Health to Graph Flow', steps: ['/health', '/api/graph/initial'] },
            { name: 'Metrics Collection', steps: ['/metrics', '/api/health'] }
        ];

        const results = {
            totalTests: integrationTests.length,
            passed: 0,
            failed: 0,
            details: []
        };

        for (const test of integrationTests) {
            try {
                const legacyFlow = await this.executeIntegrationFlow(LEGACY_BASE_URL, test);
                const newFlow = await this.executeIntegrationFlow(NEW_BASE_URL, test);

                const comparison = {
                    test: test.name,
                    legacySuccess: legacyFlow.success,
                    newSuccess: newFlow.success,
                    compatible: legacyFlow.success === newFlow.success,
                    legacyDuration: legacyFlow.totalDuration,
                    newDuration: newFlow.totalDuration
                };

                results.details.push(comparison);

                if (comparison.compatible && newFlow.success) {
                    results.passed++;
                    console.log(`✅ ${test.name}: Integration flow successful`);
                } else {
                    results.failed++;
                    console.log(`❌ ${test.name}: Integration flow failed`);
                }

            } catch (error) {
                results.failed++;
                results.details.push({
                    test: test.name,
                    compatible: false,
                    error: error.message
                });
                console.log(`❌ ${test.name}: Integration test failed - ${error.message}`);
            }
        }

        results.successRate = (results.passed / results.totalTests * 100).toFixed(2);
        this.results.phase2.integrationTesting = { completed: true, results };

        console.log(`\n📈 Integration Testing Results: ${results.passed}/${results.totalTests} passed (${results.successRate}%)`);
    }

    async generateMigrationAssessment() {
        console.log('\n🎯 Phase 3: Migration Readiness Assessment');
        console.log('=' * 50);

        const assessment = {
            timestamp: new Date().toISOString(),
            overallScore: 0,
            readiness: 'NOT_READY',
            recommendation: '',
            criticalIssues: [],
            warnings: [],
            followUpTasks: []
        };

        // Calculate overall score based on test results
        const weights = {
            parallelTesting: 0.3,
            contractTesting: 0.25,
            databaseComparison: 0.25,
            loadTesting: 0.15,
            integrationTesting: 0.05
        };

        let totalScore = 0;
        const phase2 = this.results.phase2;

        if (phase2.parallelTesting.completed) {
            totalScore += parseFloat(phase2.parallelTesting.results.successRate) * weights.parallelTesting;
        }
        if (phase2.contractTesting.completed) {
            totalScore += parseFloat(phase2.contractTesting.results.successRate) * weights.contractTesting;
        }
        if (phase2.databaseComparison.completed) {
            totalScore += parseFloat(phase2.databaseComparison.results.identicalRate) * weights.databaseComparison;
        }
        if (phase2.loadTesting.completed) {
            totalScore += parseFloat(phase2.loadTesting.results.successRate) * weights.loadTesting;
        }
        if (phase2.integrationTesting.completed) {
            totalScore += parseFloat(phase2.integrationTesting.results.successRate) * weights.integrationTesting;
        }

        assessment.overallScore = Math.round(totalScore);

        // Determine readiness level
        if (assessment.overallScore >= 90) {
            assessment.readiness = 'READY';
            assessment.recommendation = 'Migration can proceed. All critical tests passed with excellent compatibility.';
        } else if (assessment.overallScore >= 75) {
            assessment.readiness = 'READY_WITH_CAUTION';
            assessment.recommendation = 'Migration can proceed with careful monitoring. Address warnings before production deployment.';
        } else if (assessment.overallScore >= 60) {
            assessment.readiness = 'NOT_READY';
            assessment.recommendation = 'Migration should be delayed. Critical issues must be resolved before proceeding.';
        } else {
            assessment.readiness = 'BLOCKED';
            assessment.recommendation = 'Migration is blocked. Major compatibility issues require significant development work.';
        }

        // Identify critical issues and warnings
        this.identifyIssues(assessment);

        // Generate follow-up tasks
        this.generateFollowUpTasks(assessment);

        this.results.phase3 = { completed: true, assessment };

        // Display assessment
        console.log(`\n🎯 MIGRATION READINESS ASSESSMENT`);
        console.log(`Overall Score: ${assessment.overallScore}/100`);
        console.log(`Readiness Level: ${assessment.readiness}`);
        console.log(`Recommendation: ${assessment.recommendation}`);

        if (assessment.criticalIssues.length > 0) {
            console.log(`\n❌ Critical Issues (${assessment.criticalIssues.length}):`);
            assessment.criticalIssues.forEach((issue, i) => {
                console.log(`  ${i + 1}. ${issue}`);
            });
        }

        if (assessment.warnings.length > 0) {
            console.log(`\n⚠️ Warnings (${assessment.warnings.length}):`);
            assessment.warnings.forEach((warning, i) => {
                console.log(`  ${i + 1}. ${warning}`);
            });
        }

        if (assessment.followUpTasks.length > 0) {
            console.log(`\n📋 Follow-up Tasks (${assessment.followUpTasks.length}):`);
            assessment.followUpTasks.forEach((task, i) => {
                console.log(`  ${i + 1}. ${task}`);
            });
        }

        return assessment;
    }

    // Helper methods
    async executeRequest(baseUrl, testCase) {
        const params = testCase.params || {};
        const queryString = Object.keys(params).length > 0
            ? '?' + Object.entries(params).map(([k, v]) => `${k}=${encodeURIComponent(v)}`).join('&')
            : '';
        const fullUrl = `${baseUrl}${testCase.endpoint}${queryString}`;

        const startTime = performance.now();

        return new Promise((resolve) => {
            try {
                const urlObj = new URL(fullUrl);
                const isHttps = urlObj.protocol === 'https:';
                const client = isHttps ? https : http;

                const options = {
                    hostname: urlObj.hostname,
                    port: urlObj.port || (isHttps ? 443 : 80),
                    path: urlObj.pathname + urlObj.search,
                    method: testCase.method,
                    timeout: TEST_TIMEOUT,
                    headers: {
                        'User-Agent': 'Migration-Test-Runner/1.0'
                    }
                };

                const req = client.request(options, (res) => {
                    let data = '';
                    res.on('data', chunk => data += chunk);
                    res.on('end', () => {
                        const endTime = performance.now();
                        let parsedData = null;

                        try {
                            parsedData = JSON.parse(data);
                        } catch (e) {
                            parsedData = data;
                        }

                        resolve({
                            status: res.statusCode,
                            data: parsedData,
                            responseTime: endTime - startTime,
                            success: res.statusCode >= 200 && res.statusCode < 300
                        });
                    });
                });

                req.on('error', (error) => {
                    const endTime = performance.now();
                    resolve({
                        status: 0,
                        data: null,
                        responseTime: endTime - startTime,
                        success: false,
                        error: error.message
                    });
                });

                req.on('timeout', () => {
                    const endTime = performance.now();
                    req.destroy();
                    resolve({
                        status: 0,
                        data: null,
                        responseTime: endTime - startTime,
                        success: false,
                        error: 'Request timeout'
                    });
                });

                req.end();
            } catch (error) {
                const endTime = performance.now();
                resolve({
                    status: 0,
                    data: null,
                    responseTime: endTime - startTime,
                    success: false,
                    error: error.message
                });
            }
        });
    }

    compareResponses(legacyResult, newResult, testCase) {
        // Basic compatibility check
        if (legacyResult.success !== newResult.success) {
            return {
                compatible: false,
                reason: `Success status differs: legacy=${legacyResult.success}, new=${newResult.success}`,
                test: testCase.description
            };
        }

        // If both failed, consider compatible
        if (!legacyResult.success && !newResult.success) {
            return {
                compatible: true,
                reason: 'Both systems failed consistently',
                test: testCase.description
            };
        }

        // Check response structure for successful responses
        if (legacyResult.success && newResult.success) {
            const structureMatch = this.compareDataStructures(legacyResult.data, newResult.data);
            return {
                compatible: structureMatch.identical || structureMatch.differences.length <= 2, // Allow minor differences
                reason: structureMatch.identical ? 'Responses identical' : `${structureMatch.differences.length} structural differences`,
                test: testCase.description,
                differences: structureMatch.differences
            };
        }

        return {
            compatible: true,
            reason: 'Basic compatibility check passed',
            test: testCase.description
        };
    }

    compareDataStructures(data1, data2) {
        const differences = [];

        if (typeof data1 !== typeof data2) {
            differences.push(`Type mismatch: ${typeof data1} vs ${typeof data2}`);
            return { identical: false, differences };
        }

        if (data1 === null || data2 === null) {
            return { identical: data1 === data2, differences: data1 !== data2 ? ['Null value mismatch'] : [] };
        }

        if (typeof data1 === 'object') {
            const keys1 = Object.keys(data1);
            const keys2 = Object.keys(data2);

            // Check for missing keys
            keys1.forEach(key => {
                if (!keys2.includes(key)) {
                    differences.push(`Missing key in new system: ${key}`);
                }
            });

            keys2.forEach(key => {
                if (!keys1.includes(key)) {
                    differences.push(`Extra key in new system: ${key}`);
                }
            });

            // For arrays, compare lengths
            if (Array.isArray(data1) && Array.isArray(data2)) {
                if (data1.length !== data2.length) {
                    differences.push(`Array length differs: ${data1.length} vs ${data2.length}`);
                }
            }
        }

        return {
            identical: differences.length === 0,
            differences
        };
    }

    validateSchema(data, schema) {
        const errors = [];

        const validateObject = (obj, schemaObj, path = '') => {
            for (const [key, expectedType] of Object.entries(schemaObj)) {
                const currentPath = path ? `${path}.${key}` : key;

                if (!(key in obj)) {
                    errors.push(`Missing required field: ${currentPath}`);
                    continue;
                }

                const value = obj[key];

                if (typeof expectedType === 'string') {
                    if (expectedType === 'array' && !Array.isArray(value)) {
                        errors.push(`Field ${currentPath} should be array, got ${typeof value}`);
                    } else if (expectedType !== 'array' && typeof value !== expectedType) {
                        errors.push(`Field ${currentPath} should be ${expectedType}, got ${typeof value}`);
                    }
                } else if (typeof expectedType === 'object') {
                    if (typeof value !== 'object' || value === null) {
                        errors.push(`Field ${currentPath} should be object, got ${typeof value}`);
                    } else {
                        validateObject(value, expectedType, currentPath);
                    }
                }
            }
        };

        validateObject(data, schema);

        return {
            valid: errors.length === 0,
            errors
        };
    }

    async executeLoadTest(baseUrl, test) {
        const results = [];
        const startTime = performance.now();

        // Create concurrent requests
        const promises = [];
        for (let i = 0; i < test.requests; i++) {
            const promise = this.executeRequest(baseUrl, { endpoint: test.endpoint, method: 'GET' })
                .then(result => {
                    results.push(result);
                    return result;
                });
            promises.push(promise);

            // Add small delay between requests to simulate realistic load
            if (i % test.concurrent === 0 && i > 0) {
                await new Promise(resolve => setTimeout(resolve, 10));
            }
        }

        await Promise.all(promises);
        const endTime = performance.now();

        const successfulRequests = results.filter(r => r.success);
        const responseTimes = results.map(r => r.responseTime);

        return {
            totalRequests: test.requests,
            successfulRequests: successfulRequests.length,
            failedRequests: results.length - successfulRequests.length,
            successRate: (successfulRequests.length / results.length * 100).toFixed(2),
            averageResponseTime: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length,
            minResponseTime: Math.min(...responseTimes),
            maxResponseTime: Math.max(...responseTimes),
            totalDuration: endTime - startTime
        };
    }

    async executeIntegrationFlow(baseUrl, test) {
        const results = [];
        const startTime = performance.now();

        try {
            for (const step of test.steps) {
                const result = await this.executeRequest(baseUrl, { endpoint: step, method: 'GET' });
                results.push(result);

                if (!result.success) {
                    break; // Stop on first failure
                }
            }

            const endTime = performance.now();
            const allSuccessful = results.every(r => r.success);

            return {
                success: allSuccessful,
                steps: results.length,
                completedSteps: results.filter(r => r.success).length,
                totalDuration: endTime - startTime,
                results
            };

        } catch (error) {
            const endTime = performance.now();
            return {
                success: false,
                steps: test.steps.length,
                completedSteps: results.filter(r => r.success).length,
                totalDuration: endTime - startTime,
                error: error.message,
                results
            };
        }
    }

    identifyIssues(assessment) {
        const phase2 = this.results.phase2;

        // Check for critical issues
        if (phase2.parallelTesting.results.successRate < 80) {
            assessment.criticalIssues.push('Parallel testing success rate below 80% - major compatibility issues detected');
        }

        if (phase2.contractTesting.results.successRate < 90) {
            assessment.criticalIssues.push('Contract testing failures - API compatibility issues detected');
        }

        if (phase2.databaseComparison.results.identicalRate < 70) {
            assessment.criticalIssues.push('Database comparison shows significant data differences');
        }

        // Check for warnings
        if (phase2.loadTesting.results.successRate < 90) {
            assessment.warnings.push('Load testing shows performance degradation under concurrent load');
        }

        if (phase2.integrationTesting.results.successRate < 100) {
            assessment.warnings.push('Integration testing shows workflow compatibility issues');
        }

        // Check individual test failures
        phase2.parallelTesting.results.details.forEach(detail => {
            if (!detail.compatible) {
                assessment.warnings.push(`Endpoint compatibility issue: ${detail.test} - ${detail.reason}`);
            }
        });
    }

    generateFollowUpTasks(assessment) {
        const phase2 = this.results.phase2;

        if (assessment.criticalIssues.length > 0) {
            assessment.followUpTasks.push('Resolve all critical compatibility issues before proceeding with migration');
        }

        if (phase2.contractTesting.results.failed > 0) {
            assessment.followUpTasks.push('Update API contracts and ensure backward compatibility');
        }

        if (phase2.loadTesting.results.failed > 0) {
            assessment.followUpTasks.push('Optimize performance bottlenecks identified in load testing');
        }

        if (phase2.databaseComparison.results.different > 0) {
            assessment.followUpTasks.push('Investigate and resolve database query result differences');
        }

        assessment.followUpTasks.push('Implement comprehensive monitoring for production migration');
        assessment.followUpTasks.push('Prepare rollback procedures and emergency response plan');
        assessment.followUpTasks.push('Schedule user acceptance testing with key stakeholders');
    }
}

// Main execution
async function main() {
    const runner = new MigrationTestRunner();
    const results = await runner.runComprehensiveTests();

    // Save results to file
    const fs = require('fs');
    const resultsFile = `migration-test-results-${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
    fs.writeFileSync(resultsFile, JSON.stringify(results, null, 2));

    console.log(`\n💾 Results saved to: ${resultsFile}`);

    // Exit with appropriate code
    const assessment = results.phase3?.assessment;
    if (assessment?.readiness === 'READY' || assessment?.readiness === 'READY_WITH_CAUTION') {
        process.exit(0);
    } else {
        process.exit(1);
    }
}

if (require.main === module) {
    main().catch(error => {
        console.error('❌ Test runner failed:', error);
        process.exit(1);
    });
}

module.exports = MigrationTestRunner;
