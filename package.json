{"dependencies": {"@anthropic-ai/sdk": "^0.39.0", "boxen": "^8.0.1", "chalk": "^4.1.2", "cli-table3": "^0.6.5", "commander": "^11.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "fastmcp": "^1.20.5", "figlet": "^1.8.0", "fuse.js": "^7.0.0", "gradient-string": "^3.0.0", "helmet": "^8.1.0", "inquirer": "^12.5.0", "jsonwebtoken": "^9.0.2", "lru-cache": "^10.2.0", "openai": "^4.89.0", "ora": "^8.2.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "swagger-ui-express": "^5.0.1", "yamljs": "^0.3.0", "axios": "^1.6.0", "neo4j-driver": "^5.15.0", "ts-node": "^10.9.0"}, "devDependencies": {"identity-obj-proxy": "^3.0.0", "jest-environment-jsdom": "^30.0.2", "kill-port": "^2.0.1"}, "scripts": {"dev": "node scripts/dev.js", "list": "node scripts/dev.js list", "generate": "node scripts/dev.js generate", "parse-prd": "node scripts/dev.js parse-prd", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:integration": "ts-node packages/shared/src/testing/integration-test-suite.ts", "test:validate": "ts-node packages/shared/src/testing/run-all-tests.ts", "test:parallel": "ts-node packages/shared/src/testing/run-parallel-tests.ts", "test:parallel:api": "ts-node packages/shared/src/testing/run-parallel-tests.ts api", "test:parallel:database": "ts-node packages/shared/src/testing/run-parallel-tests.ts database", "test:parallel:smoke": "ts-node packages/shared/src/testing/run-parallel-tests.ts smoke", "test:parallel:load": "ts-node packages/shared/src/testing/run-parallel-tests.ts load", "test:parallel:readiness": "ts-node packages/shared/src/testing/run-parallel-tests.ts readiness", "test:contracts": "ts-node packages/shared/src/testing/contract-test-runner.ts", "test:contracts:critical": "ts-node packages/shared/src/testing/contract-test-runner.ts critical", "test:contracts:performance": "ts-node packages/shared/src/testing/contract-test-runner.ts performance", "test:contracts:compatibility": "ts-node packages/shared/src/testing/contract-test-runner.ts compatibility", "test:contracts:docs": "ts-node packages/shared/src/testing/contract-test-runner.ts docs", "test:integration:fast": "ts-node packages/shared/src/testing/integration-test-suite.ts --no-database --no-load", "test:load": "ts-node packages/shared/src/testing/load-test-runner.ts", "test:load:light": "ts-node packages/shared/src/testing/load-test-runner.ts light", "test:load:medium": "ts-node packages/shared/src/testing/load-test-runner.ts medium", "test:load:heavy": "ts-node packages/shared/src/testing/load-test-runner.ts heavy", "test:load:migration": "ts-node packages/shared/src/testing/load-test-runner.ts migration", "test:load:progressive": "ts-node packages/shared/src/testing/load-test-runner.ts progressive", "test:load:stress": "ts-node packages/shared/src/testing/load-test-runner.ts stress", "test:load:dry-run": "ts-node packages/shared/src/testing/load-test-runner.ts dry-run", "test:validate:quick": "ts-node packages/shared/src/testing/run-all-tests.ts quick"}, "type": "module"}