/**
 * Controller Factory
 * 
 * Implements the Strangler Fig pattern by gradually replacing legacy
 * route handlers with new controllers based on feature flags.
 */

import { Router } from 'express';
import { FeatureFlags, logger } from '@kg-visualizer/shared';
import { GraphController, GraphService } from './GraphController';
import { AnalysisController, AnalysisService } from './AnalysisController';
import { HealthController, HealthService } from './HealthController';
import { FeatureFlagsController, FeatureFlagsService } from './FeatureFlagsController';
import { CompatibilityRouter } from '../routes/CompatibilityRouter';
import { CompatibilityService } from '../services/CompatibilityService';

export interface ControllerDependencies {
  graphService: GraphService;
  analysisService: AnalysisService;
  healthService: HealthService;
  featureFlagsService: FeatureFlagsService;
  compatibilityService: CompatibilityService;
  featureFlags: FeatureFlags;
  legacyApiUrl?: string;
}

export interface RouteRegistration {
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  controller: string;
  action: string;
  legacyEndpoint: string;
  enableDualExecution?: boolean;
  enableFallback?: boolean;
  requiresAuth?: boolean;
  rateLimit?: number;
}

export class ControllerFactory {
  private readonly logger = logger.child('controller-factory');
  private readonly controllers: Map<string, any> = new Map();
  private readonly compatibilityRouter: CompatibilityRouter;

  constructor(private dependencies: ControllerDependencies) {
    this.initializeControllers();
    this.compatibilityRouter = new CompatibilityRouter(
      dependencies.compatibilityService,
      dependencies.featureFlags,
      dependencies.legacyApiUrl
    );
  }

  /**
   * Initialize all controllers
   */
  private initializeControllers(): void {
    const {
      graphService,
      analysisService,
      healthService,
      featureFlagsService,
      featureFlags
    } = this.dependencies;

    // Initialize controllers
    this.controllers.set('graph', new GraphController(graphService));
    this.controllers.set('analysis', new AnalysisController(analysisService));
    this.controllers.set('health', new HealthController(healthService, featureFlags));
    this.controllers.set('featureFlags', new FeatureFlagsController(featureFlagsService, featureFlags));

    this.logger.info('Controllers initialized', 'controller-factory', {
      controllerCount: this.controllers.size,
      controllers: Array.from(this.controllers.keys())
    });
  }

  /**
   * Create router with Strangler Fig pattern
   */
  createRouter(): Router {
    const router = Router();

    // Register routes based on feature flags
    this.registerGraphRoutes(router);
    this.registerAnalysisRoutes(router);
    this.registerHealthRoutes(router);
    this.registerFeatureFlagRoutes(router);

    this.logger.info('Router created with Strangler Fig pattern', 'controller-factory');
    return router;
  }

  /**
   * Register graph routes
   */
  private registerGraphRoutes(router: Router): void {
    // const graphController = this.controllers.get('graph') as GraphController;

    if (this.dependencies.featureFlags.NEW_CONTROLLER_LAYER) {
      // Use new controllers
      this.registerRoute(router, {
        path: '/graph/initial',
        method: 'GET',
        controller: 'graph',
        action: 'getInitialGraph',
        legacyEndpoint: '/api/graph/initial',
        enableDualExecution: this.dependencies.featureFlags.DUAL_EXECUTION_MODE,
        enableFallback: true
      });

      this.registerRoute(router, {
        path: '/graph/search',
        method: 'GET',
        controller: 'graph',
        action: 'searchNodes',
        legacyEndpoint: '/api/graph/search',
        enableDualExecution: this.dependencies.featureFlags.DUAL_EXECUTION_MODE,
        enableFallback: true
      });

      this.registerRoute(router, {
        path: '/graph/expand',
        method: 'GET',
        controller: 'graph',
        action: 'expandNode',
        legacyEndpoint: '/api/graph/expand',
        enableDualExecution: this.dependencies.featureFlags.DUAL_EXECUTION_MODE,
        enableFallback: true
      });

      this.registerRoute(router, {
        path: '/graph/filter',
        method: 'POST',
        controller: 'graph',
        action: 'filterGraph',
        legacyEndpoint: '/api/graph/filter',
        enableDualExecution: this.dependencies.featureFlags.DUAL_EXECUTION_MODE,
        enableFallback: true
      });

      this.registerRoute(router, {
        path: '/graph/query',
        method: 'POST',
        controller: 'graph',
        action: 'executeQuery',
        legacyEndpoint: '/api/graph/query',
        enableDualExecution: this.dependencies.featureFlags.DUAL_EXECUTION_MODE,
        enableFallback: true
      });

      this.registerRoute(router, {
        path: '/graph/metadata',
        method: 'GET',
        controller: 'graph',
        action: 'getMetadata',
        legacyEndpoint: '/api/metadata',
        enableDualExecution: this.dependencies.featureFlags.DUAL_EXECUTION_MODE,
        enableFallback: true
      });

      this.logger.info('Graph routes registered with new controllers', 'controller-factory');
    } else {
      // Use compatibility router for legacy behavior
      this.compatibilityRouter.registerLegacyRoutes();
      router.use('/graph', this.compatibilityRouter.getRouter());
      
      this.logger.info('Graph routes registered with compatibility layer', 'controller-factory');
    }
  }

  /**
   * Register analysis routes
   */
  private registerAnalysisRoutes(router: Router): void {
    const analysisController = this.controllers.get('analysis') as AnalysisController;

    if (this.dependencies.featureFlags.NEW_SERVICE_LAYER) {
      this.registerRoute(router, {
        path: '/analysis/centrality',
        method: 'GET',
        controller: 'analysis',
        action: 'getCentrality',
        legacyEndpoint: '/api/analysis/centrality',
        enableDualExecution: this.dependencies.featureFlags.DUAL_EXECUTION_MODE,
        enableFallback: true
      });

      this.registerRoute(router, {
        path: '/analysis/clusters',
        method: 'GET',
        controller: 'analysis',
        action: 'getClusters',
        legacyEndpoint: '/api/analysis/clusters',
        enableDualExecution: this.dependencies.featureFlags.DUAL_EXECUTION_MODE,
        enableFallback: true
      });

      this.registerRoute(router, {
        path: '/analysis/hidden-links',
        method: 'GET',
        controller: 'analysis',
        action: 'predictLinks',
        legacyEndpoint: '/api/analysis/hidden-links',
        enableDualExecution: this.dependencies.featureFlags.DUAL_EXECUTION_MODE,
        enableFallback: true
      });

      router.get('/analysis/paths', analysisController.findPaths);
      router.get('/analysis/statistics', analysisController.getStatistics);
      router.get('/analysis/similarity', analysisController.getSimilarity);
      router.post('/analysis/batch', analysisController.runBatchAnalysis);

      this.logger.info('Analysis routes registered with new controllers', 'controller-factory');
    } else {
      // Register compatibility routes for analysis
      this.compatibilityRouter.registerRoute({
        path: '/centrality',
        method: 'GET',
        legacyEndpoint: '/api/analysis/centrality'
      });

      this.compatibilityRouter.registerRoute({
        path: '/clusters',
        method: 'GET',
        legacyEndpoint: '/api/analysis/clusters'
      });

      this.compatibilityRouter.registerRoute({
        path: '/hidden-links',
        method: 'GET',
        legacyEndpoint: '/api/analysis/hidden-links'
      });

      router.use('/analysis', this.compatibilityRouter.getRouter());
      
      this.logger.info('Analysis routes registered with compatibility layer', 'controller-factory');
    }
  }

  /**
   * Register health routes
   */
  private registerHealthRoutes(router: Router): void {
    const healthController = this.controllers.get('health') as HealthController;

    // Health routes are always new (no legacy equivalent)
    router.get('/health', healthController.getHealth);
    router.get('/health/detailed', healthController.getDetailedHealth);
    router.get('/health/ready', healthController.getReadiness);
    router.get('/health/live', healthController.getLiveness);
    router.get('/health/metrics', healthController.getMetrics);

    this.logger.info('Health routes registered', 'controller-factory');
  }

  /**
   * Register feature flag routes
   */
  private registerFeatureFlagRoutes(router: Router): void {
    const featureFlagsController = this.controllers.get('featureFlags') as FeatureFlagsController;

    // Feature flag routes are always new
    router.get('/feature-flags', featureFlagsController.getFlags);
    router.post('/feature-flags/update', featureFlagsController.updateFlag);
    router.post('/feature-flags/batch-update', featureFlagsController.batchUpdateFlags);
    router.post('/feature-flags/reset', featureFlagsController.resetFlags);
    router.get('/feature-flags/history', featureFlagsController.getFlagHistory);
    router.get('/feature-flags/migration-status', featureFlagsController.getMigrationStatus);

    this.logger.info('Feature flag routes registered', 'controller-factory');
  }

  /**
   * Register a route with compatibility handling
   */
  private registerRoute(router: Router, registration: RouteRegistration): void {
    const controller = this.controllers.get(registration.controller);
    if (!controller) {
      throw new Error(`Controller '${registration.controller}' not found`);
    }

    const handler = controller[registration.action];
    if (!handler) {
      throw new Error(`Action '${registration.action}' not found on controller '${registration.controller}'`);
    }

    // Register with compatibility router if dual execution is enabled
    if (registration.enableDualExecution) {
      this.compatibilityRouter.registerRoute({
        path: registration.path,
        method: registration.method,
        legacyEndpoint: registration.legacyEndpoint,
        newHandler: handler,
        enableDualExecution: registration.enableDualExecution,
        enableFallback: registration.enableFallback
      });

      router.use(registration.path, this.compatibilityRouter.getRouter());
    } else {
      // Register directly
      switch (registration.method) {
        case 'GET':
          router.get(registration.path, handler);
          break;
        case 'POST':
          router.post(registration.path, handler);
          break;
        case 'PUT':
          router.put(registration.path, handler);
          break;
        case 'DELETE':
          router.delete(registration.path, handler);
          break;
      }
    }

    this.logger.debug('Route registered', 'controller-factory', {
      path: registration.path,
      method: registration.method,
      controller: registration.controller,
      action: registration.action,
      dualExecution: registration.enableDualExecution
    });
  }

  /**
   * Get controller instance
   */
  getController(name: string): any {
    return this.controllers.get(name);
  }

  /**
   * Get migration status
   */
  getMigrationStatus(): {
    phase: string;
    controllersEnabled: string[];
    routesRegistered: number;
    dualExecutionEnabled: boolean;
  } {
    const controllersEnabled: string[] = [];
    
    if (this.dependencies.featureFlags.NEW_CONTROLLER_LAYER) {
      controllersEnabled.push('graph');
    }
    
    if (this.dependencies.featureFlags.NEW_SERVICE_LAYER) {
      controllersEnabled.push('analysis');
    }

    controllersEnabled.push('health', 'featureFlags');

    return {
      phase: this.determineMigrationPhase(),
      controllersEnabled,
      routesRegistered: this.controllers.size,
      dualExecutionEnabled: this.dependencies.featureFlags.DUAL_EXECUTION_MODE
    };
  }

  /**
   * Determine current migration phase
   */
  private determineMigrationPhase(): string {
    const flags = this.dependencies.featureFlags;
    
    if (!flags.NEW_CONTROLLER_LAYER && !flags.NEW_SERVICE_LAYER) {
      return 'legacy';
    }
    
    if (flags.NEW_CONTROLLER_LAYER && flags.NEW_SERVICE_LAYER && !flags.DUAL_EXECUTION_MODE) {
      return 'complete';
    }
    
    return 'migration';
  }
}
