/**
 * Analysis Controller
 * 
 * Handles graph analysis endpoints including centrality measures,
 * community detection, path finding, and link prediction.
 */

import { Request, Response } from 'express';
import { BaseController } from './BaseController';

export interface AnalysisService {
  getCentrality(type: CentralityType, limit?: number): Promise<CentralityResult[]>;
  getClusters(algorithm?: ClusteringAlgorithm, limit?: number): Promise<ClusterResult[]>;
  predictLinks(topN?: number, threshold?: number): Promise<LinkPrediction[]>;
  findPaths(startNodeId: string, endNodeId: string, maxDepth?: number): Promise<PathResult[]>;
  getStatistics(): Promise<GraphStatistics>;
  getSimilarity(nodeId: string, limit?: number): Promise<SimilarityResult[]>;
}

export type CentralityType = 'degree' | 'betweenness' | 'closeness' | 'pagerank' | 'eigenvector';
export type ClusteringAlgorithm = 'louvain' | 'leiden' | 'label_propagation' | 'weakly_connected';

export interface CentralityResult {
  nodeId: string;
  nodeName: string;
  score: number;
  rank: number;
  properties?: Record<string, any>;
}

export interface ClusterResult {
  clusterId: string;
  nodes: Array<{
    nodeId: string;
    nodeName: string;
    properties?: Record<string, any>;
  }>;
  size: number;
  density: number;
  modularity?: number;
}

export interface LinkPrediction {
  sourceNodeId: string;
  targetNodeId: string;
  sourceName: string;
  targetName: string;
  score: number;
  confidence: number;
  reasoning?: string;
}

export interface PathResult {
  path: Array<{
    nodeId: string;
    nodeName: string;
    relationshipType?: string;
  }>;
  length: number;
  weight?: number;
  cost?: number;
}

export interface GraphStatistics {
  nodeCount: number;
  relationshipCount: number;
  density: number;
  diameter: number;
  averagePathLength: number;
  clusteringCoefficient: number;
  connectedComponents: number;
  stronglyConnectedComponents: number;
  nodeTypeDistribution: Record<string, number>;
  relationshipTypeDistribution: Record<string, number>;
  degreeDistribution: {
    min: number;
    max: number;
    mean: number;
    median: number;
    standardDeviation: number;
  };
}

export interface SimilarityResult {
  nodeId: string;
  nodeName: string;
  similarity: number;
  commonNeighbors: number;
  sharedProperties: string[];
}

export class AnalysisController extends BaseController {
  constructor(private analysisService: AnalysisService) {
    super();
  }

  /**
   * GET /api/analysis/centrality
   * Calculate node centrality metrics
   */
  getCentrality = this.asyncHandler(async (req: Request, res: Response) => {
    const type = (req.query.type as CentralityType) || 'degree';
    const { limit } = this.getPaginationParams(req);

    // Validate centrality type
    const validTypes: CentralityType[] = ['degree', 'betweenness', 'closeness', 'pagerank', 'eigenvector'];
    if (!validTypes.includes(type)) {
      return this.sendError(res, `Invalid centrality type. Must be one of: ${validTypes.join(', ')}`, 400);
    }

    this.logAction('getCentrality', req, { type, limit });

    const { result: centrality, duration } = await this.withTiming(
      () => this.analysisService.getCentrality(type, limit),
      'getCentrality',
      req
    );

    // Set cache headers for centrality data (15 minutes)
    this.setCacheHeaders(res, 900);

    this.sendSuccess(res, centrality, 200, {
      executionTime: duration,
      centralityType: type,
      resultCount: centrality.length
    });
  });

  /**
   * GET /api/analysis/clusters
   * Perform community detection
   */
  getClusters = this.asyncHandler(async (req: Request, res: Response) => {
    const algorithm = (req.query.algorithm as ClusteringAlgorithm) || 'louvain';
    const { limit } = this.getPaginationParams(req);

    // Validate clustering algorithm
    const validAlgorithms: ClusteringAlgorithm[] = ['louvain', 'leiden', 'label_propagation', 'weakly_connected'];
    if (!validAlgorithms.includes(algorithm)) {
      return this.sendError(res, `Invalid clustering algorithm. Must be one of: ${validAlgorithms.join(', ')}`, 400);
    }

    this.logAction('getClusters', req, { algorithm, limit });

    const { result: clusters, duration } = await this.withTiming(
      () => this.analysisService.getClusters(algorithm, limit),
      'getClusters',
      req
    );

    // Set cache headers for cluster data (30 minutes)
    this.setCacheHeaders(res, 1800);

    this.sendSuccess(res, clusters, 200, {
      executionTime: duration,
      algorithm,
      clusterCount: clusters.length,
      totalNodes: clusters.reduce((sum, cluster) => sum + cluster.size, 0)
    });
  });

  /**
   * GET /api/analysis/hidden-links
   * Predict potential links using machine learning
   */
  predictLinks = this.asyncHandler(async (req: Request, res: Response) => {
    const topN = Math.min(parseInt(req.query.topN as string) || 20, 100);
    const threshold = Math.max(Math.min(parseFloat(req.query.threshold as string) || 0.4, 1.0), 0.0);

    this.logAction('predictLinks', req, { topN, threshold });

    const { result: predictions, duration } = await this.withTiming(
      () => this.analysisService.predictLinks(topN, threshold),
      'predictLinks',
      req
    );

    // Set cache headers for link predictions (5 minutes due to computational cost)
    this.setCacheHeaders(res, 300);

    this.sendSuccess(res, predictions, 200, {
      executionTime: duration,
      predictionCount: predictions.length,
      threshold,
      topN
    });
  });

  /**
   * GET /api/analysis/paths
   * Find paths between two nodes
   */
  findPaths = this.asyncHandler(async (req: Request, res: Response) => {
    const { startNodeId, endNodeId } = req.query;
    const maxDepth = Math.min(parseInt(req.query.maxDepth as string) || 5, 10);

    const validation = this.validateRequired(
      { startNodeId, endNodeId },
      ['startNodeId', 'endNodeId']
    );

    if (!validation.isValid) {
      return this.sendError(res, 'Start and end node IDs are required', 400, {
        missing: validation.missing
      });
    }

    this.logAction('findPaths', req, { 
      startNodeId, 
      endNodeId, 
      maxDepth 
    });

    const { result: paths, duration } = await this.withTiming(
      () => this.analysisService.findPaths(startNodeId as string, endNodeId as string, maxDepth),
      'findPaths',
      req
    );

    this.sendSuccess(res, paths, 200, {
      executionTime: duration,
      pathCount: paths.length,
      startNodeId,
      endNodeId,
      maxDepth
    });
  });

  /**
   * GET /api/analysis/statistics
   * Get comprehensive graph statistics
   */
  getStatistics = this.asyncHandler(async (req: Request, res: Response) => {
    this.logAction('getStatistics', req);

    const { result: statistics, duration } = await this.withTiming(
      () => this.analysisService.getStatistics(),
      'getStatistics',
      req
    );

    // Set cache headers for statistics (1 hour)
    this.setCacheHeaders(res, 3600);

    this.sendSuccess(res, statistics, 200, {
      executionTime: duration
    });
  });

  /**
   * GET /api/analysis/similarity
   * Find similar nodes to a given node
   */
  getSimilarity = this.asyncHandler(async (req: Request, res: Response) => {
    const { nodeId } = req.query;
    const { limit } = this.getPaginationParams(req);

    if (!nodeId || typeof nodeId !== 'string') {
      return this.sendError(res, 'Node ID is required', 400);
    }

    this.logAction('getSimilarity', req, { nodeId, limit });

    const { result: similarities, duration } = await this.withTiming(
      () => this.analysisService.getSimilarity(nodeId, limit),
      'getSimilarity',
      req
    );

    this.sendSuccess(res, similarities, 200, {
      executionTime: duration,
      referenceNodeId: nodeId,
      resultCount: similarities.length
    });
  });

  /**
   * POST /api/analysis/batch
   * Run multiple analysis operations in batch
   */
  runBatchAnalysis = this.asyncHandler(async (req: Request, res: Response) => {
    const { operations } = req.body;

    if (!Array.isArray(operations) || operations.length === 0) {
      return this.sendError(res, 'Operations array is required', 400);
    }

    if (operations.length > 10) {
      return this.sendError(res, 'Maximum 10 operations allowed per batch', 400);
    }

    this.logAction('runBatchAnalysis', req, { 
      operationCount: operations.length 
    });

    const startTime = Date.now();
    const results: Array<{ operation: string; result?: any; error?: string }> = [];

    for (const operation of operations) {
      try {
        let result;
        
        switch (operation.type) {
          case 'centrality':
            result = await this.analysisService.getCentrality(
              operation.centralityType || 'degree',
              operation.limit
            );
            break;
          case 'clusters':
            result = await this.analysisService.getClusters(
              operation.algorithm || 'louvain',
              operation.limit
            );
            break;
          case 'statistics':
            result = await this.analysisService.getStatistics();
            break;
          default:
            throw new Error(`Unknown operation type: ${operation.type}`);
        }

        results.push({ operation: operation.type, result });
      } catch (error) {
        results.push({ 
          operation: operation.type, 
          error: (error as Error).message 
        });
      }
    }

    const duration = Date.now() - startTime;

    this.sendSuccess(res, results, 200, {
      executionTime: duration,
      operationCount: operations.length,
      successCount: results.filter(r => !r.error).length,
      errorCount: results.filter(r => r.error).length
    });
  });
}
