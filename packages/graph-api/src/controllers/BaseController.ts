/**
 * Base Controller
 * 
 * Abstract base class for all controllers providing common functionality
 * like error handling, response formatting, and request validation.
 */

import { Request, Response, NextFunction } from 'express';
import { logger, ApiResponse, ApiError } from '@kg-visualizer/shared';

export abstract class BaseController {
  protected readonly logger = logger.child('controller');

  /**
   * Wrap controller methods with error handling
   */
  protected asyncHandler = (
    fn: (req: Request, res: Response, next: NextFunction) => Promise<void>
  ) => {
    return (req: Request, res: Response, next: NextFunction): void => {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  };

  /**
   * Send successful response
   */
  protected sendSuccess<T>(
    res: Response,
    data: T,
    statusCode: number = 200,
    metadata?: Record<string, any>
  ): void {
    const response: ApiResponse<T> = {
      success: true,
      data,
      timestamp: new Date().toISOString(),
      requestId: res.locals.requestId
    };

    if (metadata) {
      response.metadata = metadata;
    }

    res.status(statusCode).json(response);
  }

  /**
   * Send error response
   */
  protected sendError(
    res: Response,
    error: string | Error,
    statusCode: number = 500,
    details?: Record<string, any>
  ): void {
    const errorMessage = error instanceof Error ? error.message : error;
    
    const response: ApiError = {
      code: this.getErrorCode(statusCode),
      message: errorMessage,
      statusCode,
      timestamp: new Date().toISOString(),
      requestId: res.locals.requestId
    };

    if (details) {
      response.details = details;
    }

    this.logger.error('Controller error', 'controller', {
      error: errorMessage,
      statusCode,
      requestId: res.locals.requestId,
      details
    });

    res.status(statusCode).json(response);
  }

  /**
   * Validate required parameters
   */
  protected validateRequired(
    params: Record<string, any>,
    required: string[]
  ): { isValid: boolean; missing: string[] } {
    const missing = required.filter(key => 
      params[key] === undefined || params[key] === null || params[key] === ''
    );

    return {
      isValid: missing.length === 0,
      missing
    };
  }

  /**
   * Extract pagination parameters
   */
  protected getPaginationParams(req: Request): {
    limit: number;
    offset: number;
    page: number;
  } {
    const limit = Math.floor(Math.min(parseInt(req.query.limit as string) || 20, 100));
    const page = Math.floor(Math.max(parseInt(req.query.page as string) || 1, 1));
    const offset = Math.floor((page - 1) * limit);

    return { limit, offset, page };
  }

  /**
   * Extract sorting parameters
   */
  protected getSortParams(req: Request): {
    sortBy?: string;
    sortOrder: 'asc' | 'desc';
  } {
    const sortBy = req.query.sortBy as string;
    const sortOrder = (req.query.sortOrder as string)?.toLowerCase() === 'desc' ? 'desc' : 'asc';

    return { sortBy, sortOrder };
  }

  /**
   * Log controller action
   */
  protected logAction(
    action: string,
    req: Request,
    metadata?: Record<string, any>
  ): void {
    this.logger.info(`Controller action: ${action}`, 'controller', {
      path: req.path,
      method: req.method,
      requestId: req.headers['x-request-id'] as string,
      userId: req.headers['x-user-id'],
      ...metadata
    });
  }

  /**
   * Get error code from status code
   */
  private getErrorCode(statusCode: number): string {
    switch (statusCode) {
      case 400:
        return 'VALIDATION_ERROR';
      case 401:
        return 'AUTHENTICATION_ERROR';
      case 403:
        return 'AUTHORIZATION_ERROR';
      case 404:
        return 'NOT_FOUND_ERROR';
      case 409:
        return 'CONFLICT_ERROR';
      case 429:
        return 'RATE_LIMIT_ERROR';
      case 500:
        return 'INTERNAL_SERVER_ERROR';
      case 503:
        return 'SERVICE_UNAVAILABLE';
      default:
        return 'UNKNOWN_ERROR';
    }
  }

  /**
   * Handle async operations with timing
   */
  protected async withTiming<T>(
    operation: () => Promise<T>,
    operationName: string,
    req: Request
  ): Promise<{ result: T; duration: number }> {
    const startTime = Date.now();
    
    try {
      const result = await operation();
      const duration = Date.now() - startTime;

      this.logger.debug(`Operation completed: ${operationName}`, 'controller', {
        duration,
        requestId: req.headers['x-request-id'] as string,
        path: req.path
      });

      return { result, duration };
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.logger.error(`Operation failed: ${operationName}`, 'controller', {
        duration,
        error: (error as Error).message,
        requestId: req.headers['x-request-id'] as string,
        path: req.path
      });

      throw error;
    }
  }

  /**
   * Validate request body against schema
   */
  protected validateRequestBody<T>(
    body: any,
    validator: (data: any) => { isValid: boolean; errors: string[] }
  ): { isValid: boolean; errors: string[]; data?: T } {
    const validation = validator(body);
    
    if (validation.isValid) {
      return { isValid: true, errors: [], data: body as T };
    }

    return { isValid: false, errors: validation.errors };
  }

  /**
   * Check if request is from legacy client
   */
  protected isLegacyRequest(req: Request): boolean {
    return !!(req.headers['x-legacy-api'] === 'true' ||
           req.query.legacy === 'true' ||
           (req.headers['user-agent'] && req.headers['user-agent'].includes('legacy')));
  }

  /**
   * Get client information
   */
  protected getClientInfo(req: Request): {
    ip: string;
    userAgent?: string;
    userId?: string;
    sessionId?: string;
  } {
    return {
      ip: req.ip || req.connection.remoteAddress || 'unknown',
      userAgent: req.headers['user-agent'],
      userId: req.headers['x-user-id'] as string,
      sessionId: req.headers['x-session-id'] as string
    };
  }

  /**
   * Set cache headers
   */
  protected setCacheHeaders(
    res: Response,
    maxAge: number,
    isPublic: boolean = true
  ): void {
    const cacheControl = isPublic ? 'public' : 'private';
    res.setHeader('Cache-Control', `${cacheControl}, max-age=${maxAge}`);
    res.setHeader('ETag', `"${Date.now()}"`);
  }

  /**
   * Handle conditional requests (304 Not Modified)
   */
  protected handleConditionalRequest(
    req: Request,
    res: Response,
    lastModified: Date
  ): boolean {
    const ifModifiedSince = req.headers['if-modified-since'];
    
    if (ifModifiedSince) {
      const clientDate = new Date(ifModifiedSince);
      if (lastModified <= clientDate) {
        res.status(304).end();
        return true;
      }
    }

    res.setHeader('Last-Modified', lastModified.toUTCString());
    return false;
  }
}
