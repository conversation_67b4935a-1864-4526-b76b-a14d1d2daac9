/**
 * Middleware Index
 * 
 * Central export point for all middleware components.
 */

// Authentication middleware
export {
  AuthenticationMiddleware
} from './AuthenticationMiddleware';

export type {
  AuthenticatedUser,
  AuthenticationConfig
} from './AuthenticationMiddleware';

// Validation middleware
export {
  ValidationMiddleware,
  CommonSchemas,
  validationMiddleware,
  validatePagination,
  validateGraphQuery,
  validateSearch,
  validateNode,
  validateRelationship,
  validateAnalysis,
  validateClustering,
  validatePathFinding,
  validateFilters,
  validateChat,
  validateId,
  validateUuid,
  validateHeaders,
  createCustomValidation
} from './ValidationMiddleware';

export type {
  ValidationConfig,
  ValidationSchemas,
  ValidationError
} from './ValidationMiddleware';

// Error handling middleware
export {
  ErrorHandlingMiddleware,
  CustomError,
  ValidationError as ValidationErrorClass,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  DatabaseError,
  ExternalServiceError,
  TimeoutError,
  errorHandlingMiddleware,
  handleError,
  asyncHandler,
  notFound,
  timeout,
  recovery,
  createValidationError,
  createAuthenticationError,
  createAuthorizationError,
  createNotFoundError,
  createConflictError,
  createRateLimitError,
  createDatabaseError,
  createExternalServiceError,
  createTimeoutError,
  errorHandlingHealthCheck
} from './ErrorHandlingMiddleware';

export type {
  ErrorHandlingConfig,
  ErrorResponse
} from './ErrorHandlingMiddleware';

// Logging middleware
export {
  LoggingMiddleware,
  loggingMiddleware,
  logRequests,
  logPerformance,
  logErrors,
  detectSlowRequests,
  createProductionLogging,
  createDevelopmentLogging,
  createDebugLogging
} from './LoggingMiddleware';

export type {
  LoggingConfig,
  RequestLog,
  ResponseLog,
  PerformanceLog
} from './LoggingMiddleware';

// Security middleware
export {
  SecurityMiddleware,
  securityMiddleware,
  configureCors,
  configureRateLimit,
  configureSecurityHeaders,
  configureSanitization,
  configureTrustedProxy,
  configureSecurityAudit,
  configureCspReporting,
  createProductionSecurity,
  createDevelopmentSecurity
} from './SecurityMiddleware';

export type {
  SecurityConfig,
  RateLimitInfo
} from './SecurityMiddleware';

// Compatibility middleware
export {
  legacyResponseTransformer,
  legacyRequestTransformer,
  compatibilityHeaders,
  compatibilityErrorHandler
} from './compatibility';

// Middleware factory
export {
  MiddlewareFactory
} from './MiddlewareFactory';

export type {
  MiddlewareConfig,
  MiddlewareInstances,
  MiddlewareHealth
} from './MiddlewareFactory';

// Convenience function to create a complete middleware stack
import { MiddlewareFactory, type MiddlewareConfig } from './MiddlewareFactory';
import { FeatureFlags } from '@kg-visualizer/shared';

export const createMiddlewareStack = (
  config?: Partial<MiddlewareConfig>,
  featureFlags?: FeatureFlags
) => {
  const defaultFeatureFlags: FeatureFlags = {
    NEW_CONTROLLER_LAYER: true,
    NEW_SERVICE_LAYER: true,
    NEW_REPOSITORY_PATTERN: true,
    NEW_MIDDLEWARE_STACK: true,
    NEW_CHAT_SERVICE: true,
    CIRCUIT_BREAKER_ENABLED: true,
    DUAL_EXECUTION_MODE: false,
    PERFORMANCE_MONITORING: true,
    AUTOMATIC_ROLLBACK: true,
    TRAFFIC_PERCENTAGE_NEW_API: 100,
    DEBUG_MODE: false,
    VERBOSE_LOGGING: false,
    MIGRATION_METRICS: true
  };

  return new MiddlewareFactory(config, featureFlags || defaultFeatureFlags);
};

// Pre-configured middleware stacks for different environments
export const createProductionMiddleware = (featureFlags?: FeatureFlags) => {
  return createMiddlewareStack({
    environment: 'production',
    enableCompression: true,
    enableFeatureFlags: true,
    enableCompatibility: true,
    enableHealthChecks: true,
    authentication: {
      enableGuestAccess: false,
      jwtSecret: process.env.JWT_SECRET,
      adminApiKey: process.env.ADMIN_API_KEY
    },
    logging: {
      logRequests: true,
      logResponses: false,
      logHeaders: false,
      logBody: false,
      logPerformance: true,
      excludePaths: ['/health', '/metrics', '/favicon.ico'],
      maxBodySize: 512
    },
    security: {
      cors: {
        origins: process.env.CORS_ORIGINS?.split(',') || [],
        credentials: true
      },
      rateLimit: {
        windowMs: 15 * 60 * 1000,
        max: 50
      }
    },
    errorHandling: {
      includeStackTrace: false,
      logErrors: true,
      enableRecovery: true
    }
  }, featureFlags);
};

export const createDevelopmentMiddleware = (featureFlags?: FeatureFlags) => {
  return createMiddlewareStack({
    environment: 'development',
    enableCompression: false,
    enableFeatureFlags: true,
    enableCompatibility: true,
    enableHealthChecks: true,
    authentication: {
      enableGuestAccess: true,
      jwtSecret: 'development-secret',
      adminApiKey: 'dev-admin-key'
    },
    logging: {
      logRequests: true,
      logResponses: true,
      logHeaders: true,
      logBody: true,
      logPerformance: true,
      excludePaths: ['/favicon.ico'],
      maxBodySize: 2048
    },
    security: {
      cors: {
        origins: ['*'],
        credentials: true
      },
      rateLimit: {
        windowMs: 15 * 60 * 1000,
        max: 1000
      }
    },
    errorHandling: {
      includeStackTrace: true,
      logErrors: true,
      enableRecovery: true
    }
  }, featureFlags);
};

export const createTestingMiddleware = (featureFlags?: FeatureFlags) => {
  return createMiddlewareStack({
    environment: 'development',
    enableCompression: false,
    enableFeatureFlags: false,
    enableCompatibility: false,
    enableHealthChecks: false,
    authentication: {
      enableGuestAccess: true,
      jwtSecret: 'test-secret',
      adminApiKey: 'test-admin-key'
    },
    logging: {
      logRequests: false,
      logResponses: false,
      logHeaders: false,
      logBody: false,
      logPerformance: false,
      excludePaths: ['*']
    },
    security: {
      cors: {
        origins: ['*'],
        credentials: true
      },
      rateLimit: {
        windowMs: 15 * 60 * 1000,
        max: 10000 // Very high limit for testing
      }
    },
    errorHandling: {
      includeStackTrace: true,
      logErrors: false,
      enableRecovery: false
    }
  }, featureFlags);
};

// Health check function for all middleware
export const getMiddlewareHealth = async (factory: MiddlewareFactory) => {
  return factory.getInstances();
};

// Utility functions
export const isAuthenticatedUser = (req: any): boolean => {
  return req.isAuthenticated === true && req.user && req.user.id;
};

export const hasRole = (req: any, role: string): boolean => {
  return req.user && req.user.roles && req.user.roles.includes(role);
};

export const hasPermission = (req: any, permission: string): boolean => {
  return req.user && req.user.permissions && 
         (req.user.permissions.includes(permission) || req.user.permissions.includes('*'));
};

export const isAdmin = (req: any): boolean => {
  return req.user && (req.user.isAdmin === true || hasRole(req, 'admin'));
};

export const getCorrelationId = (req: any): string => {
  return req.headers['x-correlation-id'] || 
         req.headers['x-request-id'] || 
         `unknown-${Date.now()}`;
};

export const getUserId = (req: any): string | undefined => {
  return req.user?.id;
};

export const getSessionId = (req: any): string | undefined => {
  return req.user?.sessionId || req.sessionId;
};

// Type guards
export const isCustomError = (error: any): boolean => {
  return error && error.name === 'CustomError';
};

export const isValidationError = (error: any): boolean => {
  return error && (error.name === 'ValidationError' || error.name === 'ValidationErrorClass');
};

export const isAuthenticationError = (error: any): boolean => {
  return error && error.name === 'AuthenticationError';
};

export const isAuthorizationError = (error: any): boolean => {
  return error && error.name === 'AuthorizationError';
};
