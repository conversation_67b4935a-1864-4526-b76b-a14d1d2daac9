/**
 * Tests for backward compatibility middleware
 */

import { Request, Response, NextFunction } from 'express';
import {
  legacyResponseTransformer,
  legacyRequestTransformer,
  compatibilityHeaders,
  compatibilityErrorHandler
} from '../compatibility';

// Mock logger
jest.mock('@kg-visualizer/shared', () => ({
  logger: {
    child: jest.fn(() => ({
      debug: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    }))
  }
}));

describe('Compatibility Middleware', () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockReq = {
      path: '/api/graph/initial',
      method: 'GET',
      headers: {},
      query: {},
      body: {}
    };

    mockRes = {
      json: jest.fn(),
      setHeader: jest.fn(),
      status: jest.fn(() => mockRes as Response)
    };

    mockNext = jest.fn();
  });

  describe('legacyResponseTransformer', () => {
    it('should transform new API response to legacy format for legacy endpoints', () => {
      mockReq.path = '/api/health';
      const originalJson = jest.fn();
      mockRes.json = originalJson;

      legacyResponseTransformer(mockReq as Request, mockRes as Response, mockNext);

      // Call the transformed json method
      const newApiResponse = {
        success: true,
        data: { status: 'ok', migrationPhase: { backend: { percentage: 50 } } },
        timestamp: '2024-01-01T00:00:00.000Z'
      };

      (mockRes.json as jest.Mock)(newApiResponse);

      expect(originalJson).toHaveBeenCalledWith({
        status: 'ok',
        timestamp: '2024-01-01T00:00:00.000Z',
        migrationPhase: { backend: { percentage: 50 } }
      });
    });

    it('should pass through response unchanged for non-legacy endpoints', () => {
      mockReq.path = '/api/v2/new-endpoint';
      const originalJson = jest.fn();
      mockRes.json = originalJson;

      legacyResponseTransformer(mockReq as Request, mockRes as Response, mockNext);

      const response = { newFormat: true };
      (mockRes.json as jest.Mock)(response);

      expect(originalJson).toHaveBeenCalledWith(response);
    });

    it('should transform graph response correctly', () => {
      mockReq.path = '/api/graph/initial';
      const originalJson = jest.fn();
      mockRes.json = originalJson;

      legacyResponseTransformer(mockReq as Request, mockRes as Response, mockNext);

      const newApiResponse = {
        success: true,
        data: {
          nodes: [{ id: '1', label: 'Node 1' }],
          edges: [{ id: 'e1', from: '1', to: '2' }]
        }
      };

      (mockRes.json as jest.Mock)(newApiResponse);

      expect(originalJson).toHaveBeenCalledWith({
        nodes: [{ id: '1', label: 'Node 1' }],
        edges: [{ id: 'e1', from: '1', to: '2' }]
      });
    });

    it('should handle error responses correctly', () => {
      mockReq.path = '/api/graph/search';
      const originalJson = jest.fn();
      mockRes.json = originalJson;

      legacyResponseTransformer(mockReq as Request, mockRes as Response, mockNext);

      const errorResponse = {
        success: false,
        error: 'Search term is required'
      };

      (mockRes.json as jest.Mock)(errorResponse);

      expect(originalJson).toHaveBeenCalledWith({
        error: 'Search term is required'
      });
    });
  });

  describe('legacyRequestTransformer', () => {
    it('should add request ID if not present', () => {
      mockReq.headers = {};
      
      legacyRequestTransformer(mockReq as Request, mockRes as Response, mockNext);

      expect(mockReq.headers['x-request-id']).toBeDefined();
      expect(typeof mockReq.headers['x-request-id']).toBe('string');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should not override existing request ID', () => {
      const existingId = 'existing-request-id';
      mockReq.headers = { 'x-request-id': existingId };
      
      legacyRequestTransformer(mockReq as Request, mockRes as Response, mockNext);

      expect(mockReq.headers['x-request-id']).toBe(existingId);
      expect(mockNext).toHaveBeenCalled();
    });

    it('should transform filter request body', () => {
      mockReq.path = '/api/graph/filter';
      mockReq.method = 'POST';
      mockReq.body = {
        nodeLabels: ['Product', 'Feature'],
        relationshipTypes: ['CONTAINS']
      };

      legacyRequestTransformer(mockReq as Request, mockRes as Response, mockNext);

      expect(mockReq.body.filters).toEqual({
        nodeLabels: ['Product', 'Feature'],
        relationshipTypes: ['CONTAINS']
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('should validate and limit pagination parameters', () => {
      mockReq.query = {
        limit: '2000', // Should be limited to 1000
        offset: '-5'   // Should be set to 0
      };

      legacyRequestTransformer(mockReq as Request, mockRes as Response, mockNext);

      expect(mockReq.query.limit).toBe('1000');
      expect(mockReq.query.offset).toBe('0');
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('compatibilityHeaders', () => {
    it('should add compatibility headers', () => {
      compatibilityHeaders(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.setHeader).toHaveBeenCalledWith('X-API-Version', '2.0');
      expect(mockRes.setHeader).toHaveBeenCalledWith('X-Legacy-Compatible', 'true');
      expect(mockRes.setHeader).toHaveBeenCalledWith('X-Migration-Phase', expect.any(String));
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('compatibilityErrorHandler', () => {
    it('should transform errors to legacy format for legacy endpoints', () => {
      const error = new Error('Test error');
      error.status = 400;

      compatibilityErrorHandler(error, mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'Test error' });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should pass through errors for non-legacy endpoints', () => {
      mockReq.path = '/api/v2/new-endpoint';
      const error = new Error('Test error');

      compatibilityErrorHandler(error, mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(error);
    });

    it('should handle errors without status code', () => {
      const error = new Error('Test error');

      compatibilityErrorHandler(error, mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'Test error' });
    });
  });

  describe('Response format transformations', () => {
    it('should transform metadata response correctly', () => {
      mockReq.path = '/api/metadata';
      const originalJson = jest.fn();
      mockRes.json = originalJson;

      legacyResponseTransformer(mockReq as Request, mockRes as Response, mockNext);

      const newApiResponse = {
        success: true,
        data: {
          nodeLabels: [{ label: 'Product', count: 10 }],
          relationshipTypes: [{ type: 'CONTAINS', count: 5 }]
        }
      };

      (mockRes.json as jest.Mock)(newApiResponse);

      expect(originalJson).toHaveBeenCalledWith({
        nodeLabels: [{ label: 'Product', count: 10 }],
        relationshipTypes: [{ type: 'CONTAINS', count: 5 }]
      });
    });

    it('should transform feature flags response correctly', () => {
      mockReq.path = '/api/feature-flags';
      const originalJson = jest.fn();
      mockRes.json = originalJson;

      legacyResponseTransformer(mockReq as Request, mockRes as Response, mockNext);

      const newApiResponse = {
        success: true,
        data: {
          flags: { NEW_CONTROLLER_LAYER: true },
          migrationPhase: { backend: { percentage: 50 } },
          userId: 'user123...'
        },
        timestamp: '2024-01-01T00:00:00.000Z'
      };

      (mockRes.json as jest.Mock)(newApiResponse);

      expect(originalJson).toHaveBeenCalledWith({
        flags: { NEW_CONTROLLER_LAYER: true },
        migrationPhase: { backend: { percentage: 50 } },
        userId: 'user123...',
        timestamp: '2024-01-01T00:00:00.000Z'
      });
    });

    it('should handle legacy format detection correctly', () => {
      mockReq.headers = { 'x-legacy-api': 'true' };
      const originalJson = jest.fn();
      mockRes.json = originalJson;

      legacyResponseTransformer(mockReq as Request, mockRes as Response, mockNext);

      const response = { data: 'test' };
      (mockRes.json as jest.Mock)(response);

      // Should transform even non-legacy endpoints when header is present
      expect(originalJson).toHaveBeenCalledWith(response);
    });
  });
});
