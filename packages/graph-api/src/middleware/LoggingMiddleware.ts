/**
 * Logging Middleware
 * 
 * Comprehensive request/response logging middleware with performance tracking,
 * correlation IDs, and structured logging.
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '@kg-visualizer/shared';

export interface LoggingConfig {
  logRequests?: boolean;
  logResponses?: boolean;
  logHeaders?: boolean;
  logBody?: boolean;
  logPerformance?: boolean;
  excludePaths?: string[];
  excludeHeaders?: string[];
  maxBodySize?: number;
  sensitiveFields?: string[];
}

export interface RequestLog {
  correlationId: string;
  method: string;
  path: string;
  query: any;
  headers: any;
  body?: any;
  userAgent?: string;
  ip: string;
  userId?: string;
  sessionId?: string;
  timestamp: string;
}

export interface ResponseLog {
  correlationId: string;
  statusCode: number;
  headers: any;
  body?: any;
  responseTime: number;
  timestamp: string;
}

export interface PerformanceLog {
  correlationId: string;
  method: string;
  path: string;
  responseTime: number;
  statusCode: number;
  userId?: string;
  timestamp: string;
  memoryUsage?: NodeJS.MemoryUsage;
}

export class LoggingMiddleware {
  private readonly config: LoggingConfig;
  private readonly logger = logger.child('request-middleware');

  constructor(config: LoggingConfig = {}) {
    this.config = {
      logRequests: true,
      logResponses: true,
      logHeaders: false,
      logBody: false,
      logPerformance: true,
      excludePaths: ['/health', '/metrics', '/favicon.ico'],
      excludeHeaders: ['authorization', 'cookie', 'x-api-key'],
      maxBodySize: 1024, // 1KB
      sensitiveFields: ['password', 'token', 'secret', 'key', 'auth'],
      ...config
    };
  }

  /**
   * Main logging middleware
   */
  log = () => {
    return (req: Request, res: Response, next: NextFunction): void => {
      // Skip excluded paths
      if (this.shouldSkipLogging(req.path)) {
        return next();
      }

      const startTime = Date.now();
      const correlationId = this.getOrCreateCorrelationId(req);
      
      // Add correlation ID to request
      req.headers['x-correlation-id'] = correlationId;

      // Log request
      if (this.config.logRequests) {
        this.logRequest(req, correlationId);
      }

      // Capture original response methods
      const originalSend = res.send;
      const originalJson = res.json;
      let responseBody: any;

      // Override response methods to capture response data
      res.send = function (body: any) {
        responseBody = body;
        return originalSend.call(this, body);
      };

      res.json = function (body: any) {
        responseBody = body;
        return originalJson.call(this, body);
      };

      // Log response when finished
      res.on('finish', () => {
        const responseTime = Date.now() - startTime;

        if (this.config.logResponses) {
          this.logResponse(res, responseBody, correlationId, responseTime);
        }

        if (this.config.logPerformance) {
          this.logPerformance(req, res, correlationId, responseTime);
        }
      });

      next();
    };
  };

  /**
   * Performance-only logging middleware (lighter weight)
   */
  performance = () => {
    return (req: Request, res: Response, next: NextFunction): void => {
      if (this.shouldSkipLogging(req.path)) {
        return next();
      }

      const startTime = Date.now();
      const correlationId = this.getOrCreateCorrelationId(req);

      res.on('finish', () => {
        const responseTime = Date.now() - startTime;
        this.logPerformance(req, res, correlationId, responseTime);
      });

      next();
    };
  };

  /**
   * Error logging middleware
   */
  error = () => {
    return (error: Error, req: Request, _res: Response, next: NextFunction): void => {
      const correlationId = this.getOrCreateCorrelationId(req);

      this.logger.error('Request error', 'request-middleware', {
        correlationId,
        method: req.method,
        path: req.path,
        error: error.message,
        stack: error.stack,
        userId: (req as any).user?.id,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      next(error);
    };
  };

  /**
   * Slow request detection middleware
   */
  slowRequestDetection = (thresholdMs: number = 1000) => {
    return (req: Request, res: Response, next: NextFunction): void => {
      const startTime = Date.now();
      const correlationId = this.getOrCreateCorrelationId(req);

      res.on('finish', () => {
        const responseTime = Date.now() - startTime;
        
        if (responseTime > thresholdMs) {
          this.logger.warn('Slow request detected', 'request-middleware', {
            correlationId,
            method: req.method,
            path: req.path,
            responseTime,
            threshold: thresholdMs,
            statusCode: res.statusCode,
            userId: (req as any).user?.id
          });
        }
      });

      next();
    };
  };

  /**
   * Log request details
   */
  private logRequest(req: Request, correlationId: string): void {
    const requestLog: RequestLog = {
      correlationId,
      method: req.method,
      path: req.path,
      query: req.query,
      headers: this.config.logHeaders ? this.sanitizeHeaders(req.headers) : {},
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: (req as any).user?.id,
      sessionId: (req as any).sessionId,
      timestamp: new Date().toISOString()
    };

    if (this.config.logBody && req.body) {
      requestLog.body = this.sanitizeBody(req.body);
    }

    this.logger.info('Request received', 'request-middleware', requestLog);
  }

  /**
   * Log response details
   */
  private logResponse(res: Response, body: any, correlationId: string, responseTime: number): void {
    const responseLog: ResponseLog = {
      correlationId,
      statusCode: res.statusCode,
      headers: this.config.logHeaders ? this.sanitizeHeaders(res.getHeaders()) : {},
      responseTime,
      timestamp: new Date().toISOString()
    };

    if (this.config.logBody && body) {
      responseLog.body = this.sanitizeBody(body);
    }

    const logLevel = res.statusCode >= 400 ? 'warn' : 'info';
    this.logger[logLevel]('Response sent', 'request-middleware', responseLog);
  }

  /**
   * Log performance metrics
   */
  private logPerformance(req: Request, res: Response, correlationId: string, responseTime: number): void {
    const performanceLog: PerformanceLog = {
      correlationId,
      method: req.method,
      path: req.path,
      responseTime,
      statusCode: res.statusCode,
      userId: (req as any).user?.id,
      timestamp: new Date().toISOString()
    };

    // Add memory usage in development
    if (process.env.NODE_ENV !== 'production') {
      performanceLog.memoryUsage = process.memoryUsage();
    }

    this.logger.debug('Request performance', 'request-middleware', performanceLog);
  }

  /**
   * Get or create correlation ID
   */
  private getOrCreateCorrelationId(req: Request): string {
    return req.headers['x-correlation-id'] as string || 
           req.headers['x-request-id'] as string ||
           this.generateCorrelationId();
  }

  /**
   * Generate correlation ID
   */
  private generateCorrelationId(): string {
    return `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Check if logging should be skipped for this path
   */
  private shouldSkipLogging(path: string): boolean {
    return this.config.excludePaths!.some(excludePath => 
      path.includes(excludePath)
    );
  }

  /**
   * Sanitize headers by removing sensitive information
   */
  private sanitizeHeaders(headers: any): any {
    const sanitized = { ...headers };
    
    this.config.excludeHeaders!.forEach(header => {
      if (sanitized[header]) {
        sanitized[header] = '[REDACTED]';
      }
    });

    return sanitized;
  }

  /**
   * Sanitize body by removing sensitive fields and limiting size
   */
  private sanitizeBody(body: any): any {
    if (!body) return body;

    let sanitized = { ...body };

    // Remove sensitive fields
    this.config.sensitiveFields!.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    });

    // Limit body size
    const bodyString = JSON.stringify(sanitized);
    if (bodyString.length > this.config.maxBodySize!) {
      return {
        ...sanitized,
        _truncated: true,
        _originalSize: bodyString.length,
        _maxSize: this.config.maxBodySize
      };
    }

    return sanitized;
  }

  /**
   * Get logging statistics
   */
  getStatistics(): {
    requestsLogged: number;
    averageResponseTime: number;
    slowRequests: number;
    errorRequests: number;
  } {
    // In a real implementation, you would track these metrics
    return {
      requestsLogged: 0,
      averageResponseTime: 0,
      slowRequests: 0,
      errorRequests: 0
    };
  }
}

// Pre-configured logging middleware instances
export const loggingMiddleware = new LoggingMiddleware();

// Export common middleware functions
export const logRequests = loggingMiddleware.log();
export const logPerformance = loggingMiddleware.performance();
export const logErrors = loggingMiddleware.error();
export const detectSlowRequests = loggingMiddleware.slowRequestDetection;

// Custom logging configurations
export const createProductionLogging = () => new LoggingMiddleware({
  logRequests: true,
  logResponses: false,
  logHeaders: false,
  logBody: false,
  logPerformance: true,
  excludePaths: ['/health', '/metrics', '/favicon.ico', '/docs'],
  maxBodySize: 512
});

export const createDevelopmentLogging = () => new LoggingMiddleware({
  logRequests: true,
  logResponses: true,
  logHeaders: true,
  logBody: true,
  logPerformance: true,
  excludePaths: ['/favicon.ico'],
  maxBodySize: 2048
});

export const createDebugLogging = () => new LoggingMiddleware({
  logRequests: true,
  logResponses: true,
  logHeaders: true,
  logBody: true,
  logPerformance: true,
  excludePaths: [],
  maxBodySize: 4096,
  excludeHeaders: [] // Log all headers in debug mode
});
