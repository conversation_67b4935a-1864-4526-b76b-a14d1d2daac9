/**
 * Backward Compatibility Middleware
 * 
 * This middleware ensures that the new API maintains backward compatibility
 * with the existing API contracts during the migration period.
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '@kg-visualizer/shared';

// Legacy API response format interface
interface LegacyApiResponse {
  success?: boolean;
  data?: any;
  error?: string;
  timestamp?: string;
  [key: string]: any;
}

// New API response format interface
interface NewApiResponse {
  success: boolean;
  data?: any;
  error?: string;
  timestamp: string;
  requestId?: string;
  metadata?: Record<string, any>;
}

/**
 * Middleware to transform new API responses to legacy format
 */
export const legacyResponseTransformer = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const originalJson = res.json;
  const compatibilityLogger = logger.child('compatibility');

  res.json = function (body: any) {
    // Check if this is a legacy endpoint request
    const isLegacyRequest = req.headers['x-legacy-api'] === 'true' || 
                           req.query.legacy === 'true' ||
                           isLegacyEndpoint(req.path);

    if (isLegacyRequest) {
      const transformedBody = transformToLegacyFormat(body, req.path);
      
      compatibilityLogger.debug('Transformed response for legacy compatibility', 'compatibility', {
        path: req.path,
        method: req.method,
        originalFormat: !!body.requestId,
        transformedFormat: 'legacy'
      });

      return originalJson.call(this, transformedBody);
    }

    return originalJson.call(this, body);
  };

  next();
};

/**
 * Middleware to transform legacy request format to new format
 */
export const legacyRequestTransformer = (
  req: Request,
  _res: Response,
  next: NextFunction
): void => {
  const compatibilityLogger = logger.child('compatibility');

  // Transform legacy request parameters
  if (isLegacyEndpoint(req.path)) {
    transformLegacyRequest(req);
    
    compatibilityLogger.debug('Transformed request for legacy compatibility', 'compatibility', {
      path: req.path,
      method: req.method,
      hasTransformation: true
    });
  }

  next();
};

/**
 * Check if the endpoint is a legacy endpoint that needs compatibility
 */
function isLegacyEndpoint(path: string): boolean {
  const legacyPaths = [
    '/api/graph/initial',
    '/api/graph/search',
    '/api/graph/expand',
    '/api/graph/filter',
    '/api/graph/query',
    '/api/analysis/centrality',
    '/api/analysis/clusters',
    '/api/analysis/hidden-links',
    '/api/chat/message',
    '/api/chat/stream',
    '/api/chat/conversations',
    '/api/chat/performance',
    '/api/metadata',
    '/api/health',
    '/api/feature-flags'
  ];

  return legacyPaths.some(legacyPath => path.startsWith(legacyPath));
}

/**
 * Transform new API response format to legacy format
 */
function transformToLegacyFormat(body: any, path: string): LegacyApiResponse {
  // If it's already in legacy format, return as-is
  if (!body || typeof body !== 'object' || !body.hasOwnProperty('success')) {
    return body;
  }

  const newResponse = body as NewApiResponse;
  // const legacyResponse: LegacyApiResponse = {};

  // Handle different endpoint-specific transformations
  switch (true) {
    case path.includes('/api/health'):
      return transformHealthResponse(newResponse);
    
    case path.includes('/api/metadata'):
      return transformMetadataResponse(newResponse);
    
    case path.includes('/api/graph/'):
      return transformGraphResponse(newResponse, path);
    
    case path.includes('/api/analysis/'):
      return transformAnalysisResponse(newResponse, path);
    
    case path.includes('/api/chat/'):
      return transformChatResponse(newResponse, path);
    
    case path.includes('/api/feature-flags'):
      return transformFeatureFlagsResponse(newResponse);
    
    default:
      // Generic transformation
      if (newResponse.success) {
        return newResponse.data || {};
      } else {
        return { error: newResponse.error || 'Unknown error' };
      }
  }
}

/**
 * Transform legacy request to new format
 */
function transformLegacyRequest(req: Request): void {
  // Add request ID for tracking
  if (!req.headers['x-request-id']) {
    req.headers['x-request-id'] = generateRequestId();
  }

  // Transform query parameters for specific endpoints
  if (req.path.includes('/api/graph/filter') && req.method === 'POST') {
    // Ensure filter request has proper structure
    if (!req.body.filters) {
      req.body.filters = {
        nodeLabels: req.body.nodeLabels || [],
        relationshipTypes: req.body.relationshipTypes || []
      };
    }
  }

  // Transform pagination parameters
  if (req.query.limit) {
    req.query.limit = Math.min(parseInt(req.query.limit as string) || 20, 1000).toString();
  }
  if (req.query.offset) {
    req.query.offset = Math.max(parseInt(req.query.offset as string) || 0, 0).toString();
  }
}

/**
 * Transform health endpoint response
 */
function transformHealthResponse(response: NewApiResponse): any {
  if (response.success && response.data) {
    return {
      status: response.data.status || 'ok',
      timestamp: response.timestamp,
      migrationPhase: response.data.migrationPhase
    };
  }
  return { status: 'error', error: response.error };
}

/**
 * Transform metadata endpoint response
 */
function transformMetadataResponse(response: NewApiResponse): any {
  if (response.success && response.data) {
    return {
      nodeLabels: response.data.nodeLabels || [],
      relationshipTypes: response.data.relationshipTypes || []
    };
  }
  return { error: response.error };
}

/**
 * Transform graph endpoint responses
 */
function transformGraphResponse(response: NewApiResponse, path: string): any {
  if (!response.success) {
    return { error: response.error };
  }

  const data = response.data;
  
  if (path.includes('/initial') || path.includes('/expand') || path.includes('/filter')) {
    // Graph data endpoints should return nodes and edges directly
    return {
      nodes: data?.nodes || [],
      edges: data?.edges || data?.relationships || []
    };
  }
  
  if (path.includes('/search')) {
    // Search endpoint returns array of nodes
    return data?.results || data?.nodes || [];
  }
  
  if (path.includes('/query')) {
    // Query endpoint returns raw Neo4j results
    return data?.records || data || [];
  }
  
  return data || {};
}

/**
 * Transform analysis endpoint responses
 */
function transformAnalysisResponse(response: NewApiResponse, path: string): any {
  if (!response.success) {
    return { error: response.error };
  }

  const data = response.data;
  
  if (path.includes('/centrality')) {
    return data?.centrality || data?.results || [];
  }
  
  if (path.includes('/clusters')) {
    return data?.clusters || data?.communities || [];
  }
  
  if (path.includes('/hidden-links')) {
    return data?.predictions || data?.links || [];
  }
  
  return data || {};
}

/**
 * Transform chat endpoint responses
 */
function transformChatResponse(response: NewApiResponse, path: string): any {
  if (!response.success) {
    return { error: response.error };
  }

  const data = response.data;
  
  if (path.includes('/conversations') && !path.includes('/conversations/')) {
    // List conversations endpoint
    return data?.conversations || [];
  }
  
  if (path.includes('/performance')) {
    return data?.performance || data || {};
  }
  
  // For message and stream endpoints, return the data directly
  return data || {};
}

/**
 * Transform feature flags response
 */
function transformFeatureFlagsResponse(response: NewApiResponse): any {
  if (response.success && response.data) {
    return {
      flags: response.data.flags || {},
      migrationPhase: response.data.migrationPhase || {},
      userId: response.data.userId,
      timestamp: response.timestamp
    };
  }
  return { error: response.error };
}

/**
 * Generate a unique request ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
}

/**
 * Middleware to add compatibility headers
 */
export const compatibilityHeaders = (
  _req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Add headers to indicate API version and compatibility
  res.setHeader('X-API-Version', '2.0');
  res.setHeader('X-Legacy-Compatible', 'true');
  res.setHeader('X-Migration-Phase', process.env.MIGRATION_PHASE || 'phase-1');
  
  next();
};

/**
 * Error handler for compatibility issues
 */
export const compatibilityErrorHandler = (
  err: any,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const compatibilityLogger = logger.child('compatibility-error');
  
  compatibilityLogger.error('Compatibility error occurred', 'compatibility-error', {
    error: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method,
    isLegacy: isLegacyEndpoint(req.path)
  });

  // Transform error to legacy format if needed
  if (isLegacyEndpoint(req.path)) {
    const status = err.status || err.statusCode || 500;
    const message = err.message || 'Internal Server Error';
    
    res.status(status).json({ error: message });
    return;
  }

  next(err);
};
