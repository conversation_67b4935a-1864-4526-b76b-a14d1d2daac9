/**
 * Middleware Factory
 * 
 * Factory for creating and configuring comprehensive middleware stacks
 * based on environment and feature flags.
 */

import { Express, Request, Response, NextFunction } from 'express';
import express from 'express';
import compression from 'compression';
import { FeatureFlags, logger } from '@kg-visualizer/shared';

// Import middleware classes
import { AuthenticationMiddleware, AuthenticationConfig } from './AuthenticationMiddleware';
import { ValidationMiddleware, ValidationConfig } from './ValidationMiddleware';
import { ErrorHandlingMiddleware, ErrorHandlingConfig } from './ErrorHandlingMiddleware';
import { LoggingMiddleware, LoggingConfig } from './LoggingMiddleware';
import { SecurityMiddleware, SecurityConfig } from './SecurityMiddleware';
import { compatibilityMiddleware, compatibilityHeaders } from './compatibility';

export interface MiddlewareConfig {
  environment?: 'development' | 'staging' | 'production';
  authentication?: AuthenticationConfig;
  validation?: ValidationConfig;
  errorHandling?: ErrorHandlingConfig;
  logging?: LoggingConfig;
  security?: SecurityConfig;
  enableCompression?: boolean;
  enableFeatureFlags?: boolean;
  enableCompatibility?: boolean;
  enableHealthChecks?: boolean;
}

export interface MiddlewareInstances {
  authentication: AuthenticationMiddleware;
  validation: ValidationMiddleware;
  errorHandling: ErrorHandlingMiddleware;
  logging: LoggingMiddleware;
  security: SecurityMiddleware;
}

export interface MiddlewareHealth {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  middleware: Record<string, {
    status: 'healthy' | 'degraded' | 'unhealthy';
    details?: any;
  }>;
}

export class MiddlewareFactory {
  private readonly config: MiddlewareConfig;
  private readonly featureFlags: FeatureFlags;
  private readonly logger = logger.child('middleware-factory');
  private instances: Partial<MiddlewareInstances> = {};

  constructor(config: MiddlewareConfig = {}, featureFlags: FeatureFlags) {
    this.config = {
      environment: process.env.NODE_ENV as any || 'development',
      enableCompression: true,
      enableFeatureFlags: true,
      enableCompatibility: true,
      enableHealthChecks: true,
      ...config
    };
    this.featureFlags = featureFlags;
  }

  /**
   * Configure complete middleware stack for Express app
   */
  configureApp(app: Express): void {
    this.logger.info('Configuring middleware stack', 'middleware-factory', {
      environment: this.config.environment,
      enabledFeatures: {
        compression: this.config.enableCompression,
        featureFlags: this.config.enableFeatureFlags,
        compatibility: this.config.enableCompatibility,
        healthChecks: this.config.enableHealthChecks
      }
    });

    // 1. Trust proxy (must be first)
    this.configureTrustProxy(app);

    // 2. Security middleware (CORS, headers, rate limiting)
    this.configureSecurity(app);

    // 3. Compression middleware
    if (this.config.enableCompression) {
      this.configureCompression(app);
    }

    // 4. Request parsing middleware
    this.configureRequestParsing(app);

    // 5. Logging middleware
    this.configureLogging(app);

    // 6. Feature flags middleware
    if (this.config.enableFeatureFlags) {
      this.configureFeatureFlags(app);
    }

    // 7. Compatibility middleware
    if (this.config.enableCompatibility) {
      this.configureCompatibility(app);
    }

    // 8. Authentication middleware (applied per route)
    this.configureAuthentication();

    // 9. Validation middleware (applied per route)
    this.configureValidation();

    // 10. Health check endpoints
    if (this.config.enableHealthChecks) {
      this.configureHealthChecks(app);
    }

    // 11. Error handling middleware (must be last)
    this.configureErrorHandling(app);

    this.logger.info('Middleware stack configured successfully', 'middleware-factory');
  }

  /**
   * Get middleware instances for manual configuration
   */
  getInstances(): MiddlewareInstances {
    return {
      authentication: this.getAuthenticationMiddleware(),
      validation: this.getValidationMiddleware(),
      errorHandling: this.getErrorHandlingMiddleware(),
      logging: this.getLoggingMiddleware(),
      security: this.getSecurityMiddleware()
    };
  }

  /**
   * Configure trust proxy
   */
  private configureTrustProxy(app: Express): void {
    if (this.config.environment === 'production') {
      app.set('trust proxy', 1); // Trust first proxy
    } else {
      app.set('trust proxy', true); // Trust all proxies in development
    }
  }

  /**
   * Configure security middleware
   */
  private configureSecurity(app: Express): void {
    const security = this.getSecurityMiddleware();

    // CORS
    app.use(security.cors());

    // Security headers
    app.use(security.securityHeaders());

    // Rate limiting
    app.use(security.rateLimit());

    // Request sanitization
    app.use(security.sanitization());

    // Trusted proxy check
    app.use(security.trustedProxy());

    // Security audit
    app.use(security.securityAudit());

    // CSP violation reporting
    app.use(security.cspReporting());
  }

  /**
   * Configure compression
   */
  private configureCompression(app: Express): void {
    app.use(compression({
      filter: (req: Request, res: Response) => {
        // Don't compress responses if the request includes a cache-control header
        if (req.headers['cache-control'] && req.headers['cache-control'].includes('no-transform')) {
          return false;
        }
        return compression.filter(req, res);
      },
      level: this.config.environment === 'production' ? 6 : 1,
      threshold: 1024 // Only compress responses larger than 1KB
    }));
  }

  /**
   * Configure request parsing
   */
  private configureRequestParsing(app: Express): void {
    // JSON parsing with size limits
    app.use(express.json({ 
      limit: '10mb',
      verify: (req: any, res: Response, buf: Buffer) => {
        req.rawBody = buf;
      }
    }));

    // URL-encoded parsing
    app.use(express.urlencoded({ 
      extended: true, 
      limit: '10mb' 
    }));

    // Raw body parsing for webhooks
    app.use('/webhooks/*', express.raw({ type: 'application/json' }));
  }

  /**
   * Configure logging middleware
   */
  private configureLogging(app: Express): void {
    const logging = this.getLoggingMiddleware();

    // Request/response logging
    app.use(logging.log());

    // Slow request detection
    app.use(logging.slowRequestDetection(2000)); // 2 seconds threshold

    // Error logging
    app.use(logging.error());
  }

  /**
   * Configure feature flags middleware
   */
  private configureFeatureFlags(app: Express): void {
    app.use((req: Request, res: Response, next: NextFunction) => {
      // Add feature flags to request
      (req as any).featureFlags = this.featureFlags;
      
      // Add migration phase header
      res.setHeader('X-Migration-Phase', this.featureFlags.getMigrationPhase());
      res.setHeader('X-Feature-Flags-Version', '2.0.0');
      
      next();
    });
  }

  /**
   * Configure compatibility middleware
   */
  private configureCompatibility(app: Express): void {
    app.use(compatibilityHeaders);
    app.use(compatibilityMiddleware);
  }

  /**
   * Configure authentication (returns middleware for route-level use)
   */
  private configureAuthentication(): void {
    this.getAuthenticationMiddleware(); // Initialize instance
  }

  /**
   * Configure validation (returns middleware for route-level use)
   */
  private configureValidation(): void {
    this.getValidationMiddleware(); // Initialize instance
  }

  /**
   * Configure health check endpoints
   */
  private configureHealthChecks(app: Express): void {
    // Middleware health check endpoint
    app.get('/health/middleware', async (req: Request, res: Response) => {
      try {
        const health = await this.getMiddlewareHealth();
        const statusCode = health.overall === 'healthy' ? 200 : 
                          health.overall === 'degraded' ? 200 : 503;
        
        res.status(statusCode).json(health);
      } catch (error) {
        res.status(500).json({
          overall: 'unhealthy',
          error: (error as Error).message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Individual middleware health checks
    app.get('/health/middleware/:name', async (req: Request, res: Response) => {
      const { name } = req.params;
      
      try {
        const health = await this.getMiddlewareHealth();
        const middlewareHealth = health.middleware[name];
        
        if (!middlewareHealth) {
          return res.status(404).json({
            error: `Middleware '${name}' not found`,
            available: Object.keys(health.middleware)
          });
        }

        const statusCode = middlewareHealth.status === 'healthy' ? 200 : 
                          middlewareHealth.status === 'degraded' ? 200 : 503;
        
        res.status(statusCode).json(middlewareHealth);
      } catch (error) {
        res.status(500).json({
          error: (error as Error).message,
          timestamp: new Date().toISOString()
        });
      }
    });
  }

  /**
   * Configure error handling (must be last)
   */
  private configureErrorHandling(app: Express): void {
    const errorHandling = this.getErrorHandlingMiddleware();

    // 404 handler
    app.use(errorHandling.notFound());

    // Recovery middleware
    app.use(errorHandling.recovery());

    // Main error handler
    app.use(errorHandling.handleError());
  }

  /**
   * Get or create authentication middleware
   */
  private getAuthenticationMiddleware(): AuthenticationMiddleware {
    if (!this.instances.authentication) {
      this.instances.authentication = new AuthenticationMiddleware(this.config.authentication);
    }
    return this.instances.authentication;
  }

  /**
   * Get or create validation middleware
   */
  private getValidationMiddleware(): ValidationMiddleware {
    if (!this.instances.validation) {
      this.instances.validation = new ValidationMiddleware(this.config.validation);
    }
    return this.instances.validation;
  }

  /**
   * Get or create error handling middleware
   */
  private getErrorHandlingMiddleware(): ErrorHandlingMiddleware {
    if (!this.instances.errorHandling) {
      this.instances.errorHandling = new ErrorHandlingMiddleware(this.config.errorHandling);
    }
    return this.instances.errorHandling;
  }

  /**
   * Get or create logging middleware
   */
  private getLoggingMiddleware(): LoggingMiddleware {
    if (!this.instances.logging) {
      const loggingConfig = this.config.logging || this.getEnvironmentLoggingConfig();
      this.instances.logging = new LoggingMiddleware(loggingConfig);
    }
    return this.instances.logging;
  }

  /**
   * Get or create security middleware
   */
  private getSecurityMiddleware(): SecurityMiddleware {
    if (!this.instances.security) {
      const securityConfig = this.config.security || this.getEnvironmentSecurityConfig();
      this.instances.security = new SecurityMiddleware(securityConfig);
    }
    return this.instances.security;
  }

  /**
   * Get environment-specific logging configuration
   */
  private getEnvironmentLoggingConfig(): LoggingConfig {
    switch (this.config.environment) {
      case 'production':
        return {
          logRequests: true,
          logResponses: false,
          logHeaders: false,
          logBody: false,
          logPerformance: true,
          excludePaths: ['/health', '/metrics', '/favicon.ico'],
          maxBodySize: 512
        };
      case 'staging':
        return {
          logRequests: true,
          logResponses: true,
          logHeaders: false,
          logBody: false,
          logPerformance: true,
          excludePaths: ['/health', '/favicon.ico'],
          maxBodySize: 1024
        };
      default: // development
        return {
          logRequests: true,
          logResponses: true,
          logHeaders: true,
          logBody: true,
          logPerformance: true,
          excludePaths: ['/favicon.ico'],
          maxBodySize: 2048
        };
    }
  }

  /**
   * Get environment-specific security configuration
   */
  private getEnvironmentSecurityConfig(): SecurityConfig {
    switch (this.config.environment) {
      case 'production':
        return {
          cors: {
            origins: process.env.CORS_ORIGINS?.split(',') || [],
            credentials: true
          },
          rateLimit: {
            windowMs: 15 * 60 * 1000,
            max: 50
          },
          helmet: {
            contentSecurityPolicy: {
              directives: {
                defaultSrc: ["'self'"],
                scriptSrc: ["'self'"],
                styleSrc: ["'self'"],
                imgSrc: ["'self'", 'data:', 'https:'],
                connectSrc: ["'self'"]
              }
            }
          }
        };
      default:
        return {
          cors: {
            origins: ['*'],
            credentials: true
          },
          rateLimit: {
            windowMs: 15 * 60 * 1000,
            max: 1000
          },
          helmet: {
            contentSecurityPolicy: false
          }
        };
    }
  }

  /**
   * Get middleware health status
   */
  private async getMiddlewareHealth(): Promise<MiddlewareHealth> {
    const middleware: Record<string, any> = {};
    const statuses: string[] = [];

    // Check each middleware component
    try {
      middleware.authentication = { status: 'healthy' };
      statuses.push('healthy');
    } catch (error) {
      middleware.authentication = { 
        status: 'unhealthy', 
        details: { error: (error as Error).message } 
      };
      statuses.push('unhealthy');
    }

    try {
      middleware.validation = { status: 'healthy' };
      statuses.push('healthy');
    } catch (error) {
      middleware.validation = { 
        status: 'unhealthy', 
        details: { error: (error as Error).message } 
      };
      statuses.push('unhealthy');
    }

    try {
      middleware.errorHandling = { status: 'healthy' };
      statuses.push('healthy');
    } catch (error) {
      middleware.errorHandling = { 
        status: 'unhealthy', 
        details: { error: (error as Error).message } 
      };
      statuses.push('unhealthy');
    }

    try {
      middleware.logging = { status: 'healthy' };
      statuses.push('healthy');
    } catch (error) {
      middleware.logging = { 
        status: 'unhealthy', 
        details: { error: (error as Error).message } 
      };
      statuses.push('unhealthy');
    }

    try {
      middleware.security = { status: 'healthy' };
      statuses.push('healthy');
    } catch (error) {
      middleware.security = { 
        status: 'unhealthy', 
        details: { error: (error as Error).message } 
      };
      statuses.push('unhealthy');
    }

    // Determine overall health
    const hasUnhealthy = statuses.includes('unhealthy');
    const hasDegraded = statuses.includes('degraded');

    let overall: 'healthy' | 'degraded' | 'unhealthy';
    if (hasUnhealthy) {
      overall = 'unhealthy';
    } else if (hasDegraded) {
      overall = 'degraded';
    } else {
      overall = 'healthy';
    }

    return { overall, middleware };
  }
}
