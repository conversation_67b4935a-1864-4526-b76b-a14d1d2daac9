/**
 * Error Handling Middleware
 * 
 * Comprehensive error handling middleware for consistent error responses,
 * logging, and error recovery strategies.
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '@kg-visualizer/shared';

export interface ErrorResponse {
  code: string;
  message: string;
  statusCode: number;
  timestamp: string;
  correlationId?: string;
  details?: any;
  stack?: string;
}

export interface ErrorHandlingConfig {
  includeStackTrace?: boolean;
  logErrors?: boolean;
  enableRecovery?: boolean;
  maxRetries?: number;
  retryDelay?: number;
}

export class CustomError extends Error {
  public readonly statusCode: number;
  public readonly code: string;
  public readonly details?: any;
  public readonly isOperational: boolean;

  constructor(
    message: string,
    statusCode: number = 500,
    code: string = 'INTERNAL_ERROR',
    details?: any,
    isOperational: boolean = true
  ) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Specific error classes
export class ValidationError extends CustomError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR', details);
  }
}

export class AuthenticationError extends CustomError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

export class AuthorizationError extends CustomError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

export class NotFoundError extends CustomError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, 404, 'NOT_FOUND');
  }
}

export class ConflictError extends CustomError {
  constructor(message: string) {
    super(message, 409, 'CONFLICT');
  }
}

export class RateLimitError extends CustomError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 429, 'RATE_LIMIT_EXCEEDED');
  }
}

export class DatabaseError extends CustomError {
  constructor(message: string, details?: any) {
    super(message, 500, 'DATABASE_ERROR', details);
  }
}

export class ExternalServiceError extends CustomError {
  constructor(service: string, message: string, details?: any) {
    super(`${service} service error: ${message}`, 502, 'EXTERNAL_SERVICE_ERROR', details);
  }
}

export class TimeoutError extends CustomError {
  constructor(operation: string, timeout: number) {
    super(`${operation} timed out after ${timeout}ms`, 408, 'TIMEOUT_ERROR');
  }
}

export class ErrorHandlingMiddleware {
  private readonly config: ErrorHandlingConfig;
  private readonly logger = logger.child('error-middleware');

  constructor(config: ErrorHandlingConfig = {}) {
    this.config = {
      includeStackTrace: process.env.NODE_ENV !== 'production',
      logErrors: true,
      enableRecovery: true,
      maxRetries: 3,
      retryDelay: 1000,
      ...config
    };
  }

  /**
   * Main error handling middleware
   */
  handleError = () => {
    return (error: Error, req: Request, res: Response, next: NextFunction): void => {
      // Skip if response already sent
      if (res.headersSent) {
        return next(error);
      }

      const correlationId = req.headers['x-correlation-id'] as string || 
                           this.generateCorrelationId();

      // Log the error
      if (this.config.logErrors) {
        this.logError(error, req, correlationId);
      }

      // Create error response
      const errorResponse = this.createErrorResponse(error, correlationId);

      // Send error response
      res.status(errorResponse.statusCode).json(errorResponse);
    };
  };

  /**
   * Async error wrapper for route handlers
   */
  asyncHandler = (fn: (req: Request, res: Response, next: NextFunction) => Promise<any>) => {
    return (req: Request, res: Response, next: NextFunction): void => {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  };

  /**
   * Not found middleware
   */
  notFound = () => {
    return (req: Request, res: Response, next: NextFunction): void => {
      const error = new NotFoundError(`Route ${req.method} ${req.path}`);
      next(error);
    };
  };

  /**
   * Timeout middleware
   */
  timeout = (timeoutMs: number = 30000) => {
    return (req: Request, _res: Response, next: NextFunction): void => {
      const timeout = setTimeout(() => {
        if (!res.headersSent) {
          const error = new TimeoutError(`Request to ${req.path}`, timeoutMs);
          next(error);
        }
      }, timeoutMs);

      // Clear timeout when response is finished
      res.on('finish', () => clearTimeout(timeout));
      res.on('close', () => clearTimeout(timeout));

      next();
    };
  };

  /**
   * Recovery middleware for specific error types
   */
  recovery = () => {
    return async (error: Error, req: Request, _res: Response, next: NextFunction): Promise<void> => {
      if (!this.config.enableRecovery) {
        return next(error);
      }

      // Try to recover from specific error types
      if (error instanceof DatabaseError) {
        const recovered = await this.tryDatabaseRecovery(error, req);
        if (recovered) {
          this.logger.info('Database error recovered', 'error-middleware', {
            path: req.path,
            method: req.method,
            originalError: error.message
          });
          return next(); // Continue processing
        }
      }

      if (error instanceof ExternalServiceError) {
        const recovered = await this.tryServiceRecovery(error, req);
        if (recovered) {
          this.logger.info('Service error recovered', 'error-middleware', {
            path: req.path,
            method: req.method,
            originalError: error.message
          });
          return next(); // Continue processing
        }
      }

      // If recovery failed, continue with error handling
      next(error);
    };
  };

  /**
   * Create standardized error response
   */
  private createErrorResponse(error: Error, correlationId: string): ErrorResponse {
    let statusCode = 500;
    let code = 'INTERNAL_ERROR';
    let message = 'Internal server error';
    let details: any;

    if (error instanceof CustomError) {
      statusCode = error.statusCode;
      code = error.code;
      message = error.message;
      details = error.details;
    } else if (error.name === 'ValidationError') {
      statusCode = 400;
      code = 'VALIDATION_ERROR';
      message = error.message;
    } else if (error.name === 'CastError') {
      statusCode = 400;
      code = 'INVALID_ID';
      message = 'Invalid ID format';
    } else if (error.name === 'MongoError' || error.name === 'MongooseError') {
      statusCode = 500;
      code = 'DATABASE_ERROR';
      message = 'Database operation failed';
    } else if (error.name === 'JsonWebTokenError') {
      statusCode = 401;
      code = 'INVALID_TOKEN';
      message = 'Invalid authentication token';
    } else if (error.name === 'TokenExpiredError') {
      statusCode = 401;
      code = 'TOKEN_EXPIRED';
      message = 'Authentication token expired';
    }

    const response: ErrorResponse = {
      code,
      message,
      statusCode,
      timestamp: new Date().toISOString(),
      correlationId
    };

    if (details) {
      response.details = details;
    }

    if (this.config.includeStackTrace && error.stack) {
      response.stack = error.stack;
    }

    return response;
  }

  /**
   * Log error with context
   */
  private logError(error: Error, req: Request, correlationId: string): void {
    const logLevel = error instanceof CustomError && error.statusCode < 500 ? 'warn' : 'error';
    
    const errorContext = {
      correlationId,
      path: req.path,
      method: req.method,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: (req as any).user?.id,
      statusCode: error instanceof CustomError ? error.statusCode : 500,
      errorCode: error instanceof CustomError ? error.code : 'INTERNAL_ERROR',
      stack: error.stack
    };

    if (logLevel === 'error') {
      this.logger.error(error.message, 'error-middleware', errorContext);
    } else {
      this.logger.warn(error.message, 'error-middleware', errorContext);
    }
  }

  /**
   * Try to recover from database errors
   */
  private async tryDatabaseRecovery(error: DatabaseError, req: Request): Promise<boolean> {
    // Implement database recovery logic
    // For example: retry connection, use fallback data source, etc.
    
    if (error.message.includes('connection')) {
      // Try to reconnect or use cached data
      this.logger.info('Attempting database recovery', 'error-middleware', {
        path: req.path,
        errorType: 'connection'
      });
      
      // Simulate recovery attempt
      await this.delay(this.config.retryDelay!);
      return false; // Recovery failed in this example
    }

    return false;
  }

  /**
   * Try to recover from external service errors
   */
  private async tryServiceRecovery(error: ExternalServiceError, req: Request): Promise<boolean> {
    // Implement service recovery logic
    // For example: retry request, use fallback service, return cached data
    
    this.logger.info('Attempting service recovery', 'error-middleware', {
      path: req.path,
      service: error.details?.service
    });

    // Simulate recovery attempt
    await this.delay(this.config.retryDelay!);
    return false; // Recovery failed in this example
  }

  /**
   * Generate correlation ID
   */
  private generateCorrelationId(): string {
    return `err-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Pre-configured error handling middleware
export const errorHandlingMiddleware = new ErrorHandlingMiddleware();

// Export common middleware functions
export const handleError = errorHandlingMiddleware.handleError();
export const asyncHandler = errorHandlingMiddleware.asyncHandler;
export const notFound = errorHandlingMiddleware.notFound();
export const timeout = errorHandlingMiddleware.timeout;
export const recovery = errorHandlingMiddleware.recovery();

// Error factory functions
export const createValidationError = (message: string, details?: any) => 
  new ValidationError(message, details);

export const createAuthenticationError = (message?: string) => 
  new AuthenticationError(message);

export const createAuthorizationError = (message?: string) => 
  new AuthorizationError(message);

export const createNotFoundError = (resource?: string) => 
  new NotFoundError(resource);

export const createConflictError = (message: string) => 
  new ConflictError(message);

export const createRateLimitError = (message?: string) => 
  new RateLimitError(message);

export const createDatabaseError = (message: string, details?: any) => 
  new DatabaseError(message, details);

export const createExternalServiceError = (service: string, message: string, details?: any) => 
  new ExternalServiceError(service, message, details);

export const createTimeoutError = (operation: string, timeout: number) =>
  new TimeoutError(operation, timeout);

// Health check for error handling
export const errorHandlingHealthCheck = () => {
  return {
    status: 'healthy',
    middleware: 'error-handling',
    timestamp: new Date().toISOString()
  };
};
