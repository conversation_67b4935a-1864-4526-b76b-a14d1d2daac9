/**
 * Tests for BaseService
 */

import { BaseService } from '../BaseService';

// Mock logger
jest.mock('@kg-visualizer/shared', () => ({
  logger: {
    child: jest.fn(() => ({
      info: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    }))
  },
  PerformanceTimer: jest.fn().mockImplementation(() => ({
    elapsed: jest.fn().mockReturnValue(100)
  }))
}));

class TestService extends BaseService {
  async testOperation(data: string): Promise<string> {
    return this.executeOperation(
      'testOperation',
      async () => {
        return `processed: ${data}`;
      },
      `test-${data}`
    );
  }

  async testOperationWithError(): Promise<string> {
    return this.executeOperation(
      'testOperationWithError',
      async () => {
        throw new Error('Test error');
      }
    );
  }

  async testRetryOperation(shouldFail: boolean = false): Promise<string> {
    return this.executeWithRetry(
      'testRetryOperation',
      async () => {
        if (shouldFail) {
          throw new Error('Retry test error');
        }
        return 'success';
      },
      2,
      100
    );
  }

  testValidation(input: any) {
    return this.validateInput(input, (data) => {
      const errors: string[] = [];
      if (!data.name) errors.push('name is required');
      if (!data.value) errors.push('value is required');
      return { isValid: errors.length === 0, errors };
    });
  }

  testTransform<T, U>(data: T, transformer: (input: T) => U): U {
    return this.transformData(data, transformer);
  }

  testClearCache(pattern?: string) {
    this.clearCache(pattern);
  }
}

describe('BaseService', () => {
  let service: TestService;

  beforeEach(() => {
    service = new TestService({
      enableCaching: true,
      cacheTimeout: 1000,
      enableMetrics: true,
      timeout: 5000
    });
  });

  describe('executeOperation', () => {
    it('should execute operation successfully', async () => {
      const result = await service.testOperation('test-data');
      expect(result).toBe('processed: test-data');
    });

    it('should handle operation errors', async () => {
      await expect(service.testOperationWithError()).rejects.toThrow('Test error');
    });

    it('should cache results when caching is enabled', async () => {
      const result1 = await service.testOperation('cached-data');
      const result2 = await service.testOperation('cached-data');
      
      expect(result1).toBe(result2);
      expect(result1).toBe('processed: cached-data');
    });

    it('should record metrics', async () => {
      await service.testOperation('metrics-test');
      
      const metrics = service.getMetrics();
      expect(metrics).toHaveLength(1);
      expect(metrics[0].operationName).toBe('testOperation');
      expect(metrics[0].success).toBe(true);
    });

    it('should record error metrics', async () => {
      try {
        await service.testOperationWithError();
      } catch (error) {
        // Expected error
      }
      
      const metrics = service.getMetrics();
      expect(metrics).toHaveLength(1);
      expect(metrics[0].operationName).toBe('testOperationWithError');
      expect(metrics[0].success).toBe(false);
      expect(metrics[0].error).toBe('Test error');
    });
  });

  describe('executeWithRetry', () => {
    it('should succeed on first attempt', async () => {
      const result = await service.testRetryOperation(false);
      expect(result).toBe('success');
    });

    it('should retry on failure and eventually fail', async () => {
      await expect(service.testRetryOperation(true)).rejects.toThrow('Retry test error');
    });
  });

  describe('validateInput', () => {
    it('should validate input successfully', () => {
      expect(() => {
        service.testValidation({ name: 'test', value: 'data' });
      }).not.toThrow();
    });

    it('should throw validation error for invalid input', () => {
      expect(() => {
        service.testValidation({ name: 'test' }); // missing value
      }).toThrow('Validation failed: value is required');
    });

    it('should throw validation error for multiple missing fields', () => {
      expect(() => {
        service.testValidation({}); // missing both
      }).toThrow('Validation failed: name is required, value is required');
    });
  });

  describe('transformData', () => {
    it('should transform data successfully', () => {
      const result = service.testTransform('hello', (input: string) => input.toUpperCase());
      expect(result).toBe('HELLO');
    });

    it('should handle transformation errors', () => {
      expect(() => {
        service.testTransform('test', () => {
          throw new Error('Transform error');
        });
      }).toThrow('Data transformation failed: Transform error');
    });
  });

  describe('cache management', () => {
    it('should clear all cache', async () => {
      await service.testOperation('cache-test-1');
      await service.testOperation('cache-test-2');
      
      service.testClearCache();
      
      // After clearing cache, operations should execute again
      const result = await service.testOperation('cache-test-1');
      expect(result).toBe('processed: cache-test-1');
    });

    it('should clear cache by pattern', async () => {
      await service.testOperation('pattern-test-1');
      await service.testOperation('other-test-1');
      
      service.testClearCache('pattern');
      
      // Only pattern-matching cache should be cleared
      const result1 = await service.testOperation('pattern-test-1');
      const result2 = await service.testOperation('other-test-1');
      
      expect(result1).toBe('processed: pattern-test-1');
      expect(result2).toBe('processed: other-test-1');
    });
  });

  describe('getStatistics', () => {
    it('should return service statistics', async () => {
      await service.testOperation('stats-test-1');
      await service.testOperation('stats-test-2');
      
      try {
        await service.testOperationWithError();
      } catch (error) {
        // Expected error
      }
      
      const stats = service.getStatistics();
      
      expect(stats.totalOperations).toBe(3);
      expect(stats.successRate).toBeCloseTo(66.67, 1);
      expect(stats.averageDuration).toBeGreaterThan(0);
      expect(stats.recentErrors).toContain('Test error');
    });
  });

  describe('healthCheck', () => {
    it('should return healthy status for good performance', async () => {
      // Execute some successful operations
      await service.testOperation('health-test-1');
      await service.testOperation('health-test-2');
      
      const health = await service.healthCheck();
      
      expect(health.status).toBe('healthy');
      expect(health.details.totalOperations).toBe(2);
      expect(health.details.successRate).toBe(100);
    });

    it('should return degraded status for poor success rate', async () => {
      // Execute some operations with failures
      await service.testOperation('health-test');

      try {
        await service.testOperationWithError();
      } catch (error) {
        // Expected error
      }

      const health = await service.healthCheck();

      expect(health.status).toBe('degraded');
      expect(health.details.successRate).toBeCloseTo(50, 1);
    });
  });

  describe('reset', () => {
    it('should reset service state', async () => {
      await service.testOperation('reset-test');
      
      let stats = service.getStatistics();
      expect(stats.totalOperations).toBe(1);
      
      service.reset();
      
      stats = service.getStatistics();
      expect(stats.totalOperations).toBe(0);
    });
  });

  describe('configuration', () => {
    it('should use default configuration when none provided', () => {
      const defaultService = new TestService();
      const health = defaultService.healthCheck();
      
      expect(health).resolves.toBeDefined();
    });

    it('should respect custom configuration', () => {
      const customService = new TestService({
        enableCaching: false,
        enableMetrics: false,
        timeout: 1000
      });
      
      expect(customService).toBeDefined();
    });
  });

  describe('error handling', () => {
    it('should handle timeout errors', async () => {
      const timeoutService = new TestService({ timeout: 1 }); // 1ms timeout
      
      await expect(
        timeoutService.executeOperation(
          'timeoutTest',
          async () => {
            await new Promise(resolve => setTimeout(resolve, 100)); // 100ms delay
            return 'should not reach here';
          }
        )
      ).rejects.toThrow('Operation timed out after 1ms');
    });

    it('should handle non-Error objects', async () => {
      const errorService = new TestService();
      
      await expect(
        errorService.executeOperation(
          'stringErrorTest',
          async () => {
            throw 'String error';
          }
        )
      ).rejects.toBe('String error');
    });
  });
});
