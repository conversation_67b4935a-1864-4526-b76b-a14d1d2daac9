/**
 * Graph API Server
 * 
 * Main entry point for the Knowledge Graph Visualizer Graph API microservice.
 * This server implements the Strangler Fig pattern to gradually replace
 * the legacy monolithic system.
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import { config } from 'dotenv';
import { join } from 'path';
import neo4j, { Driver } from 'neo4j-driver';

// Load environment variables
config({ path: join(__dirname, '../../../.env') });

// Import services and controllers
import { ControllerFactory } from './controllers/ControllerFactory';
import { GraphService } from './services/GraphService';
import { AnalysisService } from './services/AnalysisService';
import { HealthService } from './services/HealthService';
import { FeatureFlagsService } from './services/FeatureFlagsService';
import { CompatibilityService } from './services/CompatibilityService';
import { FeatureFlags } from '@kg-visualizer/shared';
import { initializeConfig } from './config';

const app = express();
const port = process.env.NEW_PORT || process.env.GRAPH_API_PORT || 3003;

// Global variables
let driver: Driver;
let server: any;

/**
 * Initialize Neo4j connection with retry logic
 */
const initNeo4j = async (retries = 5, delay = 2000): Promise<Driver> => {
    for (let i = 0; i < retries; i++) {
        try {
            const neo4jDriver = neo4j.driver(
                process.env.NEO4J_URI || 'bolt://localhost:7687',
                neo4j.auth.basic(
                    process.env.NEO4J_USER || process.env.NEO4J_USERNAME || 'neo4j',
                    process.env.NEO4J_PASSWORD || 'password'
                )
            );
            
            await neo4jDriver.verifyConnectivity();
            console.log('✅ Neo4j connection established');
            return neo4jDriver;
        } catch (error: any) {
            console.error(`Failed to connect to Neo4j (attempt ${i + 1}/${retries}):`, error.message);
            if (i < retries - 1) {
                await new Promise(resolve => setTimeout(resolve, delay));
            } else {
                throw new Error('Failed to connect to Neo4j after multiple attempts');
            }
        }
    }
    throw new Error('Failed to connect to Neo4j');
};

/**
 * Setup middleware
 */
const setupMiddleware = () => {
    // Security middleware
    app.use(helmet({
        contentSecurityPolicy: {
            directives: {
                defaultSrc: ["'self'"],
                styleSrc: ["'self'", "'unsafe-inline'"],
                scriptSrc: ["'self'"],
                imgSrc: ["'self'", "data:", "https:"],
            },
        },
    }));

    // Compression
    app.use(compression());

    // CORS
    app.use(cors({
        origin: [
            'http://localhost:3000',
            'http://localhost:3001',
            'http://localhost:3002',
            'http://localhost:5173',
            process.env.FRONTEND_URL || ''
        ].filter(url => url && url.length > 0),
        credentials: true
    }));

    // Rate limiting
    const limiter = rateLimit({
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 1000, // Limit each IP to 1000 requests per windowMs
        message: 'Too many requests from this IP, please try again later.',
        standardHeaders: true,
        legacyHeaders: false,
    });
    app.use(limiter);

    // Body parsing
    app.use(express.json({ limit: '10mb' }));
    app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Logging
    app.use(morgan('combined'));

    console.log('✅ Middleware configured');
};

/**
 * Setup routes and controllers
 */
const setupRoutes = async () => {
    try {
        // Initialize configuration
        await initializeConfig();

        // Create services
        const database = process.env.NEO4J_DATABASE || 'neo4j';
        const graphService = new GraphService(driver, database);
        const analysisService = new AnalysisService(driver, database);
        const healthService = new HealthService(driver);
        // Create feature flags with default values matching the interface
        const defaultFeatureFlags: FeatureFlags = {
            NEW_CONTROLLER_LAYER: true,
            NEW_SERVICE_LAYER: true,
            NEW_REPOSITORY_PATTERN: true,
            NEW_MIDDLEWARE_STACK: true,
            NEW_CHAT_SERVICE: false,
            CIRCUIT_BREAKER_ENABLED: true,
            DUAL_EXECUTION_MODE: false,
            PERFORMANCE_MONITORING: true,
            AUTOMATIC_ROLLBACK: true,
            TRAFFIC_PERCENTAGE_NEW_API: 100,
            DEBUG_MODE: process.env.NODE_ENV === 'development',
            VERBOSE_LOGGING: process.env.NODE_ENV === 'development',
            MIGRATION_METRICS: true
        };

        const featureFlagsService = new FeatureFlagsService(defaultFeatureFlags);
        const compatibilityService = new CompatibilityService(
            process.env.LEGACY_BASE_URL || 'http://localhost:3002',
            defaultFeatureFlags
        );

        // Create controller factory with dependencies
        const controllerFactory = new ControllerFactory({
            graphService,
            analysisService,
            healthService,
            featureFlagsService,
            compatibilityService,
            featureFlags: defaultFeatureFlags,
            legacyApiUrl: process.env.LEGACY_BASE_URL || 'http://localhost:3002'
        });

        // Legacy compatibility endpoints (must be before router registration)
        app.get('/health', async (_req, res) => {
            // For legacy compatibility, return 404 like the legacy system
            res.status(404).json({
                error: {
                    message: "Not Found",
                    stack: "Error: Not Found"
                }
            });
        });

        app.get('/api/health', (_req, res) => {
            // Optimized for performance - match legacy system behavior exactly
            // No database checks to achieve sub-100ms response times
            res.json({
                status: 'ok',
                timestamp: new Date().toISOString(),
                migrationPhase: {
                    backend: {
                        enabled: 4,
                        total: 4,
                        percentage: 100
                    },
                    services: {
                        chatService: defaultFeatureFlags.NEW_CHAT_SERVICE,
                        circuitBreaker: defaultFeatureFlags.CIRCUIT_BREAKER_ENABLED
                    },
                    traffic: {
                        api: defaultFeatureFlags.TRAFFIC_PERCENTAGE_NEW_API
                    }
                }
            });
        });

        // Comprehensive health check endpoint for monitoring (separate from legacy /api/health)
        app.get('/api/health/detailed', async (_req, res) => {
            try {
                const dbCheck = await healthService.checkDatabase();
                const isHealthy = dbCheck.status === 'pass';

                res.status(isHealthy ? 200 : 503).json({
                    status: isHealthy ? 'healthy' : 'unhealthy',
                    timestamp: new Date().toISOString(),
                    uptime: process.uptime(),
                    version: process.env.npm_package_version || '1.0.0',
                    checks: {
                        database: dbCheck
                    }
                });
            } catch (error) {
                res.status(503).json({
                    status: 'unhealthy',
                    timestamp: new Date().toISOString(),
                    error: 'Health check failed'
                });
            }
        });

        // Create and register routes (after legacy compatibility routes)
        const router = controllerFactory.createRouter();
        app.use('/api', router);

        // Metrics endpoint
        app.get('/metrics', (_req, res) => {
            res.json({
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                version: process.env.npm_package_version || '1.0.0'
            });
        });

        console.log('✅ Routes configured');
    } catch (error) {
        console.error('❌ Failed to setup routes:', error);
        throw error;
    }
};

/**
 * Setup error handling
 */
const setupErrorHandling = () => {
    // 404 handler
    app.use((req, _res, next) => {
        const error = new Error(`Not Found: ${req.method} ${req.path}`);
        (error as any).status = 404;
        next(error);
    });

    // Global error handler
    app.use((err: any, _req: express.Request, res: express.Response, _next: express.NextFunction) => {
        console.error('Error:', err);

        const status = err.status || 500;
        const message = err.message || 'Internal Server Error';

        // Don't expose stack traces in production
        const error = process.env.NODE_ENV === 'production'
            ? { message, status }
            : { message, status, stack: err.stack };

        res.status(status).json({ error });
    });

    console.log('✅ Error handling configured');
};

/**
 * Start the server
 */
const startServer = () => {
    server = app.listen(port, () => {
        console.log('🚀 Graph API Server started');
        console.log(`📍 Server running on port ${port}`);
        console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
        console.log(`📊 Health check: http://localhost:${port}/health`);
        console.log(`📚 API docs: http://localhost:${port}/api`);
    });

    server.on('error', (error: any) => {
        if (error.code === 'EADDRINUSE') {
            console.error(`❌ Port ${port} is already in use`);
        } else {
            console.error('❌ Server error:', error);
        }
        process.exit(1);
    });
};

/**
 * Graceful shutdown
 */
const shutdown = async () => {
    console.log('🛑 Shutting down gracefully...');
    try {
        if (driver) {
            await driver.close();
            console.log('✅ Neo4j connection closed');
        }
        if (server) {
            server.close(() => {
                console.log('✅ HTTP server closed');
                process.exit(0);
            });
        } else {
            process.exit(0);
        }
    } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
    }
};

/**
 * Main initialization function
 */
const main = async () => {
    try {
        console.log('🚀 Starting Knowledge Graph API Server...');
        
        // Initialize Neo4j connection
        driver = await initNeo4j();
        
        // Setup application
        setupMiddleware();
        await setupRoutes();
        setupErrorHandling();
        
        // Start server
        startServer();
        
    } catch (error) {
        console.error('❌ Failed to start server:', error);
        process.exit(1);
    }
};

// Handle process signals
process.on('SIGTERM', shutdown);
process.on('SIGINT', shutdown);
process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    shutdown();
});
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    shutdown();
});

// Start the application
if (require.main === module) {
    main();
}

export { app, driver };
