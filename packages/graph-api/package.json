{"name": "@kg-visualizer/graph-api", "version": "1.0.0", "description": "Graph API service for Knowledge Graph Visualizer", "main": "dist/server.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "dev": "nodemon --exec ts-node src/server.ts", "start": "node dist/server.js", "start:production": "NODE_ENV=production node dist/server.js", "clean": "rm -rf dist", "clean:build": "npm run clean && npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "migration:legacy": "node ../../360t-kg-api/server.js"}, "dependencies": {"@kg-visualizer/shared": "^1.0.0", "@types/compression": "^1.8.1", "@types/jsonwebtoken": "^9.0.10", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.3.0", "express": "^4.18.0", "express-rate-limit": "^6.11.2", "helmet": "^7.2.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "neo4j-driver": "^5.12.0", "swagger-ui-express": "^5.0.0", "yamljs": "^0.3.0"}, "devDependencies": {"@types/cors": "^2.8.0", "@types/express": "^4.17.0", "@types/jest": "^29.5.0", "@types/morgan": "^1.9.0", "@types/node": "^20.0.0", "@types/swagger-ui-express": "^4.1.0", "jest": "^29.5.0", "nodemon": "^3.0.0", "supertest": "^6.3.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.1.0"}, "files": ["dist", "src"]}