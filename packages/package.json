{"name": "@kg-visualizer/monorepo", "version": "1.0.0", "description": "Knowledge Graph Visualizer - Monorepo", "private": true, "workspaces": ["shared", "graph-api", "web-ui", "chat-service"], "scripts": {"install:all": "npm install && npm run install:packages", "install:packages": "npm install --workspaces", "build": "npm run build --workspaces", "build:shared": "npm run build --workspace=shared", "build:api": "npm run build --workspace=graph-api", "build:ui": "npm run build --workspace=web-ui", "build:chat": "npm run build --workspace=chat-service", "dev": "concurrently \"npm run dev --workspace=graph-api\" \"npm run dev --workspace=web-ui\"", "dev:api": "npm run dev --workspace=graph-api", "dev:ui": "npm run dev --workspace=web-ui", "dev:chat": "npm run dev --workspace=chat-service", "test": "npm run test --workspaces", "test:unit": "npm run test:unit --workspaces", "test:integration": "npm run test:integration --workspaces", "test:e2e": "npm run test:e2e --workspace=web-ui", "lint": "npm run lint --workspaces", "lint:fix": "npm run lint:fix --workspaces", "type-check": "npm run type-check --workspaces", "clean": "npm run clean --workspaces && rm -rf node_modules", "clean:build": "npm run clean:build --workspaces", "start": "npm run start --workspace=graph-api", "start:production": "concurrently \"npm run start:production --workspace=graph-api\" \"npm run start:production --workspace=chat-service\"", "migration:baseline": "node ../tools/scripts/performance-baseline.cjs", "migration:monitor": "node ../tools/monitoring/monitor.cjs", "migration:rollback": "../tools/scripts/emergency-rollback.sh", "migration:health": "../tools/scripts/migration-safety-check.sh health", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.2.0", "eslint": "^8.45.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.0.0", "typescript": "^5.1.0", "ts-jest": "^29.1.0", "jest": "^29.5.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/azarab79/KnowledgeGraphVisualizer.git"}, "keywords": ["knowledge-graph", "neo4j", "visualization", "react", "nodejs", "<PERSON><PERSON><PERSON>", "microservices"], "author": "Knowledge Graph Team", "license": "MIT"}