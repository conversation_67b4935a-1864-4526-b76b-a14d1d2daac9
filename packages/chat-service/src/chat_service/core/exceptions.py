"""
Exception Handling

This module defines custom exceptions for the chat service with
proper error codes, messages, and HTTP status codes.
"""

from typing import Any, Dict, Optional


class ChatServiceException(Exception):
    """
    Base exception for chat service errors.
    
    All custom exceptions should inherit from this class to ensure
    consistent error handling and logging.
    """
    
    def __init__(
        self,
        message: str,
        error_code: str = "CHAT_SERVICE_ERROR",
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Initialize chat service exception.
        
        Args:
            message: Human-readable error message
            error_code: Machine-readable error code
            status_code: HTTP status code
            details: Additional error details
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}


class ValidationError(ChatServiceException):
    """Exception raised for validation errors."""
    
    def __init__(
        self,
        message: str = "Validation failed",
        field: str = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        error_details = details or {}
        if field:
            error_details["field"] = field
            
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            status_code=400,
            details=error_details
        )


class AuthenticationError(ChatServiceException):
    """Exception raised for authentication errors."""
    
    def __init__(
        self,
        message: str = "Authentication failed",
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            status_code=401,
            details=details
        )


class AuthorizationError(ChatServiceException):
    """Exception raised for authorization errors."""
    
    def __init__(
        self,
        message: str = "Access denied",
        required_permission: str = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        error_details = details or {}
        if required_permission:
            error_details["required_permission"] = required_permission
            
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            status_code=403,
            details=error_details
        )


class NotFoundError(ChatServiceException):
    """Exception raised when a resource is not found."""
    
    def __init__(
        self,
        message: str = "Resource not found",
        resource_type: str = None,
        resource_id: str = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        error_details = details or {}
        if resource_type:
            error_details["resource_type"] = resource_type
        if resource_id:
            error_details["resource_id"] = resource_id
            
        super().__init__(
            message=message,
            error_code="NOT_FOUND_ERROR",
            status_code=404,
            details=error_details
        )


class ConflictError(ChatServiceException):
    """Exception raised for resource conflicts."""
    
    def __init__(
        self,
        message: str = "Resource conflict",
        resource_type: str = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        error_details = details or {}
        if resource_type:
            error_details["resource_type"] = resource_type
            
        super().__init__(
            message=message,
            error_code="CONFLICT_ERROR",
            status_code=409,
            details=error_details
        )


class RateLimitError(ChatServiceException):
    """Exception raised when rate limit is exceeded."""
    
    def __init__(
        self,
        message: str = "Rate limit exceeded",
        retry_after: int = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        error_details = details or {}
        if retry_after:
            error_details["retry_after"] = retry_after
            
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_ERROR",
            status_code=429,
            details=error_details
        )


class ExternalServiceError(ChatServiceException):
    """Exception raised for external service errors."""
    
    def __init__(
        self,
        message: str = "External service error",
        service_name: str = None,
        service_error: str = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        error_details = details or {}
        if service_name:
            error_details["service_name"] = service_name
        if service_error:
            error_details["service_error"] = service_error
            
        super().__init__(
            message=message,
            error_code="EXTERNAL_SERVICE_ERROR",
            status_code=502,
            details=error_details
        )


class LLMProviderError(ExternalServiceError):
    """Exception raised for LLM provider errors."""
    
    def __init__(
        self,
        message: str = "LLM provider error",
        provider: str = None,
        model: str = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        error_details = details or {}
        if provider:
            error_details["provider"] = provider
        if model:
            error_details["model"] = model
            
        super().__init__(
            message=message,
            service_name="llm_provider",
            service_error=message,
            details=error_details
        )
        self.error_code = "LLM_PROVIDER_ERROR"


class KnowledgeGraphError(ExternalServiceError):
    """Exception raised for knowledge graph errors."""
    
    def __init__(
        self,
        message: str = "Knowledge graph error",
        query: str = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        error_details = details or {}
        if query:
            error_details["query"] = query
            
        super().__init__(
            message=message,
            service_name="knowledge_graph",
            service_error=message,
            details=error_details
        )
        self.error_code = "KNOWLEDGE_GRAPH_ERROR"


class DatabaseError(ChatServiceException):
    """Exception raised for database errors."""
    
    def __init__(
        self,
        message: str = "Database error",
        operation: str = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        error_details = details or {}
        if operation:
            error_details["operation"] = operation
            
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            status_code=500,
            details=error_details
        )


class ConfigurationError(ChatServiceException):
    """Exception raised for configuration errors."""
    
    def __init__(
        self,
        message: str = "Configuration error",
        config_key: str = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        error_details = details or {}
        if config_key:
            error_details["config_key"] = config_key
            
        super().__init__(
            message=message,
            error_code="CONFIGURATION_ERROR",
            status_code=500,
            details=error_details
        )


class CircuitBreakerError(ChatServiceException):
    """Exception raised when circuit breaker is open."""
    
    def __init__(
        self,
        message: str = "Service temporarily unavailable",
        service_name: str = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        error_details = details or {}
        if service_name:
            error_details["service_name"] = service_name
            
        super().__init__(
            message=message,
            error_code="CIRCUIT_BREAKER_ERROR",
            status_code=503,
            details=error_details
        )


class TimeoutError(ChatServiceException):
    """Exception raised for timeout errors."""
    
    def __init__(
        self,
        message: str = "Operation timed out",
        timeout_seconds: float = None,
        operation: str = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        error_details = details or {}
        if timeout_seconds:
            error_details["timeout_seconds"] = timeout_seconds
        if operation:
            error_details["operation"] = operation
            
        super().__init__(
            message=message,
            error_code="TIMEOUT_ERROR",
            status_code=504,
            details=error_details
        )
