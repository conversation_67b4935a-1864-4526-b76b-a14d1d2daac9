"""
Configuration Management

This module handles all configuration settings for the chat service,
including environment variables, validation, and default values.
"""

import os
from functools import lru_cache
from typing import List, Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    Application settings with environment variable support.
    
    All settings can be overridden using environment variables with
    the CHAT_SERVICE_ prefix (e.g., CHAT_SERVICE_HOST=0.0.0.0).
    """
    
    # Server settings
    host: str = Field(default="127.0.0.1", description="Server host")
    port: int = Field(default=8001, description="Server port")
    environment: str = Field(default="development", description="Environment name")
    debug: bool = Field(default=False, description="Debug mode")
    
    # Logging settings
    log_level: str = Field(default="INFO", description="Log level")
    log_format: str = Field(default="json", description="Log format (json|console)")
    
    # Security settings
    secret_key: str = Field(default="dev-secret-key", description="Secret key for JWT")
    algorithm: str = Field(default="HS256", description="JWT algorithm")
    access_token_expire_minutes: int = Field(default=30, description="Token expiry")
    
    # CORS settings
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:3001"],
        description="Allowed CORS origins"
    )
    allowed_hosts: List[str] = Field(
        default=["localhost", "127.0.0.1"],
        description="Allowed hosts"
    )
    
    # Rate limiting
    enable_rate_limiting: bool = Field(default=True, description="Enable rate limiting")
    rate_limit_calls: int = Field(default=100, description="Rate limit calls per period")
    rate_limit_period: int = Field(default=60, description="Rate limit period in seconds")
    
    # Database settings
    neo4j_uri: str = Field(default="bolt://localhost:7687", description="Neo4j URI")
    neo4j_user: str = Field(default="neo4j", description="Neo4j username")
    neo4j_password: str = Field(default="password", description="Neo4j password")
    neo4j_database: str = Field(default="neo4j", description="Neo4j database name")
    
    # Redis settings (for caching and rate limiting)
    redis_url: str = Field(default="redis://localhost:6379", description="Redis URL")
    redis_password: Optional[str] = Field(default=None, description="Redis password")
    redis_db: int = Field(default=0, description="Redis database number")
    
    # LLM Provider settings
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API key")
    openai_model: str = Field(default="gpt-3.5-turbo", description="OpenAI model")
    openai_max_tokens: int = Field(default=1000, description="OpenAI max tokens")
    openai_temperature: float = Field(default=0.7, description="OpenAI temperature")
    
    groq_api_key: Optional[str] = Field(default=None, description="Groq API key")
    groq_model: str = Field(default="mixtral-8x7b-32768", description="Groq model")
    groq_max_tokens: int = Field(default=1000, description="Groq max tokens")
    groq_temperature: float = Field(default=0.7, description="Groq temperature")
    
    google_api_key: Optional[str] = Field(default=None, description="Google API key")
    google_model: str = Field(default="gemini-pro", description="Google model")
    google_max_tokens: int = Field(default=1000, description="Google max tokens")
    google_temperature: float = Field(default=0.7, description="Google temperature")
    
    # Default LLM provider
    default_llm_provider: str = Field(default="openai", description="Default LLM provider")
    
    # Embedding settings
    embedding_model: str = Field(
        default="sentence-transformers/all-MiniLM-L6-v2",
        description="Embedding model"
    )
    embedding_dimension: int = Field(default=384, description="Embedding dimension")
    
    # Chat settings
    max_conversation_length: int = Field(
        default=50,
        description="Maximum conversation length"
    )
    max_context_tokens: int = Field(
        default=4000,
        description="Maximum context tokens"
    )
    enable_streaming: bool = Field(default=True, description="Enable streaming responses")
    
    # Knowledge graph settings
    max_search_results: int = Field(default=10, description="Maximum search results")
    similarity_threshold: float = Field(
        default=0.7,
        description="Similarity threshold for search"
    )
    enable_graph_context: bool = Field(
        default=True,
        description="Enable graph context in responses"
    )
    
    # Circuit breaker settings
    circuit_breaker_failure_threshold: int = Field(
        default=5,
        description="Circuit breaker failure threshold"
    )
    circuit_breaker_recovery_timeout: int = Field(
        default=60,
        description="Circuit breaker recovery timeout in seconds"
    )
    circuit_breaker_expected_exception: str = Field(
        default="Exception",
        description="Expected exception for circuit breaker"
    )
    
    # Monitoring settings
    enable_metrics: bool = Field(default=True, description="Enable Prometheus metrics")
    metrics_port: int = Field(default=8002, description="Metrics server port")
    
    # Health check settings
    health_check_timeout: int = Field(
        default=30,
        description="Health check timeout in seconds"
    )
    
    @validator("environment")
    def validate_environment(cls, v: str) -> str:
        """Validate environment setting."""
        allowed = ["development", "staging", "production"]
        if v not in allowed:
            raise ValueError(f"Environment must be one of {allowed}")
        return v
    
    @validator("log_level")
    def validate_log_level(cls, v: str) -> str:
        """Validate log level setting."""
        allowed = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed:
            raise ValueError(f"Log level must be one of {allowed}")
        return v.upper()
    
    @validator("log_format")
    def validate_log_format(cls, v: str) -> str:
        """Validate log format setting."""
        allowed = ["json", "console"]
        if v not in allowed:
            raise ValueError(f"Log format must be one of {allowed}")
        return v
    
    @validator("default_llm_provider")
    def validate_llm_provider(cls, v: str) -> str:
        """Validate LLM provider setting."""
        allowed = ["openai", "groq", "google"]
        if v not in allowed:
            raise ValueError(f"LLM provider must be one of {allowed}")
        return v
    
    @validator("openai_temperature", "groq_temperature", "google_temperature")
    def validate_temperature(cls, v: float) -> float:
        """Validate temperature setting."""
        if not 0.0 <= v <= 2.0:
            raise ValueError("Temperature must be between 0.0 and 2.0")
        return v
    
    @validator("similarity_threshold")
    def validate_similarity_threshold(cls, v: float) -> float:
        """Validate similarity threshold."""
        if not 0.0 <= v <= 1.0:
            raise ValueError("Similarity threshold must be between 0.0 and 1.0")
        return v
    
    class Config:
        """Pydantic configuration."""
        env_prefix = "CHAT_SERVICE_"
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """
    Get cached application settings.
    
    Returns:
        Settings: Application settings instance
    """
    return Settings()


def get_database_url() -> str:
    """
    Get the complete database URL.
    
    Returns:
        str: Complete Neo4j database URL
    """
    settings = get_settings()
    return f"{settings.neo4j_uri}"


def get_redis_url() -> str:
    """
    Get the complete Redis URL.
    
    Returns:
        str: Complete Redis URL
    """
    settings = get_settings()
    if settings.redis_password:
        # Extract host and port from redis_url
        url_parts = settings.redis_url.replace("redis://", "").split(":")
        host = url_parts[0]
        port = url_parts[1] if len(url_parts) > 1 else "6379"
        return f"redis://:{settings.redis_password}@{host}:{port}/{settings.redis_db}"
    else:
        return f"{settings.redis_url}/{settings.redis_db}"


def is_development() -> bool:
    """
    Check if running in development environment.
    
    Returns:
        bool: True if development environment
    """
    return get_settings().environment == "development"


def is_production() -> bool:
    """
    Check if running in production environment.
    
    Returns:
        bool: True if production environment
    """
    return get_settings().environment == "production"
