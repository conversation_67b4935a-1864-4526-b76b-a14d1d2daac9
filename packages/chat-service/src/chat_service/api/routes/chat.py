"""
Chat API Routes

This module provides REST API endpoints for chat functionality,
including message handling, conversation management, and streaming responses.
"""

from typing import List, Optional
from uuid import uuid4

import structlog
from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field

from chat_service.core.exceptions import ValidationError, NotFoundError
from chat_service.services.dependencies import get_chat_service
from chat_service.services.chat_service import ChatService


router = APIRouter()
logger = structlog.get_logger("api.chat")


class ChatMessage(BaseModel):
    """Chat message model."""
    id: str = Field(..., description="Unique message identifier")
    role: str = Field(..., description="Message role (user, assistant, system)")
    content: str = Field(..., description="Message content")
    timestamp: str = Field(..., description="Message timestamp")
    metadata: Optional[dict] = Field(default=None, description="Additional metadata")


class ChatRequest(BaseModel):
    """Chat request model."""
    message: str = Field(..., min_length=1, max_length=10000, description="User message")
    conversation_id: Optional[str] = Field(default=None, description="Conversation ID")
    context: Optional[dict] = Field(default=None, description="Additional context")
    options: Optional[dict] = Field(default=None, description="Chat options")


class ChatResponse(BaseModel):
    """Chat response model."""
    message: ChatMessage = Field(..., description="Assistant response message")
    conversation_id: str = Field(..., description="Conversation ID")
    metadata: Optional[dict] = Field(default=None, description="Response metadata")


class ConversationSummary(BaseModel):
    """Conversation summary model."""
    id: str = Field(..., description="Conversation ID")
    title: Optional[str] = Field(default=None, description="Conversation title")
    message_count: int = Field(..., description="Number of messages")
    created_at: str = Field(..., description="Creation timestamp")
    updated_at: str = Field(..., description="Last update timestamp")
    metadata: Optional[dict] = Field(default=None, description="Conversation metadata")


class ConversationDetail(BaseModel):
    """Detailed conversation model."""
    id: str = Field(..., description="Conversation ID")
    title: Optional[str] = Field(default=None, description="Conversation title")
    messages: List[ChatMessage] = Field(..., description="Conversation messages")
    created_at: str = Field(..., description="Creation timestamp")
    updated_at: str = Field(..., description="Last update timestamp")
    metadata: Optional[dict] = Field(default=None, description="Conversation metadata")


@router.post("/message", response_model=ChatResponse)
async def send_message(
    request: ChatRequest,
    http_request: Request,
    chat_service: ChatService = Depends(get_chat_service)
) -> ChatResponse:
    """
    Send a chat message and get a response.
    
    Args:
        request: Chat request containing message and options
        http_request: FastAPI request object
        chat_service: Chat service dependency
        
    Returns:
        ChatResponse: Assistant response
        
    Raises:
        HTTPException: If message processing fails
    """
    request_id = getattr(http_request.state, "request_id", str(uuid4()))
    
    logger.info(
        "Processing chat message",
        request_id=request_id,
        conversation_id=request.conversation_id,
        message_length=len(request.message)
    )
    
    try:
        # Process the message
        response = await chat_service.process_message(
            message=request.message,
            conversation_id=request.conversation_id,
            context=request.context or {},
            options=request.options or {}
        )
        
        logger.info(
            "Chat message processed successfully",
            request_id=request_id,
            conversation_id=response["conversation_id"],
            response_length=len(response["message"]["content"])
        )
        
        return ChatResponse(**response)
        
    except ValidationError as e:
        logger.warning(
            "Chat message validation failed",
            request_id=request_id,
            error=str(e)
        )
        raise HTTPException(status_code=400, detail=str(e))
        
    except Exception as e:
        logger.error(
            "Chat message processing failed",
            request_id=request_id,
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(status_code=500, detail="Failed to process message")


@router.post("/stream")
async def stream_message(
    request: ChatRequest,
    http_request: Request,
    chat_service: ChatService = Depends(get_chat_service)
) -> StreamingResponse:
    """
    Send a chat message and get a streaming response.
    
    Args:
        request: Chat request containing message and options
        http_request: FastAPI request object
        chat_service: Chat service dependency
        
    Returns:
        StreamingResponse: Streaming assistant response
        
    Raises:
        HTTPException: If message processing fails
    """
    request_id = getattr(http_request.state, "request_id", str(uuid4()))
    
    logger.info(
        "Processing streaming chat message",
        request_id=request_id,
        conversation_id=request.conversation_id,
        message_length=len(request.message)
    )
    
    try:
        # Create streaming response
        async def generate_response():
            async for chunk in chat_service.stream_message(
                message=request.message,
                conversation_id=request.conversation_id,
                context=request.context or {},
                options=request.options or {}
            ):
                yield f"data: {chunk}\n\n"
        
        return StreamingResponse(
            generate_response(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Request-ID": request_id
            }
        )
        
    except ValidationError as e:
        logger.warning(
            "Streaming chat message validation failed",
            request_id=request_id,
            error=str(e)
        )
        raise HTTPException(status_code=400, detail=str(e))
        
    except Exception as e:
        logger.error(
            "Streaming chat message processing failed",
            request_id=request_id,
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(status_code=500, detail="Failed to process streaming message")


@router.get("/conversations", response_model=List[ConversationSummary])
async def list_conversations(
    limit: int = 20,
    offset: int = 0,
    chat_service: ChatService = Depends(get_chat_service)
) -> List[ConversationSummary]:
    """
    List user conversations.
    
    Args:
        limit: Maximum number of conversations to return
        offset: Number of conversations to skip
        chat_service: Chat service dependency
        
    Returns:
        List[ConversationSummary]: List of conversation summaries
    """
    try:
        conversations = await chat_service.list_conversations(
            limit=limit,
            offset=offset
        )
        
        return [ConversationSummary(**conv) for conv in conversations]
        
    except Exception as e:
        logger.error(
            "Failed to list conversations",
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(status_code=500, detail="Failed to list conversations")


@router.get("/conversations/{conversation_id}", response_model=ConversationDetail)
async def get_conversation(
    conversation_id: str,
    chat_service: ChatService = Depends(get_chat_service)
) -> ConversationDetail:
    """
    Get conversation details.
    
    Args:
        conversation_id: Conversation identifier
        chat_service: Chat service dependency
        
    Returns:
        ConversationDetail: Detailed conversation information
        
    Raises:
        HTTPException: If conversation not found
    """
    try:
        conversation = await chat_service.get_conversation(conversation_id)
        
        if not conversation:
            raise NotFoundError(
                message="Conversation not found",
                resource_type="conversation",
                resource_id=conversation_id
            )
        
        return ConversationDetail(**conversation)
        
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
        
    except Exception as e:
        logger.error(
            "Failed to get conversation",
            conversation_id=conversation_id,
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(status_code=500, detail="Failed to get conversation")


@router.delete("/conversations/{conversation_id}")
async def delete_conversation(
    conversation_id: str,
    chat_service: ChatService = Depends(get_chat_service)
) -> dict:
    """
    Delete a conversation.
    
    Args:
        conversation_id: Conversation identifier
        chat_service: Chat service dependency
        
    Returns:
        dict: Deletion confirmation
        
    Raises:
        HTTPException: If conversation not found or deletion fails
    """
    try:
        success = await chat_service.delete_conversation(conversation_id)
        
        if not success:
            raise NotFoundError(
                message="Conversation not found",
                resource_type="conversation",
                resource_id=conversation_id
            )
        
        return {"message": "Conversation deleted successfully"}
        
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
        
    except Exception as e:
        logger.error(
            "Failed to delete conversation",
            conversation_id=conversation_id,
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(status_code=500, detail="Failed to delete conversation")


class TitleUpdateRequest(BaseModel):
    """Title update request model."""
    title: str = Field(..., min_length=1, max_length=200, description="New conversation title")


@router.post("/conversations/{conversation_id}/title")
async def update_conversation_title(
    conversation_id: str,
    request: TitleUpdateRequest,
    chat_service: ChatService = Depends(get_chat_service)
) -> dict:
    """
    Update conversation title.
    
    Args:
        conversation_id: Conversation identifier
        title: New conversation title
        chat_service: Chat service dependency
        
    Returns:
        dict: Update confirmation
        
    Raises:
        HTTPException: If conversation not found or update fails
    """
    try:
        success = await chat_service.update_conversation_title(
            conversation_id=conversation_id,
            title=request.title
        )
        
        if not success:
            raise NotFoundError(
                message="Conversation not found",
                resource_type="conversation",
                resource_id=conversation_id
            )
        
        return {"message": "Conversation title updated successfully"}
        
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
        
    except Exception as e:
        logger.error(
            "Failed to update conversation title",
            conversation_id=conversation_id,
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(status_code=500, detail="Failed to update conversation title")
