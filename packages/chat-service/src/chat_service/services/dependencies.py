"""
Service Dependencies

This module provides dependency injection for FastAPI routes,
managing service instances and their lifecycles.
"""

import os
from functools import lru_cache
from typing import Optional

from chat_service.core.config import get_settings
from chat_service.services.chat_service import ChatService
from chat_service.services.knowledge_graph_service import KnowledgeGraphService
from chat_service.services.llm_service import LLMService
from chat_service.services.resilient_chat_service import ResilientChatService
from chat_service.services.fallback_service import (
    LegacyPythonScriptService,
    FallbackChatService
)
from chat_service.services.mock_llm_service import MockLLMService


# Global service instances
_chat_service: Optional[ResilientChatService] = None
_kg_service: Optional[KnowledgeGraphService] = None
_llm_service: Optional[LLMService] = None
_legacy_service: Optional[LegacyPythonScriptService] = None
_fallback_service: Optional[FallbackChatService] = None


@lru_cache()
def get_chat_service() -> ResilientChatService:
    """
    Get or create resilient chat service instance.

    Returns:
        ResilientChatService: Resilient chat service instance
    """
    global _chat_service, _legacy_service, _fallback_service

    if _chat_service is None:
        settings = get_settings()

        # Create LLM service
        llm_service = get_llm_service()

        # Create knowledge graph service
        kg_service = get_kg_service()

        # Create primary chat service
        primary_service = ChatService(
            llm_service=llm_service,
            kg_service=kg_service,
            settings=settings
        )

        # Create legacy script service (fallback)
        # Note: In production, set the correct path to your legacy script
        legacy_script_path = os.getenv(
            "CHAT_SERVICE_LEGACY_SCRIPT_PATH",
            "/path/to/legacy/chat_script.py"  # Default placeholder
        )

        if os.path.exists(legacy_script_path):
            _legacy_service = LegacyPythonScriptService(
                script_path=legacy_script_path,
                settings=settings
            )

            _fallback_service = FallbackChatService(
                legacy_service=_legacy_service,
                settings=settings
            )
        else:
            # Create a mock fallback service if legacy script doesn't exist
            _fallback_service = FallbackChatService(
                legacy_service=None,  # Will be handled gracefully
                settings=settings
            )

        # Create resilient chat service
        _chat_service = ResilientChatService(
            primary_service=primary_service,
            fallback_service=_fallback_service,
            settings=settings
        )

    return _chat_service


@lru_cache()
def get_kg_service() -> KnowledgeGraphService:
    """
    Get or create knowledge graph service instance.
    
    Returns:
        KnowledgeGraphService: Knowledge graph service instance
    """
    global _kg_service
    
    if _kg_service is None:
        settings = get_settings()
        
        _kg_service = KnowledgeGraphService(
            neo4j_uri=settings.neo4j_uri,
            neo4j_user=settings.neo4j_user,
            neo4j_password=settings.neo4j_password,
            neo4j_database=settings.neo4j_database,
            embedding_model=settings.embedding_model,
            settings=settings
        )
    
    return _kg_service


@lru_cache()
def get_llm_service() -> LLMService:
    """
    Get or create LLM service instance.

    Returns:
        LLMService: LLM service instance (or mock service for testing)
    """
    global _llm_service

    if _llm_service is None:
        settings = get_settings()

        # Check if we have any real API keys
        has_real_keys = any([
            settings.openai_api_key and settings.openai_api_key != "test-key",
            settings.groq_api_key and settings.groq_api_key != "test-key",
            settings.google_api_key and settings.google_api_key != "test-key"
        ])

        if has_real_keys:
            # Use real LLM service
            _llm_service = LLMService(
                default_provider=settings.default_llm_provider,
                openai_api_key=settings.openai_api_key,
                groq_api_key=settings.groq_api_key,
                google_api_key=settings.google_api_key,
                settings=settings
            )
        else:
            # Use mock service for testing/development
            _llm_service = MockLLMService(settings=settings)

    return _llm_service


def reset_services() -> None:
    """
    Reset all service instances.
    
    This is useful for testing or when configuration changes.
    """
    global _chat_service, _kg_service, _llm_service
    
    _chat_service = None
    _kg_service = None
    _llm_service = None
    
    # Clear LRU caches
    get_chat_service.cache_clear()
    get_kg_service.cache_clear()
    get_llm_service.cache_clear()


async def cleanup_services() -> None:
    """
    Cleanup all service instances.
    
    This should be called during application shutdown.
    """
    global _chat_service, _kg_service, _llm_service
    
    if _chat_service:
        await _chat_service.cleanup()
    
    if _kg_service:
        await _kg_service.cleanup()
    
    if _llm_service:
        await _llm_service.cleanup()
    
    reset_services()
