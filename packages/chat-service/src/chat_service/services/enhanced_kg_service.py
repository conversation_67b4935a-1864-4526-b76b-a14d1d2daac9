"""
Enhanced Knowledge Graph Service

This module provides an enhanced knowledge graph service that migrates
functionality from the existing Graphiti Python scripts, including
hybrid search, entity retrieval, and context building.
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, Tuple

import structlog

from chat_service.core.config import Settings
from chat_service.core.exceptions import KnowledgeGraphError, DatabaseError
from chat_service.core.logging import LoggerMixin


class EnhancedKnowledgeGraphService(LoggerMixin):
    """
    Enhanced knowledge graph service with Graphiti integration.
    
    Migrates functionality from the existing Python scripts including:
    - Graphiti hybrid search (vector + BM25 with RRF)
    - Entity and edge retrieval
    - Context building for LLM prompts
    - Group ID filtering
    - Citation tracking
    """
    
    def __init__(
        self,
        neo4j_uri: str,
        neo4j_user: str,
        neo4j_password: str,
        neo4j_database: str = "neo4j",
        openai_api_key: Optional[str] = None,
        settings: Optional[Settings] = None
    ) -> None:
        """
        Initialize enhanced knowledge graph service.
        
        Args:
            neo4j_uri: Neo4j connection URI
            neo4j_user: Neo4j username
            neo4j_password: Neo4j password
            neo4j_database: Neo4j database name
            openai_api_key: OpenAI API key for embeddings
            settings: Application settings
        """
        self.neo4j_uri = neo4j_uri
        self.neo4j_user = neo4j_user
        self.neo4j_password = neo4j_password
        self.neo4j_database = neo4j_database
        self.openai_api_key = openai_api_key
        self.settings = settings
        
        # Graphiti instance (will be initialized lazily)
        self._graphiti_instance = None
        
        # Search configuration
        self.search_config = {
            "edge_count": 4,
            "node_count": 2,
            "group_id_filter": "user_guides",
            "search_mode": "COMBINED_HYBRID_SEARCH_RRF",
            "similarity_threshold": 0.7
        }
        
        self.logger.info(
            "Enhanced knowledge graph service initialized",
            neo4j_uri=neo4j_uri,
            database=neo4j_database,
            has_openai_key=openai_api_key is not None
        )
    
    async def _get_graphiti_instance(self):
        """Get or create Graphiti instance."""
        if self._graphiti_instance is None:
            try:
                # Import Graphiti here to avoid import errors if not installed
                from graphiti_core import Graphiti
                
                # Initialize Graphiti with Neo4j connection
                self._graphiti_instance = Graphiti(
                    uri=self.neo4j_uri,
                    user=self.neo4j_user,
                    password=self.neo4j_password,
                    database=self.neo4j_database
                )
                
                self.logger.info("Graphiti instance created successfully")
                
            except ImportError:
                self.logger.warning("Graphiti not available, falling back to basic Neo4j")
                self._graphiti_instance = None
            except Exception as e:
                self.log_error(e, "_get_graphiti_instance")
                raise DatabaseError(f"Failed to initialize Graphiti: {str(e)}")
        
        return self._graphiti_instance
    
    async def search(
        self,
        query: str,
        limit: int = 10,
        threshold: float = 0.7,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Search the knowledge graph using Graphiti hybrid search.
        
        Args:
            query: Search query
            limit: Maximum number of results
            threshold: Similarity threshold
            filters: Additional filters (including group_id)
            
        Returns:
            Dict[str, Any]: Search results with metadata
        """
        start_time = time.time()
        filters = filters or {}
        
        self.log_method_call(
            "search",
            query_length=len(query),
            limit=limit,
            threshold=threshold,
            filters=filters
        )
        
        try:
            graphiti = await self._get_graphiti_instance()
            
            if graphiti:
                # Use Graphiti search
                results = await self._graphiti_search(
                    graphiti, query, limit, threshold, filters
                )
            else:
                # Fallback to basic Neo4j search
                results = await self._basic_neo4j_search(
                    query, limit, threshold, filters
                )
            
            execution_time = round((time.time() - start_time) * 1000, 2)
            
            result_data = {
                "query": query,
                "results": results["entities"],
                "total_count": len(results["entities"]),
                "execution_time_ms": execution_time,
                "context": results.get("context", ""),
                "citations": results.get("citations", []),
                "search_method": "graphiti" if graphiti else "basic_neo4j"
            }
            
            self.log_method_result(
                "search",
                results_count=len(results["entities"]),
                execution_time_ms=execution_time,
                search_method=result_data["search_method"]
            )
            
            return result_data
            
        except Exception as e:
            self.log_error(e, "search", query=query)
            raise KnowledgeGraphError(f"Search failed: {str(e)}", query=query)
    
    async def _graphiti_search(
        self,
        graphiti,
        query: str,
        limit: int,
        threshold: float,
        filters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Perform search using Graphiti."""
        try:
            # Get group IDs if filtering is requested
            group_ids = []
            if "group_id" in filters:
                group_id_filter = filters["group_id"]
                group_ids = await self._get_matching_group_ids(graphiti, group_id_filter)
            
            # Perform Graphiti search
            search_results = await graphiti.search(
                query=query,
                limit=limit,
                group_ids=group_ids if group_ids else None
            )
            
            # Process results
            entities = []
            context_lines = []
            citations = []
            
            for result in search_results:
                # Extract entity information
                entity = {
                    "id": str(result.uuid),
                    "type": result.__class__.__name__,
                    "properties": {
                        "name": getattr(result, "name", "Unknown"),
                        "summary": getattr(result, "summary", ""),
                        "group_id": getattr(result, "group_id", ""),
                        "created_at": str(getattr(result, "created_at", "")),
                        "valid_at": str(getattr(result, "valid_at", ""))
                    },
                    "labels": [result.__class__.__name__]
                }
                entities.append(entity)
                
                # Build context
                if hasattr(result, "summary") and result.summary:
                    context_lines.append(f"- {result.summary}")
                
                # Add citation
                citations.append({
                    "id": str(result.uuid),
                    "type": result.__class__.__name__,
                    "name": getattr(result, "name", "Unknown"),
                    "group_id": getattr(result, "group_id", "")
                })
            
            context = "\n".join(context_lines) if context_lines else ""
            
            return {
                "entities": entities,
                "context": context,
                "citations": citations
            }
            
        except Exception as e:
            self.log_error(e, "_graphiti_search", query=query)
            raise KnowledgeGraphError(f"Graphiti search failed: {str(e)}")
    
    async def _get_matching_group_ids(self, graphiti, filter_substring: str) -> List[str]:
        """Get group IDs that match the filter substring."""
        try:
            # Query for matching group IDs
            driver = graphiti.driver
            
            query = """
            MATCH (n)
            WHERE n.group_id IS NOT NULL AND n.group_id CONTAINS $filter_substring
            RETURN DISTINCT n.group_id as group_id
            ORDER BY n.group_id
            """
            
            records, _, _ = await driver.execute_query(
                query,
                filter_substring=filter_substring,
                database_=self.neo4j_database,
                routing_="r"
            )
            
            group_ids = [record["group_id"] for record in records]
            
            self.logger.debug(
                "Found matching group IDs",
                filter_substring=filter_substring,
                group_ids=group_ids,
                count=len(group_ids)
            )
            
            return group_ids
            
        except Exception as e:
            self.log_error(e, "_get_matching_group_ids", filter_substring=filter_substring)
            return []
    
    async def _basic_neo4j_search(
        self,
        query: str,
        limit: int,
        threshold: float,
        filters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Fallback search using basic Neo4j queries."""
        try:
            # Import Neo4j driver
            from neo4j import AsyncGraphDatabase
            
            driver = AsyncGraphDatabase.driver(
                self.neo4j_uri,
                auth=(self.neo4j_user, self.neo4j_password)
            )
            
            # Simple text-based search
            cypher_query = """
            MATCH (n)
            WHERE any(prop in keys(n) WHERE toString(n[prop]) CONTAINS $query)
            """
            
            # Add group_id filter if specified
            if "group_id" in filters:
                cypher_query += " AND n.group_id CONTAINS $group_id_filter"
            
            cypher_query += """
            RETURN n, labels(n) as labels, id(n) as node_id
            LIMIT $limit
            """
            
            params = {
                "query": query.lower(),
                "limit": limit
            }
            
            if "group_id" in filters:
                params["group_id_filter"] = filters["group_id"]
            
            async with driver.session(database=self.neo4j_database) as session:
                result = await session.run(cypher_query, **params)
                records = await result.data()
                
                entities = []
                context_lines = []
                citations = []
                
                for record in records:
                    node = record["n"]
                    labels = record["labels"]
                    node_id = record["node_id"]
                    
                    entity = {
                        "id": str(node_id),
                        "type": labels[0] if labels else "Unknown",
                        "properties": dict(node),
                        "labels": labels
                    }
                    entities.append(entity)
                    
                    # Build context from node properties
                    name = node.get("name", node.get("id", "Unknown"))
                    summary = node.get("summary", node.get("description", ""))
                    if summary:
                        context_lines.append(f"- {name}: {summary}")
                    
                    # Add citation
                    citations.append({
                        "id": str(node_id),
                        "type": labels[0] if labels else "Unknown",
                        "name": name,
                        "group_id": node.get("group_id", "")
                    })
                
                context = "\n".join(context_lines) if context_lines else ""
                
                await driver.close()
                
                return {
                    "entities": entities,
                    "context": context,
                    "citations": citations
                }
                
        except Exception as e:
            self.log_error(e, "_basic_neo4j_search", query=query)
            raise KnowledgeGraphError(f"Basic Neo4j search failed: {str(e)}")
    
    async def get_context_for_llm(
        self,
        query: str,
        max_context_length: int = 4000
    ) -> Dict[str, Any]:
        """
        Get formatted context for LLM prompt generation.
        
        Args:
            query: Search query
            max_context_length: Maximum context length
            
        Returns:
            Dict[str, Any]: Formatted context with metadata
        """
        try:
            # Perform search
            search_results = await self.search(query, limit=10)
            
            context = search_results.get("context", "")
            citations = search_results.get("citations", [])
            
            # Truncate context if too long
            if len(context) > max_context_length:
                context = context[:max_context_length] + "..."
            
            # Format context for LLM
            formatted_context = self._format_context_for_llm(context, citations)
            
            return {
                "context": formatted_context,
                "citations": citations,
                "entities_found": len(search_results.get("results", [])),
                "search_query": query,
                "truncated": len(search_results.get("context", "")) > max_context_length
            }
            
        except Exception as e:
            self.log_error(e, "get_context_for_llm", query=query)
            return {
                "context": "",
                "citations": [],
                "entities_found": 0,
                "search_query": query,
                "error": str(e)
            }
    
    def _format_context_for_llm(self, context: str, citations: List[Dict[str, Any]]) -> str:
        """Format context for LLM consumption."""
        if not context:
            return "No relevant information found in the knowledge graph."
        
        formatted = f"**Knowledge Graph Context:**\n\n{context}\n\n"
        
        if citations:
            formatted += "**Sources:**\n"
            for i, citation in enumerate(citations[:5], 1):  # Limit to 5 citations
                name = citation.get("name", "Unknown")
                entity_type = citation.get("type", "Entity")
                formatted += f"{i}. {name} ({entity_type})\n"
        
        return formatted
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check.
        
        Returns:
            Dict[str, Any]: Health check results
        """
        start_time = time.time()
        
        try:
            graphiti = await self._get_graphiti_instance()
            
            if graphiti:
                # Test Graphiti connection
                # Simple query to verify connection
                test_results = await graphiti.search("test", limit=1)
                
                response_time = round((time.time() - start_time) * 1000, 2)
                
                return {
                    "status": "healthy",
                    "response_time_ms": response_time,
                    "database": self.neo4j_database,
                    "uri": self.neo4j_uri,
                    "graphiti_available": True,
                    "test_query_results": len(test_results) if test_results else 0
                }
            else:
                # Test basic Neo4j connection
                from neo4j import AsyncGraphDatabase
                
                driver = AsyncGraphDatabase.driver(
                    self.neo4j_uri,
                    auth=(self.neo4j_user, self.neo4j_password)
                )
                
                async with driver.session(database=self.neo4j_database) as session:
                    await session.run("RETURN 1 as test")
                
                await driver.close()
                
                response_time = round((time.time() - start_time) * 1000, 2)
                
                return {
                    "status": "healthy",
                    "response_time_ms": response_time,
                    "database": self.neo4j_database,
                    "uri": self.neo4j_uri,
                    "graphiti_available": False,
                    "fallback_mode": True
                }
                
        except Exception as e:
            self.log_error(e, "health_check")
            return {
                "status": "unhealthy",
                "error": str(e),
                "database": self.neo4j_database,
                "uri": self.neo4j_uri
            }
    
    async def cleanup(self) -> None:
        """Cleanup service resources."""
        self.logger.info("Cleaning up enhanced knowledge graph service")
        
        if self._graphiti_instance:
            try:
                await self._graphiti_instance.close()
            except Exception as e:
                self.logger.warning(
                    "Error closing Graphiti instance",
                    error=str(e)
                )
            finally:
                self._graphiti_instance = None
