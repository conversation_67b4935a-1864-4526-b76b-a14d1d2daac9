"""
Chat Service

This module provides the main chat service functionality,
including message processing, conversation management, and LLM integration.
"""

import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, AsyncGenerator
from uuid import uuid4

import structlog

from chat_service.core.config import Settings
from chat_service.core.exceptions import ValidationError, NotFoundError
from chat_service.core.logging import LoggerMixin
from chat_service.services.llm_service import LLMService
from chat_service.services.knowledge_graph_service import KnowledgeGraphService


class ChatService(LoggerMixin):
    """
    Main chat service for processing messages and managing conversations.
    
    This service orchestrates LLM interactions, knowledge graph queries,
    and conversation persistence.
    """
    
    def __init__(
        self,
        llm_service: LLMService,
        kg_service: KnowledgeGraphService,
        settings: Settings
    ) -> None:
        """
        Initialize chat service.
        
        Args:
            llm_service: LLM service instance
            kg_service: Knowledge graph service instance
            settings: Application settings
        """
        self.llm_service = llm_service
        self.kg_service = kg_service
        self.settings = settings
        
        # In-memory conversation storage (use Redis/DB in production)
        self.conversations: Dict[str, Dict[str, Any]] = {}
        
        self.logger.info("Chat service initialized")
    
    async def process_message(
        self,
        message: str,
        conversation_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process a chat message and generate a response.
        
        Args:
            message: User message
            conversation_id: Optional conversation ID
            context: Additional context
            options: Processing options
            
        Returns:
            Dict[str, Any]: Response containing message and metadata
            
        Raises:
            ValidationError: If message validation fails
        """
        start_time = time.time()
        
        # Validate message
        if not message or not message.strip():
            raise ValidationError("Message cannot be empty")
        
        if len(message) > self.settings.max_context_tokens * 4:  # Rough token estimate
            raise ValidationError("Message too long")
        
        # Get or create conversation
        if not conversation_id:
            conversation_id = str(uuid4())
            await self._create_conversation(conversation_id)
        
        conversation = await self._get_conversation(conversation_id)
        if not conversation:
            raise NotFoundError(f"Conversation {conversation_id} not found")
        
        self.log_method_call(
            "process_message",
            conversation_id=conversation_id,
            message_length=len(message)
        )
        
        try:
            # Add user message to conversation
            user_message = {
                "id": str(uuid4()),
                "role": "user",
                "content": message,
                "timestamp": datetime.utcnow().isoformat(),
                "metadata": context or {}
            }
            conversation["messages"].append(user_message)
            
            # Get knowledge graph context if enabled
            kg_context = None
            if self.settings.enable_graph_context:
                kg_context = await self._get_knowledge_context(message, options or {})
            
            # Prepare conversation context
            conversation_context = self._prepare_conversation_context(
                conversation["messages"],
                kg_context
            )
            
            # Generate response using LLM
            response_content = await self.llm_service.generate_response(
                messages=conversation_context,
                options=options or {}
            )
            
            # Create assistant message
            assistant_message = {
                "id": str(uuid4()),
                "role": "assistant",
                "content": response_content["content"],
                "timestamp": datetime.utcnow().isoformat(),
                "metadata": {
                    "model": response_content.get("model"),
                    "provider": response_content.get("provider"),
                    "tokens": response_content.get("tokens"),
                    "kg_context": kg_context is not None,
                    "response_time_ms": round((time.time() - start_time) * 1000, 2)
                }
            }
            
            # Add assistant message to conversation
            conversation["messages"].append(assistant_message)
            
            # Update conversation metadata
            conversation["updated_at"] = datetime.utcnow().isoformat()
            conversation["message_count"] = len(conversation["messages"])
            
            # Trim conversation if too long
            if len(conversation["messages"]) > self.settings.max_conversation_length:
                conversation["messages"] = conversation["messages"][-self.settings.max_conversation_length:]
            
            self.log_method_result(
                "process_message",
                conversation_id=conversation_id,
                response_length=len(response_content["content"]),
                response_time_ms=assistant_message["metadata"]["response_time_ms"]
            )
            
            return {
                "message": assistant_message,
                "conversation_id": conversation_id,
                "metadata": {
                    "kg_context_used": kg_context is not None,
                    "response_time_ms": assistant_message["metadata"]["response_time_ms"]
                }
            }
            
        except Exception as e:
            self.log_error(e, "process_message", conversation_id=conversation_id)
            raise
    
    async def stream_message(
        self,
        message: str,
        conversation_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[str, None]:
        """
        Process a chat message with streaming response.
        
        Args:
            message: User message
            conversation_id: Optional conversation ID
            context: Additional context
            options: Processing options
            
        Yields:
            str: JSON-encoded response chunks
            
        Raises:
            ValidationError: If message validation fails
        """
        # Validate message
        if not message or not message.strip():
            raise ValidationError("Message cannot be empty")
        
        # Get or create conversation
        if not conversation_id:
            conversation_id = str(uuid4())
            await self._create_conversation(conversation_id)
        
        conversation = await self._get_conversation(conversation_id)
        if not conversation:
            raise NotFoundError(f"Conversation {conversation_id} not found")
        
        self.log_method_call(
            "stream_message",
            conversation_id=conversation_id,
            message_length=len(message)
        )
        
        try:
            # Add user message to conversation
            user_message = {
                "id": str(uuid4()),
                "role": "user",
                "content": message,
                "timestamp": datetime.utcnow().isoformat(),
                "metadata": context or {}
            }
            conversation["messages"].append(user_message)
            
            # Get knowledge graph context if enabled
            kg_context = None
            if self.settings.enable_graph_context:
                kg_context = await self._get_knowledge_context(message, options or {})
            
            # Prepare conversation context
            conversation_context = self._prepare_conversation_context(
                conversation["messages"],
                kg_context
            )
            
            # Stream response from LLM
            assistant_message_id = str(uuid4())
            accumulated_content = ""
            
            # Send initial chunk with message ID
            yield json.dumps({
                "type": "start",
                "message_id": assistant_message_id,
                "conversation_id": conversation_id
            })
            
            async for chunk in self.llm_service.stream_response(
                messages=conversation_context,
                options=options or {}
            ):
                accumulated_content += chunk.get("content", "")
                
                # Send content chunk
                yield json.dumps({
                    "type": "content",
                    "message_id": assistant_message_id,
                    "content": chunk.get("content", ""),
                    "delta": True
                })
            
            # Create final assistant message
            assistant_message = {
                "id": assistant_message_id,
                "role": "assistant",
                "content": accumulated_content,
                "timestamp": datetime.utcnow().isoformat(),
                "metadata": {
                    "kg_context": kg_context is not None,
                    "streaming": True
                }
            }
            
            # Add to conversation
            conversation["messages"].append(assistant_message)
            conversation["updated_at"] = datetime.utcnow().isoformat()
            conversation["message_count"] = len(conversation["messages"])
            
            # Send completion chunk
            yield json.dumps({
                "type": "complete",
                "message_id": assistant_message_id,
                "message": assistant_message
            })
            
        except Exception as e:
            self.log_error(e, "stream_message", conversation_id=conversation_id)
            
            # Send error chunk
            yield json.dumps({
                "type": "error",
                "error": str(e)
            })
    
    async def list_conversations(
        self,
        limit: int = 20,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        List conversations.
        
        Args:
            limit: Maximum number of conversations
            offset: Number of conversations to skip
            
        Returns:
            List[Dict[str, Any]]: List of conversation summaries
        """
        conversations = list(self.conversations.values())
        
        # Sort by updated_at descending
        conversations.sort(
            key=lambda x: x.get("updated_at", ""),
            reverse=True
        )
        
        # Apply pagination
        paginated = conversations[offset:offset + limit]
        
        # Return summaries
        return [
            {
                "id": conv["id"],
                "title": conv.get("title"),
                "message_count": conv.get("message_count", 0),
                "created_at": conv.get("created_at"),
                "updated_at": conv.get("updated_at"),
                "metadata": conv.get("metadata", {})
            }
            for conv in paginated
        ]
    
    async def get_conversation(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """
        Get conversation details.
        
        Args:
            conversation_id: Conversation identifier
            
        Returns:
            Optional[Dict[str, Any]]: Conversation details or None
        """
        return self.conversations.get(conversation_id)
    
    async def delete_conversation(self, conversation_id: str) -> bool:
        """
        Delete a conversation.
        
        Args:
            conversation_id: Conversation identifier
            
        Returns:
            bool: True if deleted, False if not found
        """
        if conversation_id in self.conversations:
            del self.conversations[conversation_id]
            return True
        return False
    
    async def update_conversation_title(
        self,
        conversation_id: str,
        title: str
    ) -> bool:
        """
        Update conversation title.
        
        Args:
            conversation_id: Conversation identifier
            title: New title
            
        Returns:
            bool: True if updated, False if not found
        """
        if conversation_id in self.conversations:
            self.conversations[conversation_id]["title"] = title
            self.conversations[conversation_id]["updated_at"] = datetime.utcnow().isoformat()
            return True
        return False
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check.
        
        Returns:
            Dict[str, Any]: Health check results
        """
        start_time = time.time()
        
        try:
            # Check LLM service
            llm_health = await self.llm_service.health_check()
            
            # Check knowledge graph service
            kg_health = await self.kg_service.health_check()
            
            response_time = round((time.time() - start_time) * 1000, 2)
            
            return {
                "status": "healthy",
                "response_time_ms": response_time,
                "services": {
                    "llm": llm_health,
                    "knowledge_graph": kg_health
                },
                "conversations_count": len(self.conversations)
            }
            
        except Exception as e:
            self.log_error(e, "health_check")
            raise
    
    async def cleanup(self) -> None:
        """Cleanup service resources."""
        self.logger.info("Cleaning up chat service")
        
        # Clear conversations (in production, this would be persisted)
        self.conversations.clear()
        
        # Cleanup dependent services
        await self.llm_service.cleanup()
        await self.kg_service.cleanup()
    
    async def _create_conversation(self, conversation_id: str) -> None:
        """Create a new conversation."""
        now = datetime.utcnow().isoformat()
        
        self.conversations[conversation_id] = {
            "id": conversation_id,
            "title": None,
            "messages": [],
            "created_at": now,
            "updated_at": now,
            "message_count": 0,
            "metadata": {}
        }
    
    async def _get_conversation(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """Get conversation by ID."""
        return self.conversations.get(conversation_id)
    
    async def _get_knowledge_context(
        self,
        message: str,
        options: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Get knowledge graph context for message."""
        try:
            # Use enhanced KG service for context
            if hasattr(self.kg_service, 'get_context_for_llm'):
                # Enhanced service with LLM-formatted context
                context_result = await self.kg_service.get_context_for_llm(
                    query=message,
                    max_context_length=options.get("max_context_length", 4000)
                )

                if context_result["entities_found"] > 0:
                    return {
                        "formatted_context": context_result["context"],
                        "citations": context_result["citations"],
                        "entities_found": context_result["entities_found"],
                        "search_query": message,
                        "truncated": context_result.get("truncated", False)
                    }
            else:
                # Fallback to basic search
                search_results = await self.kg_service.search(
                    query=message,
                    limit=options.get("kg_limit", self.settings.max_search_results),
                    threshold=options.get("kg_threshold", self.settings.similarity_threshold),
                    filters={"group_id": options.get("group_id", "user_guides")}
                )

                if search_results["results"]:
                    return {
                        "entities": [result for result in search_results["results"]],
                        "context": search_results.get("context", ""),
                        "citations": search_results.get("citations", []),
                        "search_query": message,
                        "results_count": len(search_results["results"])
                    }

        except Exception as e:
            self.log_error(e, "_get_knowledge_context", message_length=len(message))

        return None
    
    def _prepare_conversation_context(
        self,
        messages: List[Dict[str, Any]],
        kg_context: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, str]]:
        """Prepare conversation context for LLM."""
        context_messages = []
        
        # Add system message with knowledge graph context
        if kg_context:
            system_content = "You are a helpful assistant with access to a knowledge graph. "
            system_content += f"Based on the user's query, I found {kg_context['results_count']} relevant entities. "
            system_content += "Use this information to provide accurate and contextual responses.\n\n"
            system_content += "Relevant entities:\n"
            
            for entity in kg_context["entities"][:3]:  # Limit to top 3
                system_content += f"- {entity.get('type', 'Entity')}: {entity.get('properties', {}).get('name', 'Unknown')}\n"
            
            context_messages.append({
                "role": "system",
                "content": system_content
            })
        else:
            context_messages.append({
                "role": "system",
                "content": "You are a helpful assistant."
            })
        
        # Add conversation messages (limit to recent messages)
        recent_messages = messages[-10:]  # Keep last 10 messages
        
        for msg in recent_messages:
            if msg["role"] in ["user", "assistant"]:
                context_messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
        
        return context_messages
