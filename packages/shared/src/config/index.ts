/**
 * Configuration utilities and constants
 */

import { validatePort, validateEnvironment, validateDatabaseUri } from '../utils/validation';

export interface AppConfig {
  environment: string;
  server: {
    port: number;
    host: string;
    cors: {
      origin: string | string[];
      credentials: boolean;
    };
  };
  database: {
    uri: string;
    username: string;
    password: string;
    database?: string;
  };
  logging: {
    level: string;
    format: string;
  };
  features: {
    enableMetrics: boolean;
    enableSwagger: boolean;
    enableRateLimit: boolean;
  };
  security: {
    jwtSecret?: string;
    sessionSecret?: string;
    bcryptRounds: number;
  };
}

export const getConfig = (): AppConfig => {
  const environment = process.env.NODE_ENV || 'development';

  if (!validateEnvironment(environment)) {
    throw new Error(`Invalid environment: ${environment}`);
  }

  const port = parseInt(process.env.PORT || '3002', 10);
  if (!validatePort(port)) {
    throw new Error(`Invalid port: ${port}`);
  }

  const databaseUri = process.env.DATABASE_URI || 'neo4j://localhost:7687';
  if (!validateDatabaseUri(databaseUri)) {
    throw new Error(`Invalid database URI: ${databaseUri}`);
  }

  return {
    environment,
    server: {
      port,
      host: process.env.HOST || '0.0.0.0',
      cors: {
        origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
        credentials: process.env.CORS_CREDENTIALS === 'true',
      },
    },
    database: {
      uri: databaseUri,
      username: process.env.DATABASE_USERNAME || 'neo4j',
      password: process.env.DATABASE_PASSWORD || 'password',
      database: process.env.DATABASE_NAME,
    },
    logging: {
      level: process.env.LOG_LEVEL || 'info',
      format: process.env.LOG_FORMAT || 'text',
    },
    features: {
      enableMetrics: process.env.ENABLE_METRICS !== 'false',
      enableSwagger: process.env.ENABLE_SWAGGER !== 'false',
      enableRateLimit: process.env.ENABLE_RATE_LIMIT !== 'false',
    },
    security: {
      jwtSecret: process.env.JWT_SECRET,
      sessionSecret: process.env.SESSION_SECRET,
      bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),
    },
  };
};

// Environment helpers
export const isDevelopment = (): boolean => {
  return process.env.NODE_ENV === 'development';
};

export const isProduction = (): boolean => {
  return process.env.NODE_ENV === 'production';
};

export const isTest = (): boolean => {
  return process.env.NODE_ENV === 'test';
};

// Configuration validation
export const validateConfig = (config: AppConfig): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!validateEnvironment(config.environment)) {
    errors.push(`Invalid environment: ${config.environment}`);
  }

  if (!validatePort(config.server.port)) {
    errors.push(`Invalid server port: ${config.server.port}`);
  }

  if (!validateDatabaseUri(config.database.uri)) {
    errors.push(`Invalid database URI: ${config.database.uri}`);
  }

  if (!config.database.username || config.database.username.trim().length === 0) {
    errors.push('Database username is required');
  }

  if (!config.database.password || config.database.password.trim().length === 0) {
    errors.push('Database password is required');
  }

  if (isProduction() && !config.security.jwtSecret) {
    errors.push('JWT secret is required in production');
  }

  if (isProduction() && !config.security.sessionSecret) {
    errors.push('Session secret is required in production');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Default configuration for testing
export const getTestConfig = (): AppConfig => ({
  environment: 'test',
  server: {
    port: 3003,
    host: 'localhost',
    cors: {
      origin: ['http://localhost:3000'],
      credentials: false,
    },
  },
  database: {
    uri: 'neo4j://localhost:7687',
    username: 'neo4j',
    password: 'test',
    database: 'test',
  },
  logging: {
    level: 'error',
    format: 'text',
  },
  features: {
    enableMetrics: false,
    enableSwagger: false,
    enableRateLimit: false,
  },
  security: {
    jwtSecret: 'test-jwt-secret',
    sessionSecret: 'test-session-secret',
    bcryptRounds: 4,
  },
});
