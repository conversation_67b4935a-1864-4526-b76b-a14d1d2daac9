/**
 * Validation utilities
 */

// Basic validation functions
export const isString = (value: unknown): value is string => {
  return typeof value === 'string';
};

export const isNumber = (value: unknown): value is number => {
  return typeof value === 'number' && !isNaN(value);
};

export const isBoolean = (value: unknown): value is boolean => {
  return typeof value === 'boolean';
};

export const isObject = (value: unknown): value is Record<string, unknown> => {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
};

export const isArray = (value: unknown): value is unknown[] => {
  return Array.isArray(value);
};

export const isEmpty = (value: unknown): boolean => {
  if (value === null || value === undefined) return true;
  if (isString(value)) return value.trim().length === 0;
  if (isArray(value)) return value.length === 0;
  if (isObject(value)) return Object.keys(value).length === 0;
  return false;
};

// Email validation
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// URL validation
export const validateUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// Password validation
export const validatePassword = (
  password: string
): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Graph node ID validation
export const validateNodeId = (id: string): boolean => {
  return isString(id) && id.trim().length > 0 && !/[<>"/\\|?*]/.test(id);
};

// Cypher query validation (basic)
export const validateCypherQuery = (
  query: string
): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];

  if (!isString(query) || query.trim().length === 0) {
    errors.push('Query cannot be empty');
    return { isValid: false, errors };
  }

  const trimmedQuery = query.trim().toUpperCase();

  // Check for dangerous operations in production
  if (process.env.NODE_ENV === 'production') {
    const dangerousOperations = [
      'DELETE',
      'REMOVE',
      'DROP',
      'CREATE CONSTRAINT',
      'DROP CONSTRAINT',
    ];
    for (const operation of dangerousOperations) {
      if (trimmedQuery.includes(operation)) {
        errors.push(`Dangerous operation '${operation}' not allowed in production`);
      }
    }
  }

  // Basic syntax checks
  const openParens = (query.match(/\(/g) || []).length;
  const closeParens = (query.match(/\)/g) || []).length;
  if (openParens !== closeParens) {
    errors.push('Mismatched parentheses in query');
  }

  const openBrackets = (query.match(/\[/g) || []).length;
  const closeBrackets = (query.match(/\]/g) || []).length;
  if (openBrackets !== closeBrackets) {
    errors.push('Mismatched brackets in query');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

// JSON validation
export const validateJSON = (
  jsonString: string
): {
  isValid: boolean;
  parsed?: any;
  error?: string;
} => {
  try {
    const parsed = JSON.parse(jsonString);
    return { isValid: true, parsed };
  } catch (error) {
    return {
      isValid: false,
      error: error instanceof Error ? error.message : 'Invalid JSON',
    };
  }
};

// Feature flag name validation
export const validateFeatureFlagName = (name: string): boolean => {
  return /^[A-Z][A-Z0-9_]*$/.test(name);
};

// Pagination parameters validation
export const validatePaginationParams = (params: {
  page?: unknown;
  limit?: unknown;
}): {
  isValid: boolean;
  errors: string[];
  validated?: { page: number; limit: number };
} => {
  const errors: string[] = [];

  let page = 1;
  let limit = 20;

  if (params.page !== undefined) {
    if (!isNumber(params.page) || params.page < 1) {
      errors.push('Page must be a positive number');
    } else {
      page = Math.floor(params.page);
    }
  }

  if (params.limit !== undefined) {
    if (!isNumber(params.limit) || params.limit < 1 || params.limit > 100) {
      errors.push('Limit must be a number between 1 and 100');
    } else {
      limit = Math.floor(params.limit);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    validated: errors.length === 0 ? { page, limit } : undefined,
  };
};

// Environment validation
export const validateEnvironment = (env: string): boolean => {
  const validEnvironments = ['development', 'staging', 'production', 'test'];
  return validEnvironments.includes(env);
};

// Port validation
export const validatePort = (port: unknown): boolean => {
  return isNumber(port) && port >= 1 && port <= 65535 && Number.isInteger(port);
};

// Database URI validation
export const validateDatabaseUri = (uri: string): boolean => {
  try {
    const url = new URL(uri);
    return ['neo4j', 'neo4j+s', 'neo4j+ssc', 'bolt', 'bolt+s', 'bolt+ssc'].includes(
      url.protocol.slice(0, -1)
    );
  } catch {
    return false;
  }
};

// API key validation (basic format check)
export const validateApiKey = (key: string): boolean => {
  return isString(key) && key.length >= 20 && /^[a-zA-Z0-9_-]+$/.test(key);
};
