/**
 * Database Comparison Utility
 * 
 * This utility compares Neo4j query results between legacy and new systems
 * to validate that the repository pattern maintains exact same query results.
 */

import neo4j, { Driver, Session, Result, Record } from 'neo4j-driver';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';

export interface DatabaseConfig {
  name: string;
  uri: string;
  username: string;
  password: string;
  database?: string;
}

export interface QueryTest {
  name: string;
  query: string;
  parameters?: Record<string, any>;
  description?: string;
}

export interface QueryResult {
  system: string;
  queryName: string;
  success: boolean;
  recordCount: number;
  executionTime: number;
  records: any[];
  error?: string;
  timestamp: string;
}

export interface QueryComparison {
  queryName: string;
  identical: boolean;
  recordCountMatch: boolean;
  dataMatch: boolean;
  performanceDelta: number;
  differences: string[];
  legacyResult: QueryResult;
  newResult: QueryResult;
}

export interface DatabaseTestSuite {
  totalQueries: number;
  identicalResults: number;
  performanceImprovement: number;
  averageExecutionTime: {
    legacy: number;
    new: number;
  };
  comparisons: QueryComparison[];
  summary: string;
  timestamp: string;
}

export class DatabaseComparisonTool {
  private legacyDriver: Driver;
  private newDriver: Driver;
  private legacyConfig: DatabaseConfig;
  private newConfig: DatabaseConfig;
  private outputDir: string;

  constructor(
    legacyConfig: DatabaseConfig,
    newConfig: DatabaseConfig,
    outputDir: string = './database-test-results'
  ) {
    this.legacyConfig = legacyConfig;
    this.newConfig = newConfig;
    this.outputDir = outputDir;

    // Initialize Neo4j drivers
    this.legacyDriver = neo4j.driver(
      legacyConfig.uri,
      neo4j.auth.basic(legacyConfig.username, legacyConfig.password)
    );

    this.newDriver = neo4j.driver(
      newConfig.uri,
      neo4j.auth.basic(newConfig.username, newConfig.password)
    );

    // Ensure output directory exists
    if (!existsSync(this.outputDir)) {
      mkdirSync(this.outputDir, { recursive: true });
    }
  }

  /**
   * Run parallel database tests
   */
  async runDatabaseTests(queries: QueryTest[]): Promise<DatabaseTestSuite> {
    console.log('🗄️  Starting Database Comparison Tests');
    console.log(`📊 Testing ${queries.length} queries on both databases`);
    console.log(`🏛️  Legacy Database: ${this.legacyConfig.name}`);
    console.log(`🆕 New Database: ${this.newConfig.name}`);
    console.log('-'.repeat(60));

    const comparisons: QueryComparison[] = [];

    for (const queryTest of queries) {
      console.log(`\n🔍 Testing: ${queryTest.name}`);
      
      try {
        // Run queries on both databases in parallel
        const [legacyResult, newResult] = await Promise.allSettled([
          this.executeQuery(queryTest, this.legacyDriver, this.legacyConfig),
          this.executeQuery(queryTest, this.newDriver, this.newConfig)
        ]);

        // Process results
        const legacyQueryResult = legacyResult.status === 'fulfilled' 
          ? legacyResult.value 
          : this.createErrorResult(queryTest, this.legacyConfig, legacyResult.reason);

        const newQueryResult = newResult.status === 'fulfilled' 
          ? newResult.value 
          : this.createErrorResult(queryTest, this.newConfig, newResult.reason);

        // Compare results
        const comparison = this.compareQueryResults(queryTest, legacyQueryResult, newQueryResult);
        comparisons.push(comparison);

        // Log comparison result
        this.logQueryComparison(comparison);

      } catch (error) {
        console.error(`❌ Failed to test ${queryTest.name}:`, error);
      }
    }

    // Generate test suite result
    const suiteResult = this.generateDatabaseTestSuite(comparisons);
    
    // Save results
    await this.saveResults(suiteResult);
    
    // Print summary
    this.printSummary(suiteResult);

    return suiteResult;
  }

  /**
   * Execute a single query on a database
   */
  private async executeQuery(
    queryTest: QueryTest,
    driver: Driver,
    config: DatabaseConfig
  ): Promise<QueryResult> {
    const session: Session = driver.session({
      database: config.database || 'neo4j'
    });

    const startTime = performance.now();
    
    try {
      const result: Result = await session.run(queryTest.query, queryTest.parameters || {});
      const endTime = performance.now();

      const records = result.records.map(record => this.recordToObject(record));

      return {
        system: config.name,
        queryName: queryTest.name,
        success: true,
        recordCount: records.length,
        executionTime: Math.round(endTime - startTime),
        records,
        timestamp: new Date().toISOString()
      };

    } catch (error: any) {
      const endTime = performance.now();
      
      return {
        system: config.name,
        queryName: queryTest.name,
        success: false,
        recordCount: 0,
        executionTime: Math.round(endTime - startTime),
        records: [],
        error: error.message,
        timestamp: new Date().toISOString()
      };
    } finally {
      await session.close();
    }
  }

  /**
   * Convert Neo4j record to plain object
   */
  private recordToObject(record: Record): any {
    const obj: any = {};
    
    record.keys.forEach(key => {
      const value = record.get(key);
      obj[key] = this.convertNeo4jValue(value);
    });
    
    return obj;
  }

  /**
   * Convert Neo4j values to plain JavaScript values
   */
  private convertNeo4jValue(value: any): any {
    if (value === null || value === undefined) {
      return value;
    }
    
    // Handle Neo4j integers
    if (neo4j.isInt(value)) {
      return value.toNumber();
    }
    
    // Handle Neo4j nodes
    if (value.labels !== undefined) {
      return {
        identity: value.identity.toNumber(),
        labels: value.labels,
        properties: this.convertNeo4jValue(value.properties)
      };
    }
    
    // Handle Neo4j relationships
    if (value.type !== undefined && value.start !== undefined) {
      return {
        identity: value.identity.toNumber(),
        start: value.start.toNumber(),
        end: value.end.toNumber(),
        type: value.type,
        properties: this.convertNeo4jValue(value.properties)
      };
    }
    
    // Handle objects
    if (typeof value === 'object' && !Array.isArray(value)) {
      const converted: any = {};
      for (const [k, v] of Object.entries(value)) {
        converted[k] = this.convertNeo4jValue(v);
      }
      return converted;
    }
    
    // Handle arrays
    if (Array.isArray(value)) {
      return value.map(item => this.convertNeo4jValue(item));
    }
    
    return value;
  }

  /**
   * Create error result for failed queries
   */
  private createErrorResult(
    queryTest: QueryTest,
    config: DatabaseConfig,
    error: any
  ): QueryResult {
    return {
      system: config.name,
      queryName: queryTest.name,
      success: false,
      recordCount: 0,
      executionTime: 0,
      records: [],
      error: error?.message || 'Unknown error',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Compare query results from both databases
   */
  private compareQueryResults(
    queryTest: QueryTest,
    legacyResult: QueryResult,
    newResult: QueryResult
  ): QueryComparison {
    const differences: string[] = [];
    
    // Compare record counts
    const recordCountMatch = legacyResult.recordCount === newResult.recordCount;
    if (!recordCountMatch) {
      differences.push(
        `Record count: Legacy=${legacyResult.recordCount}, New=${newResult.recordCount}`
      );
    }

    // Compare data
    const dataMatch = this.deepEqual(legacyResult.records, newResult.records);
    if (!dataMatch && legacyResult.success && newResult.success) {
      differences.push('Query results differ');
      
      // Add specific data differences
      const dataDiffs = this.findRecordDifferences(legacyResult.records, newResult.records);
      differences.push(...dataDiffs);
    }

    // Calculate performance delta
    const performanceDelta = newResult.executionTime - legacyResult.executionTime;

    // Check for errors
    if (legacyResult.error && !newResult.error) {
      differences.push(`Legacy database error: ${legacyResult.error}`);
    } else if (!legacyResult.error && newResult.error) {
      differences.push(`New database error: ${newResult.error}`);
    } else if (legacyResult.error && newResult.error && legacyResult.error !== newResult.error) {
      differences.push(`Different errors: Legacy="${legacyResult.error}", New="${newResult.error}"`);
    }

    return {
      queryName: queryTest.name,
      identical: recordCountMatch && dataMatch && legacyResult.success && newResult.success,
      recordCountMatch,
      dataMatch,
      performanceDelta,
      differences,
      legacyResult,
      newResult
    };
  }

  /**
   * Deep equality check for arrays/objects
   */
  private deepEqual(obj1: any, obj2: any): boolean {
    if (obj1 === obj2) return true;
    
    if (obj1 == null || obj2 == null) return obj1 === obj2;
    
    if (typeof obj1 !== typeof obj2) return false;
    
    if (typeof obj1 !== 'object') return obj1 === obj2;
    
    if (Array.isArray(obj1) !== Array.isArray(obj2)) return false;
    
    if (Array.isArray(obj1)) {
      if (obj1.length !== obj2.length) return false;
      
      // Sort arrays for comparison (order might differ)
      const sorted1 = [...obj1].sort((a, b) => JSON.stringify(a).localeCompare(JSON.stringify(b)));
      const sorted2 = [...obj2].sort((a, b) => JSON.stringify(a).localeCompare(JSON.stringify(b)));
      
      for (let i = 0; i < sorted1.length; i++) {
        if (!this.deepEqual(sorted1[i], sorted2[i])) return false;
      }
      
      return true;
    }
    
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    
    if (keys1.length !== keys2.length) return false;
    
    for (const key of keys1) {
      if (!keys2.includes(key)) return false;
      if (!this.deepEqual(obj1[key], obj2[key])) return false;
    }
    
    return true;
  }

  /**
   * Find specific differences in record arrays
   */
  private findRecordDifferences(records1: any[], records2: any[]): string[] {
    const differences: string[] = [];
    
    if (records1.length !== records2.length) {
      differences.push(`Record count mismatch: ${records1.length} vs ${records2.length}`);
    }
    
    const maxLength = Math.max(records1.length, records2.length);
    
    for (let i = 0; i < Math.min(10, maxLength); i++) { // Limit to first 10 differences
      const record1 = records1[i];
      const record2 = records2[i];
      
      if (!record1 && record2) {
        differences.push(`Record ${i}: Missing in legacy`);
      } else if (record1 && !record2) {
        differences.push(`Record ${i}: Missing in new`);
      } else if (record1 && record2 && !this.deepEqual(record1, record2)) {
        differences.push(`Record ${i}: Data differs`);
      }
    }
    
    return differences;
  }

  /**
   * Generate database test suite result
   */
  private generateDatabaseTestSuite(comparisons: QueryComparison[]): DatabaseTestSuite {
    const totalQueries = comparisons.length;
    const identicalResults = comparisons.filter(c => c.identical).length;

    // Calculate average execution times
    const legacyTimes = comparisons
      .filter(c => c.legacyResult.success)
      .map(c => c.legacyResult.executionTime);
    const newTimes = comparisons
      .filter(c => c.newResult.success)
      .map(c => c.newResult.executionTime);

    const avgLegacyTime = legacyTimes.length > 0 
      ? legacyTimes.reduce((a, b) => a + b, 0) / legacyTimes.length 
      : 0;
    const avgNewTime = newTimes.length > 0 
      ? newTimes.reduce((a, b) => a + b, 0) / newTimes.length 
      : 0;

    const performanceImprovement = avgLegacyTime > 0 
      ? ((avgLegacyTime - avgNewTime) / avgLegacyTime) * 100 
      : 0;

    // Generate summary
    const identicalPercentage = (identicalResults / totalQueries) * 100;
    let summary = `Database Migration Validation: ${identicalPercentage.toFixed(1)}% identical results`;
    
    if (performanceImprovement > 0) {
      summary += `, ${performanceImprovement.toFixed(1)}% query performance improvement`;
    } else if (performanceImprovement < 0) {
      summary += `, ${Math.abs(performanceImprovement).toFixed(1)}% query performance regression`;
    }

    return {
      totalQueries,
      identicalResults,
      performanceImprovement,
      averageExecutionTime: {
        legacy: Math.round(avgLegacyTime),
        new: Math.round(avgNewTime)
      },
      comparisons,
      summary,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Log query comparison result
   */
  private logQueryComparison(comparison: QueryComparison): void {
    const { queryName, identical, performanceDelta, legacyResult, newResult } = comparison;
    
    if (identical) {
      console.log(`  ✅ ${queryName}: Identical results (${legacyResult.recordCount} records)`);
    } else {
      console.log(`  ⚠️  ${queryName}: Differences found`);
      comparison.differences.forEach(diff => {
        console.log(`     - ${diff}`);
      });
    }
    
    // Performance comparison
    if (performanceDelta < 0) {
      console.log(`  🚀 Performance: ${Math.abs(performanceDelta)}ms faster`);
    } else if (performanceDelta > 0) {
      console.log(`  🐌 Performance: ${performanceDelta}ms slower`);
    }
    
    console.log(`  📊 Times: Legacy=${legacyResult.executionTime}ms, New=${newResult.executionTime}ms`);
  }

  /**
   * Save test results to files
   */
  private async saveResults(suiteResult: DatabaseTestSuite): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // Save detailed results as JSON
    const detailedResultsPath = join(this.outputDir, `database-test-results-${timestamp}.json`);
    writeFileSync(detailedResultsPath, JSON.stringify(suiteResult, null, 2));
    
    // Save summary report as text
    const summaryPath = join(this.outputDir, `database-test-summary-${timestamp}.txt`);
    const summaryReport = this.generateSummaryReport(suiteResult);
    writeFileSync(summaryPath, summaryReport);
    
    console.log(`\n📁 Database test results saved:`);
    console.log(`   Detailed: ${detailedResultsPath}`);
    console.log(`   Summary: ${summaryPath}`);
  }

  /**
   * Generate human-readable summary report
   */
  private generateSummaryReport(suiteResult: DatabaseTestSuite): string {
    const { 
      totalQueries, 
      identicalResults, 
      performanceImprovement, 
      averageExecutionTime,
      comparisons 
    } = suiteResult;

    let report = `Database Comparison Test Report\n`;
    report += `Generated: ${suiteResult.timestamp}\n`;
    report += `${'='.repeat(50)}\n\n`;
    
    report += `SUMMARY\n`;
    report += `-------\n`;
    report += `Total Queries: ${totalQueries}\n`;
    report += `Identical Results: ${identicalResults}/${totalQueries} (${(identicalResults/totalQueries*100).toFixed(1)}%)\n`;
    report += `Performance Change: ${performanceImprovement > 0 ? '+' : ''}${performanceImprovement.toFixed(1)}%\n`;
    report += `Average Execution Time:\n`;
    report += `  Legacy Database: ${averageExecutionTime.legacy}ms\n`;
    report += `  New Database: ${averageExecutionTime.new}ms\n\n`;
    
    report += `DETAILED RESULTS\n`;
    report += `----------------\n`;
    
    for (const comparison of comparisons) {
      report += `\n${comparison.queryName}:\n`;
      report += `  Status: ${comparison.identical ? 'IDENTICAL' : 'DIFFERENT'}\n`;
      report += `  Legacy: ${comparison.legacyResult.recordCount} records (${comparison.legacyResult.executionTime}ms)\n`;
      report += `  New: ${comparison.newResult.recordCount} records (${comparison.newResult.executionTime}ms)\n`;
      
      if (comparison.differences.length > 0) {
        report += `  Differences:\n`;
        comparison.differences.forEach(diff => {
          report += `    - ${diff}\n`;
        });
      }
    }
    
    return report;
  }

  /**
   * Print summary to console
   */
  private printSummary(suiteResult: DatabaseTestSuite): void {
    console.log('\n' + '='.repeat(60));
    console.log('📋 DATABASE COMPARISON SUMMARY');
    console.log('='.repeat(60));
    console.log(`📊 Total Queries: ${suiteResult.totalQueries}`);
    console.log(`✅ Identical Results: ${suiteResult.identicalResults}/${suiteResult.totalQueries} (${(suiteResult.identicalResults/suiteResult.totalQueries*100).toFixed(1)}%)`);
    console.log(`🚀 Performance: ${suiteResult.performanceImprovement > 0 ? '+' : ''}${suiteResult.performanceImprovement.toFixed(1)}%`);
    console.log(`⏱️  Average Execution Time:`);
    console.log(`   Legacy: ${suiteResult.averageExecutionTime.legacy}ms`);
    console.log(`   New: ${suiteResult.averageExecutionTime.new}ms`);
    console.log(`\n${suiteResult.summary}`);
    console.log('='.repeat(60));
  }

  /**
   * Close database connections
   */
  async cleanup(): Promise<void> {
    await this.legacyDriver.close();
    await this.newDriver.close();
  }
}

// Common Neo4j queries for Knowledge Graph Visualizer testing
export const knowledgeGraphQueries: QueryTest[] = [
  // Basic node and relationship queries
  {
    name: 'Get All Nodes with Group ID',
    query: 'MATCH (n) WHERE n.group_id = $group_id RETURN n LIMIT 100',
    parameters: { group_id: 'user_guides' },
    description: 'Retrieve all nodes for a specific group'
  },
  {
    name: 'Get All Relationships with Group ID',
    query: 'MATCH (n)-[r]->(m) WHERE n.group_id = $group_id RETURN n, r, m LIMIT 100',
    parameters: { group_id: 'user_guides' },
    description: 'Retrieve all relationships for a specific group'
  },
  {
    name: 'Get Nodes and Relationships Combined',
    query: 'MATCH (n) WHERE n.group_id = $group_id MATCH (n)-[r]->() RETURN n, r',
    parameters: { group_id: 'user_guides' },
    description: 'Combined query for nodes and their relationships'
  },

  // Entity-specific queries
  {
    name: 'Get Entity Nodes Only',
    query: 'MATCH (n:Entity) WHERE n.group_id = $group_id RETURN n ORDER BY n.name LIMIT 50',
    parameters: { group_id: 'user_guides' },
    description: 'Retrieve only Entity nodes'
  },
  {
    name: 'Get Episode Nodes Only',
    query: 'MATCH (n:Episode) WHERE n.group_id = $group_id RETURN n ORDER BY n.created_at DESC LIMIT 50',
    parameters: { group_id: 'user_guides' },
    description: 'Retrieve only Episode nodes'
  },

  // Search and filtering queries
  {
    name: 'Search Entities by Name',
    query: `
      MATCH (n:Entity)
      WHERE n.group_id = $group_id
        AND toLower(n.name) CONTAINS toLower($search_term)
      RETURN n
      ORDER BY n.name
      LIMIT 20
    `,
    parameters: { group_id: 'user_guides', search_term: 'graph' },
    description: 'Search entities by name containing search term'
  },
  {
    name: 'Search by Entity Summary',
    query: `
      MATCH (n:Entity)
      WHERE n.group_id = $group_id
        AND toLower(n.summary) CONTAINS toLower($search_term)
      RETURN n
      ORDER BY n.name
      LIMIT 20
    `,
    parameters: { group_id: 'user_guides', search_term: 'visualization' },
    description: 'Search entities by summary content'
  },

  // Relationship pattern queries
  {
    name: 'Get Entity Relationships',
    query: `
      MATCH (e1:Entity)-[r]->(e2:Entity)
      WHERE e1.group_id = $group_id
      RETURN e1, r, e2
      ORDER BY r.weight DESC
      LIMIT 50
    `,
    parameters: { group_id: 'user_guides' },
    description: 'Get relationships between entities'
  },
  {
    name: 'Get Episode to Entity Relationships',
    query: `
      MATCH (ep:Episode)-[r]->(e:Entity)
      WHERE ep.group_id = $group_id
      RETURN ep, r, e
      ORDER BY ep.created_at DESC
      LIMIT 50
    `,
    parameters: { group_id: 'user_guides' },
    description: 'Get relationships from episodes to entities'
  },

  // Aggregation and analytics queries
  {
    name: 'Count Nodes by Type',
    query: `
      MATCH (n)
      WHERE n.group_id = $group_id
      RETURN labels(n) as node_type, count(n) as count
      ORDER BY count DESC
    `,
    parameters: { group_id: 'user_guides' },
    description: 'Count nodes by their labels/types'
  },
  {
    name: 'Count Relationships by Type',
    query: `
      MATCH (n)-[r]->(m)
      WHERE n.group_id = $group_id
      RETURN type(r) as relationship_type, count(r) as count
      ORDER BY count DESC
    `,
    parameters: { group_id: 'user_guides' },
    description: 'Count relationships by their types'
  },

  // Graph analysis queries
  {
    name: 'Get Node Degrees',
    query: `
      MATCH (n:Entity)
      WHERE n.group_id = $group_id
      OPTIONAL MATCH (n)-[r]-()
      RETURN n.name as entity_name, count(r) as degree
      ORDER BY degree DESC
      LIMIT 20
    `,
    parameters: { group_id: 'user_guides' },
    description: 'Calculate node degrees (connection counts)'
  },
  {
    name: 'Get Connected Components',
    query: `
      MATCH (n:Entity)
      WHERE n.group_id = $group_id
      OPTIONAL MATCH (n)-[*1..2]-(connected:Entity)
      WHERE connected.group_id = $group_id
      RETURN n.name as entity, collect(DISTINCT connected.name) as connected_entities
      LIMIT 10
    `,
    parameters: { group_id: 'user_guides' },
    description: 'Find connected components within 2 hops'
  },

  // Performance test queries
  {
    name: 'Large Result Set Query',
    query: 'MATCH (n) WHERE n.group_id = $group_id RETURN n LIMIT 1000',
    parameters: { group_id: 'user_guides' },
    description: 'Query returning large result set for performance testing'
  },
  {
    name: 'Complex Join Query',
    query: `
      MATCH (e1:Entity)-[r1]->(e2:Entity)-[r2]->(e3:Entity)
      WHERE e1.group_id = $group_id
      RETURN e1, r1, e2, r2, e3
      LIMIT 100
    `,
    parameters: { group_id: 'user_guides' },
    description: 'Complex query with multiple joins for performance testing'
  },

  // Edge case queries
  {
    name: 'Nodes Without Relationships',
    query: `
      MATCH (n:Entity)
      WHERE n.group_id = $group_id
        AND NOT (n)-[]-()
      RETURN n
      LIMIT 20
    `,
    parameters: { group_id: 'user_guides' },
    description: 'Find isolated nodes without any relationships'
  },
  {
    name: 'Self-Referencing Relationships',
    query: `
      MATCH (n)-[r]->(n)
      WHERE n.group_id = $group_id
      RETURN n, r
      LIMIT 10
    `,
    parameters: { group_id: 'user_guides' },
    description: 'Find self-referencing relationships'
  },

  // Metadata and property queries
  {
    name: 'Get Node Properties',
    query: `
      MATCH (n:Entity)
      WHERE n.group_id = $group_id
      RETURN n.name, n.entity_type, n.summary, n.created_at
      ORDER BY n.created_at DESC
      LIMIT 20
    `,
    parameters: { group_id: 'user_guides' },
    description: 'Retrieve specific properties from entities'
  },
  {
    name: 'Get Relationship Properties',
    query: `
      MATCH (n)-[r]->(m)
      WHERE n.group_id = $group_id
      RETURN type(r) as rel_type, r.weight, r.created_at, r.source_context
      ORDER BY r.weight DESC
      LIMIT 20
    `,
    parameters: { group_id: 'user_guides' },
    description: 'Retrieve relationship properties and metadata'
  }
];

// Database configuration factory
export function createDatabaseConfigs(): { legacy: DatabaseConfig; new: DatabaseConfig } {
  return {
    legacy: {
      name: 'Legacy Database',
      uri: process.env.NEO4J_URI || 'bolt://localhost:7687',
      username: process.env.NEO4J_USERNAME || 'neo4j',
      password: process.env.NEO4J_PASSWORD || 'password',
      database: process.env.NEO4J_DATABASE || 'neo4j'
    },
    new: {
      name: 'New Database',
      uri: process.env.NEO4J_NEW_URI || process.env.NEO4J_URI || 'bolt://localhost:7687',
      username: process.env.NEO4J_NEW_USERNAME || process.env.NEO4J_USERNAME || 'neo4j',
      password: process.env.NEO4J_NEW_PASSWORD || process.env.NEO4J_PASSWORD || 'password',
      database: process.env.NEO4J_NEW_DATABASE || process.env.NEO4J_DATABASE || 'neo4j'
    }
  };
}
