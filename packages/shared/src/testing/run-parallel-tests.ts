#!/usr/bin/env ts-node
/**
 * Comprehensive Parallel Testing Script
 * 
 * This script runs both API and database parallel tests to validate
 * migration accuracy between legacy and new systems.
 */

import { config } from 'dotenv';
import { join } from 'path';
import { TestRunner } from './test-runner';
import { DatabaseComparisonTool, knowledgeGraphQueries, createDatabaseConfigs } from './database-comparison';

// Load environment variables
config({ path: join(__dirname, '../../../../.env') });

interface TestOptions {
  apiTests: boolean;
  databaseTests: boolean;
  loadTests: boolean;
  smokeTests: boolean;
  category?: string;
  outputDir?: string;
}

class ComprehensiveTestRunner {
  private testRunner: TestRunner;
  private databaseTool: DatabaseComparisonTool | null = null;

  constructor(options: TestOptions) {
    // Initialize API test runner
    this.testRunner = new TestRunner();

    // Initialize database comparison tool if needed
    if (options.databaseTests) {
      const { legacy, new: newDb } = createDatabaseConfigs();
      this.databaseTool = new DatabaseComparisonTool(
        legacy,
        newDb,
        options.outputDir ? join(options.outputDir, 'database') : './test-results/database'
      );
    }
  }

  /**
   * Run comprehensive parallel tests
   */
  async runComprehensiveTests(options: TestOptions): Promise<void> {
    console.log('🚀 Starting Comprehensive Parallel Testing Suite');
    console.log('='.repeat(70));
    console.log(`📊 API Tests: ${options.apiTests ? 'Enabled' : 'Disabled'}`);
    console.log(`🗄️  Database Tests: ${options.databaseTests ? 'Enabled' : 'Disabled'}`);
    console.log(`🔥 Load Tests: ${options.loadTests ? 'Enabled' : 'Disabled'}`);
    console.log(`💨 Smoke Tests: ${options.smokeTests ? 'Enabled' : 'Disabled'}`);
    if (options.category) {
      console.log(`🎯 Category Filter: ${options.category}`);
    }
    console.log('='.repeat(70));

    const results = {
      apiTests: null as any,
      databaseTests: null as any,
      loadTests: null as any,
      smokeTests: null as any
    };

    try {
      // Run smoke tests first (quick validation)
      if (options.smokeTests) {
        console.log('\n💨 Running Smoke Tests');
        console.log('-' * 50);
        await this.testRunner.runSmokeTests();
        results.smokeTests = 'completed';
      }

      // Run API tests
      if (options.apiTests) {
        console.log('\n🌐 Running API Parallel Tests');
        console.log('-' * 50);
        
        if (options.category) {
          await this.testRunner.runCategoryTests(options.category as any);
        } else {
          results.apiTests = await this.testRunner.runAllTests();
        }
      }

      // Run database tests
      if (options.databaseTests && this.databaseTool) {
        console.log('\n🗄️  Running Database Comparison Tests');
        console.log('-' * 50);
        
        results.databaseTests = await this.databaseTool.runDatabaseTests(knowledgeGraphQueries);
      }

      // Run load tests
      if (options.loadTests) {
        console.log('\n🔥 Running Load Test Comparison');
        console.log('-' * 50);
        
        await this.testRunner.runLoadTests();
        results.loadTests = 'completed';
      }

      // Generate comprehensive summary
      this.generateComprehensiveSummary(results, options);

    } catch (error) {
      console.error('❌ Test execution failed:', error);
      throw error;
    } finally {
      // Cleanup resources
      if (this.databaseTool) {
        await this.databaseTool.cleanup();
      }
    }
  }

  /**
   * Generate comprehensive test summary
   */
  private generateComprehensiveSummary(results: any, options: TestOptions): void {
    console.log('\n' + '='.repeat(70));
    console.log('📋 COMPREHENSIVE TEST SUMMARY');
    console.log('='.repeat(70));

    let overallStatus = 'PASS';
    const issues: string[] = [];

    // API Test Results
    if (options.apiTests && results.apiTests) {
      const apiIdenticalPercentage = (results.apiTests.identicalResponses / results.apiTests.totalTests) * 100;
      console.log(`🌐 API Tests: ${results.apiTests.identicalResponses}/${results.apiTests.totalTests} identical (${apiIdenticalPercentage.toFixed(1)}%)`);
      
      if (apiIdenticalPercentage < 95) {
        overallStatus = 'WARNING';
        issues.push(`API compatibility below 95% (${apiIdenticalPercentage.toFixed(1)}%)`);
      }
      
      if (results.apiTests.performanceImprovement < -20) {
        overallStatus = 'WARNING';
        issues.push(`Significant API performance regression (${results.apiTests.performanceImprovement.toFixed(1)}%)`);
      }
    }

    // Database Test Results
    if (options.databaseTests && results.databaseTests) {
      const dbIdenticalPercentage = (results.databaseTests.identicalResults / results.databaseTests.totalQueries) * 100;
      console.log(`🗄️  Database Tests: ${results.databaseTests.identicalResults}/${results.databaseTests.totalQueries} identical (${dbIdenticalPercentage.toFixed(1)}%)`);
      
      if (dbIdenticalPercentage < 100) {
        overallStatus = 'CRITICAL';
        issues.push(`Database results not 100% identical (${dbIdenticalPercentage.toFixed(1)}%)`);
      }
      
      if (results.databaseTests.performanceImprovement < -50) {
        overallStatus = 'WARNING';
        issues.push(`Significant database performance regression (${results.databaseTests.performanceImprovement.toFixed(1)}%)`);
      }
    }

    // Overall Status
    console.log(`\n🎯 Overall Status: ${overallStatus}`);
    
    if (issues.length > 0) {
      console.log('\n⚠️  Issues Found:');
      issues.forEach(issue => {
        console.log(`   - ${issue}`);
      });
    } else {
      console.log('\n✅ All tests passed successfully!');
    }

    // Recommendations
    console.log('\n📝 Recommendations:');
    
    if (overallStatus === 'PASS') {
      console.log('   ✅ Migration appears safe to proceed');
      console.log('   ✅ Systems are functionally equivalent');
      console.log('   ✅ Performance is acceptable');
    } else if (overallStatus === 'WARNING') {
      console.log('   ⚠️  Review performance regressions');
      console.log('   ⚠️  Consider optimization before full migration');
      console.log('   ✅ Functional compatibility is acceptable');
    } else {
      console.log('   🚨 DO NOT PROCEED with migration');
      console.log('   🚨 Critical functional differences detected');
      console.log('   🚨 Investigate and fix issues before retesting');
    }

    console.log('='.repeat(70));
  }

  /**
   * Run migration readiness assessment
   */
  async runMigrationReadinessAssessment(): Promise<void> {
    console.log('🔍 Running Migration Readiness Assessment');
    console.log('='.repeat(60));

    // Critical smoke tests
    console.log('\n1️⃣  Critical System Validation');
    await this.testRunner.runSmokeTests();

    // Core functionality tests
    console.log('\n2️⃣  Core Functionality Validation');
    await this.testRunner.runCategoryTests('graph');
    await this.testRunner.runCategoryTests('chat');

    // Database integrity tests
    if (this.databaseTool) {
      console.log('\n3️⃣  Database Integrity Validation');
      
      // Run critical database queries
      const criticalQueries = knowledgeGraphQueries.filter(q => 
        q.name.includes('Get All Nodes') || 
        q.name.includes('Get All Relationships') ||
        q.name.includes('Count Nodes')
      );
      
      await this.databaseTool.runDatabaseTests(criticalQueries);
    }

    // Performance baseline
    console.log('\n4️⃣  Performance Baseline Validation');
    await this.testRunner.runLoadTests();

    console.log('\n✅ Migration Readiness Assessment Complete');
    console.log('📊 Review detailed results in test-results/ directory');
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'comprehensive';

  const options: TestOptions = {
    apiTests: true,
    databaseTests: true,
    loadTests: false,
    smokeTests: false,
    outputDir: './test-results'
  };

  // Parse command line arguments
  for (let i = 1; i < args.length; i++) {
    const arg = args[i];
    switch (arg) {
      case '--no-api':
        options.apiTests = false;
        break;
      case '--no-database':
        options.databaseTests = false;
        break;
      case '--load':
        options.loadTests = true;
        break;
      case '--smoke':
        options.smokeTests = true;
        break;
      case '--category':
        options.category = args[++i];
        break;
      case '--output':
        options.outputDir = args[++i];
        break;
    }
  }

  const testRunner = new ComprehensiveTestRunner(options);

  try {
    switch (command) {
      case 'comprehensive':
        await testRunner.runComprehensiveTests(options);
        break;
      case 'readiness':
        await testRunner.runMigrationReadinessAssessment();
        break;
      case 'api':
        options.databaseTests = false;
        await testRunner.runComprehensiveTests(options);
        break;
      case 'database':
        options.apiTests = false;
        await testRunner.runComprehensiveTests(options);
        break;
      case 'smoke':
        options.apiTests = false;
        options.databaseTests = false;
        options.smokeTests = true;
        await testRunner.runComprehensiveTests(options);
        break;
      case 'load':
        options.apiTests = false;
        options.databaseTests = false;
        options.loadTests = true;
        await testRunner.runComprehensiveTests(options);
        break;
      default:
        console.log('Usage: npm run test:parallel [command] [options]');
        console.log('');
        console.log('Commands:');
        console.log('  comprehensive  Run all parallel tests (default)');
        console.log('  readiness      Run migration readiness assessment');
        console.log('  api            Run only API tests');
        console.log('  database       Run only database tests');
        console.log('  smoke          Run only smoke tests');
        console.log('  load           Run only load tests');
        console.log('');
        console.log('Options:');
        console.log('  --no-api       Disable API tests');
        console.log('  --no-database  Disable database tests');
        console.log('  --load         Enable load tests');
        console.log('  --smoke        Enable smoke tests');
        console.log('  --category     Run specific test category');
        console.log('  --output       Specify output directory');
        process.exit(1);
    }
  } catch (error) {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { ComprehensiveTestRunner, TestOptions };
