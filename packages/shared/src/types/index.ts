/**
 * Shared TypeScript types and interfaces
 */

// Re-export all types from specific modules
export * from './graph';
export * from './api';
export * from './chat';

// Legacy types for backward compatibility
export interface FeatureFlags {
  NEW_CONTROLLER_LAYER: boolean;
  NEW_SERVICE_LAYER: boolean;
  NEW_REPOSITORY_PATTERN: boolean;
  NEW_MIDDLEWARE_STACK: boolean;
  NEW_CHAT_SERVICE: boolean;
  CIRCUIT_BREAKER_ENABLED: boolean;
  DUAL_EXECUTION_MODE: boolean;
  PERFORMANCE_MONITORING: boolean;
  AUTOMATIC_ROLLBACK: boolean;
  TRAFFIC_PERCENTAGE_NEW_API: number;
  DEBUG_MODE: boolean;
  VERBOSE_LOGGING: boolean;
  MIGRATION_METRICS: boolean;
}

export interface MigrationPhase {
  backend: {
    enabled: number;
    total: number;
    percentage: number;
  };
  services: {
    chatService: boolean;
    circuitBreaker: boolean;
  };
  traffic: {
    api: number;
  };
}
