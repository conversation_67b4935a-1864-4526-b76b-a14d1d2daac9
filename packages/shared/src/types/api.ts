/**
 * API-specific types and interfaces
 */

// Base API types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
  requestId?: string;
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  statusCode: number;
  timestamp: string;
  path?: string;
  requestId?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
}

// Request types
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface SearchParams extends PaginationParams {
  query?: string;
  filters?: Record<string, any>;
}

// Health check types
export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  uptime: number;
  checks: {
    database: HealthCheck;
    memory: HealthCheck;
    disk: HealthCheck;
    external?: Record<string, HealthCheck>;
  };
}

export interface HealthCheck {
  status: 'pass' | 'fail' | 'warn';
  responseTime?: number;
  message?: string;
  details?: Record<string, any>;
}

// Metadata types
export interface DatabaseMetadata {
  nodeLabels: Array<{
    label: string;
    count: number;
    properties: string[];
  }>;
  relationshipTypes: Array<{
    type: string;
    count: number;
    properties: string[];
  }>;
  indexes: Array<{
    name: string;
    type: string;
    labels: string[];
    properties: string[];
  }>;
  constraints: Array<{
    name: string;
    type: string;
    label: string;
    properties: string[];
  }>;
  statistics: {
    totalNodes: number;
    totalRelationships: number;
    databaseSize: string;
    lastUpdated: string;
  };
}

// Authentication types
export interface AuthRequest {
  username?: string;
  email?: string;
  password: string;
}

export interface AuthResponse {
  token: string;
  refreshToken: string;
  user: {
    id: string;
    username: string;
    email: string;
    roles: string[];
  };
  expiresAt: string;
}

export interface TokenPayload {
  userId: string;
  username: string;
  roles: string[];
  iat: number;
  exp: number;
}

// Rate limiting types
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number;
  retryAfter?: number;
}

// Metrics types
export interface ApiMetrics {
  requests: {
    total: number;
    successful: number;
    failed: number;
    averageResponseTime: number;
  };
  endpoints: Record<
    string,
    {
      count: number;
      averageResponseTime: number;
      errorRate: number;
    }
  >;
  errors: Array<{
    code: string;
    count: number;
    lastOccurred: string;
  }>;
  performance: {
    p50: number;
    p95: number;
    p99: number;
  };
}

// WebSocket types
export interface WebSocketMessage<T = any> {
  type: string;
  payload: T;
  timestamp: string;
  id: string;
}

export interface WebSocketResponse<T = any> extends WebSocketMessage<T> {
  success: boolean;
  error?: string;
}

// File upload types
export interface FileUploadRequest {
  file: File | Buffer;
  filename: string;
  mimeType: string;
  size: number;
  metadata?: Record<string, any>;
}

export interface FileUploadResponse {
  id: string;
  filename: string;
  url: string;
  size: number;
  uploadedAt: string;
}
