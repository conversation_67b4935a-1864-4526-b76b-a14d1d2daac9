/**
 * Shared constants
 */

// API endpoints
export const API_ENDPOINTS = {
  HEALTH: '/api/health',
  METADATA: '/api/metadata',
  FEATURE_FLAGS: '/api/feature-flags',
  GRAPH_NODES: '/api/graph/nodes',
  GRAPH_RELATIONSHIPS: '/api/graph/relationships',
  CHAT: '/api/chat',
  METRICS: '/metrics',
} as const;

// HTTP status codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

// Error codes
export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
  FEATURE_DISABLED: 'FEATURE_DISABLED',
  MIGRATION_ERROR: 'MIGRATION_ERROR',
} as const;

// Feature flag names
export const FEATURE_FLAGS = {
  NEW_CONTROLLER_LAYER: 'NEW_CONTROLLER_LAYER',
  NEW_SERVICE_LAYER: 'NEW_SERVICE_LAYER',
  NEW_REPOSITORY_PATTERN: 'NEW_REPOSITORY_PATTERN',
  NEW_MIDDLEWARE_STACK: 'NEW_MIDDLEWARE_STACK',
  NEW_CHAT_SERVICE: 'NEW_CHAT_SERVICE',
  CIRCUIT_BREAKER_ENABLED: 'CIRCUIT_BREAKER_ENABLED',
  DUAL_EXECUTION_MODE: 'DUAL_EXECUTION_MODE',
  PERFORMANCE_MONITORING: 'PERFORMANCE_MONITORING',
  AUTOMATIC_ROLLBACK: 'AUTOMATIC_ROLLBACK',
  TRAFFIC_PERCENTAGE_NEW_API: 'TRAFFIC_PERCENTAGE_NEW_API',
  DEBUG_MODE: 'DEBUG_MODE',
  VERBOSE_LOGGING: 'VERBOSE_LOGGING',
  MIGRATION_METRICS: 'MIGRATION_METRICS',
} as const;

// Default configuration values
export const DEFAULT_CONFIG = {
  API_PORT: 3002,
  UI_PORT: 3000,
  CHAT_PORT: 8000,
  DATABASE_URI: 'neo4j://localhost:7687',
  RATE_LIMIT_WINDOW: 15 * 60 * 1000, // 15 minutes
  RATE_LIMIT_MAX: 100,
  PAGINATION_DEFAULT_LIMIT: 20,
  PAGINATION_MAX_LIMIT: 100,
  CACHE_TTL: 5 * 60 * 1000, // 5 minutes
  REQUEST_TIMEOUT: 30 * 1000, // 30 seconds
  MONITORING_INTERVAL: 30 * 1000, // 30 seconds
  ROLLBACK_THRESHOLD_MULTIPLIER: 3.0,
} as const;

// Graph visualization constants
export const GRAPH_CONFIG = {
  MAX_NODES: 1000,
  MAX_RELATIONSHIPS: 2000,
  DEFAULT_NODE_SIZE: 20,
  DEFAULT_EDGE_WIDTH: 2,
  LAYOUT_ITERATIONS: 100,
  PHYSICS_ENABLED: true,
  CLUSTERING_THRESHOLD: 50,
} as const;

// Chat constants
export const CHAT_CONFIG = {
  MAX_MESSAGE_LENGTH: 4000,
  MAX_HISTORY_LENGTH: 50,
  TYPING_INDICATOR_DELAY: 1000,
  MESSAGE_RETRY_ATTEMPTS: 3,
  CONTEXT_WINDOW_SIZE: 8000,
} as const;

// Monitoring constants
export const MONITORING = {
  ALERT_SEVERITIES: ['info', 'warning', 'error', 'critical'] as const,
  METRIC_RETENTION_DAYS: 30,
  PERFORMANCE_BASELINE_WINDOW: 7, // days
  HEALTH_CHECK_INTERVAL: 30000, // 30 seconds
  ALERT_COOLDOWN: 5 * 60 * 1000, // 5 minutes
} as const;

// Migration constants
export const MIGRATION = {
  PHASES: [
    'Pre-Migration Safety Setup',
    'Foundation & Infrastructure',
    'Backend Architecture Refactoring',
    'Frontend Architecture Modernization',
    'Service Extraction & Microservices',
    'Comprehensive Testing & Validation',
    'Deployment & Monitoring',
    'Post-Migration Optimization',
  ] as const,
  ROLLBACK_REASONS: [
    'Performance Degradation',
    'High Error Rate',
    'User Complaints',
    'System Instability',
    'Security Concerns',
    'Data Inconsistency',
  ] as const,
  TRAFFIC_ROLLOUT_STEPS: [0, 10, 25, 50, 75, 100] as const,
} as const;

// Environment constants
export const ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  STAGING: 'staging',
  PRODUCTION: 'production',
  TEST: 'test',
} as const;

// Log levels
export const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug',
  VERBOSE: 'verbose',
} as const;
