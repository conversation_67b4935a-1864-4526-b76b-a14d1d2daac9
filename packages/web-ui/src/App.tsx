/**
 * Main App Component
 * 
 * Root component that sets up the feature-slice architecture
 * and provides the main application layout.
 */

import React from 'react';
import { GraphVisualizationProvider } from './features/graph-visualization';
import { GraphCanvas } from './features/graph-visualization';
import { featureBus } from './shared/infrastructure/FeatureBus';

export const App: React.FC = () => {
  React.useEffect(() => {
    // Initialize feature bus and register features
    console.log('Initializing Knowledge Graph Visualizer with Feature-Slice Architecture');
    
    // Log feature bus health
    const health = featureBus.getFeatureHealth();
    console.log('Feature Bus Health:', health);
    
    return () => {
      // Cleanup on unmount
      featureBus.cleanup();
    };
  }, []);

  return (
    <div className="app">
      <header className="app-header">
        <h1>Knowledge Graph Visualizer</h1>
        <p>Feature-Slice Architecture Demo</p>
      </header>
      
      <main className="app-main">
        <GraphVisualizationProvider>
          <div className="graph-container">
            <GraphCanvas 
              width={800} 
              height={600}
              onNodeClick={(node, event) => {
                console.log('Node clicked:', node, event);
              }}
              onCanvasClick={(event) => {
                console.log('Canvas clicked:', event);
              }}
            />
          </div>
        </GraphVisualizationProvider>
      </main>
      
      <footer className="app-footer">
        <p>Powered by Feature-Slice Architecture</p>
      </footer>
    </div>
  );
};
