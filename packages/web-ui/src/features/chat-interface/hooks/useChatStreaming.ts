/**
 * Chat Streaming Hook
 * 
 * Hook for managing streaming chat responses with proper state management
 * and error handling.
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { ChatMessage, UseChatStreamingReturn } from '../types';
import { useChat } from '../providers/ChatProvider';
import { chatApiService } from '../services/ChatApiService';

export const useChatStreaming = (): UseChatStreamingReturn => {
  const { state, actions, selectors } = useChat();
  const [streamingMessage, setStreamingMessage] = useState<ChatMessage | null>(null);
  const [streamError, setStreamError] = useState<string | null>(null);
  
  const readerRef = useRef<ReadableStreamDefaultReader | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  
  const isStreaming = state.isStreaming;
  const currentSession = selectors.getCurrentSession();

  // Generate message ID
  const generateMessageId = useCallback(() => {
    return `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // Start streaming
  const startStreaming = useCallback(async (content: string) => {
    if (!currentSession) {
      throw new Error('No active session');
    }

    if (isStreaming) {
      throw new Error('Already streaming a response');
    }

    try {
      setStreamError(null);
      actions.setError(null);
      
      // Create user message
      const userMessage: ChatMessage = {
        id: generateMessageId(),
        content,
        role: 'user',
        timestamp: new Date().toISOString()
      };

      // Add user message immediately
      actions.sendMessage(content);

      // Start streaming assistant response
      const assistantMessageId = generateMessageId();
      const assistantMessage: ChatMessage = {
        id: assistantMessageId,
        content: '',
        role: 'assistant',
        timestamp: new Date().toISOString(),
        metadata: {
          streaming: true
        }
      };

      setStreamingMessage(assistantMessage);
      
      // Create abort controller for cancellation
      abortControllerRef.current = new AbortController();
      
      // Get stream from API
      const stream = await chatApiService.streamMessage(
        content,
        currentSession.id,
        { stream: true }
      );

      const reader = stream.getReader();
      readerRef.current = reader;
      
      const decoder = new TextDecoder();
      let accumulatedContent = '';
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) {
            break;
          }

          // Decode chunk
          const chunk = decoder.decode(value, { stream: true });
          
          // Parse streaming data (assuming SSE format)
          const lines = chunk.split('\n');
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));
                
                switch (data.type) {
                  case 'token':
                    accumulatedContent += data.token;
                    setStreamingMessage(prev => prev ? {
                      ...prev,
                      content: accumulatedContent
                    } : null);
                    break;
                    
                  case 'complete':
                    // Finalize message
                    const finalMessage: ChatMessage = {
                      ...assistantMessage,
                      content: accumulatedContent,
                      metadata: {
                        ...assistantMessage.metadata,
                        streaming: false,
                        tokens: data.tokens,
                        model: data.model,
                        provider: data.provider,
                        responseTime: data.responseTime,
                        sources: data.sources || [],
                        reasoning: data.reasoning,
                        confidence: data.confidence
                      }
                    };
                    
                    // Add final message to session
                    // Note: This would need to be implemented in the provider
                    // actions.addMessage(currentSession.id, finalMessage);
                    
                    setStreamingMessage(null);
                    return;
                    
                  case 'error':
                    throw new Error(data.message || 'Streaming error');
                    
                  default:
                    console.warn('Unknown streaming event type:', data.type);
                }
              } catch (parseError) {
                console.warn('Failed to parse streaming data:', line, parseError);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
        readerRef.current = null;
      }
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Streaming failed';
      setStreamError(errorMessage);
      setStreamingMessage(null);
      actions.setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      abortControllerRef.current = null;
    }
  }, [currentSession, isStreaming, actions, generateMessageId]);

  // Stop streaming
  const stopStreaming = useCallback(() => {
    try {
      // Cancel the stream
      if (readerRef.current) {
        readerRef.current.cancel();
        readerRef.current = null;
      }
      
      // Abort the request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
      
      // Clear streaming state
      setStreamingMessage(null);
      setStreamError(null);
      actions.stopStreaming();
      
    } catch (error) {
      console.error('Error stopping stream:', error);
    }
  }, [actions]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopStreaming();
    };
  }, [stopStreaming]);

  return {
    isStreaming,
    streamingMessage,
    startStreaming,
    stopStreaming,
    error: streamError
  };
};
