/**
 * Chat Session Hook
 * 
 * Hook for managing individual chat sessions with proper state management
 * and error handling.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { ChatSession, ChatMessage, UseChatSessionReturn } from '../types';
import { useChat } from '../providers/ChatProvider';

export const useChatSession = (sessionId?: string): UseChatSessionReturn => {
  const { state, actions, selectors } = useChat();
  const [localError, setLocalError] = useState<string | null>(null);
  const [localLoading, setLocalLoading] = useState(false);
  
  const currentSession = selectors.getCurrentSession();
  const isCurrentSession = currentSession?.id === sessionId;
  
  // Get session from state or find by ID
  const session = sessionId 
    ? selectors.getSessionById(sessionId) || currentSession
    : currentSession;
  
  const messages = session?.messages || [];
  const isLoading = localLoading || state.isLoading;
  const error = localError || state.error;

  // Load session if not current
  useEffect(() => {
    if (sessionId && !isCurrentSession && sessionId !== currentSession?.id) {
      const loadSessionAsync = async () => {
        try {
          setLocalLoading(true);
          setLocalError(null);
          await actions.loadSession(sessionId);
        } catch (err) {
          setLocalError(err instanceof Error ? err.message : 'Failed to load session');
        } finally {
          setLocalLoading(false);
        }
      };
      
      loadSessionAsync();
    }
  }, [sessionId, isCurrentSession, currentSession?.id, actions]);

  // Send message to current session
  const sendMessage = useCallback(async (content: string) => {
    if (!session) {
      throw new Error('No active session');
    }
    
    try {
      setLocalError(null);
      await actions.sendMessage(content);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send message';
      setLocalError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [session, actions]);

  // Load specific session
  const loadSession = useCallback(async (targetSessionId: string) => {
    try {
      setLocalLoading(true);
      setLocalError(null);
      await actions.loadSession(targetSessionId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load session';
      setLocalError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLocalLoading(false);
    }
  }, [actions]);

  // Create new session
  const createSession = useCallback(async (title?: string) => {
    try {
      setLocalLoading(true);
      setLocalError(null);
      const newSession = await actions.createSession(title);
      return newSession;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create session';
      setLocalError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLocalLoading(false);
    }
  }, [actions]);

  return {
    session,
    messages,
    isLoading,
    error,
    sendMessage,
    loadSession,
    createSession
  };
};
