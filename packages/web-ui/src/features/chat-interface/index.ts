/**
 * Chat Interface Feature
 * 
 * Feature-slice for chat functionality including:
 * - Chat conversation management
 * - Message handling and streaming
 * - LLM integration
 * - Chat history and persistence
 */

// Placeholder exports for chat interface feature
// TODO: Implement actual chat interface components and services

// Type exports
export interface ChatMessage {
  id: string;
  content: string;
  timestamp: Date;
  type: 'user' | 'assistant';
}

export interface ChatState {
  messages: ChatMessage[];
  isLoading: boolean;
}

export interface ChatConfig {
  maxMessages: number;
  enableStreaming: boolean;
}
