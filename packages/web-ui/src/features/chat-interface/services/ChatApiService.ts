/**
 * Chat API Service
 * 
 * Modern API service for chat functionality with proper error handling,
 * retry logic, and performance monitoring.
 */

import axios, { AxiosInstance, CancelTokenSource } from 'axios';
import { ChatMessage, ChatSession, ChatOptions, ChatApiService } from '../types';

class ChatApiServiceImpl implements ChatApiService {
  private api: AxiosInstance;
  private activeRequests = new Map<string, CancelTokenSource>();
  private baseURL: string;

  constructor() {
    this.baseURL = (import.meta.env?.VITE_API_URL as string) || 'http://localhost:3002/api';
    
    this.api = axios.create({
      baseURL: `${this.baseURL}/chat`,
      timeout: 60000, // 60 seconds for LLM processing
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        // Add correlation ID for tracking
        config.headers['X-Correlation-ID'] = this.generateCorrelationId();
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (axios.isCancel(error)) {
          console.log('Request cancelled:', error.message);
          return Promise.reject(new Error('Request was cancelled'));
        }
        
        // Enhanced error handling
        if (error.response) {
          const { status, data } = error.response;
          switch (status) {
            case 400:
              throw new Error(data.message || 'Invalid request');
            case 401:
              throw new Error('Authentication required');
            case 403:
              throw new Error('Access denied');
            case 429:
              throw new Error('Rate limit exceeded. Please try again later.');
            case 500:
              throw new Error('Server error. Please try again.');
            default:
              throw new Error(data.message || `HTTP ${status} error`);
          }
        } else if (error.request) {
          throw new Error('Network error. Please check your connection.');
        } else {
          throw new Error(error.message || 'An unexpected error occurred');
        }
      }
    );
  }

  private generateCorrelationId(): string {
    return `chat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateMessageId(): string {
    return `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private async retryWithBackoff<T>(
    fn: () => Promise<T>,
    maxRetries = 3,
    delay = 1000
  ): Promise<T> {
    let lastError: Error = new Error('Unknown error');

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');

        if (attempt === maxRetries) {
          break;
        }

        // Don't retry on certain errors
        if (lastError.message.includes('cancelled') ||
            lastError.message.includes('401') ||
            lastError.message.includes('403')) {
          break;
        }

        // Exponential backoff
        const waitTime = delay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }

    throw lastError;
  }

  async sendMessage(
    content: string,
    sessionId: string,
    options: ChatOptions = {}
  ): Promise<ChatMessage> {
    const requestId = this.generateCorrelationId();
    const cancelToken = axios.CancelToken.source();
    
    // Store cancellation token
    this.activeRequests.set(requestId, cancelToken);
    
    try {
      const response = await this.retryWithBackoff(async () => {
        return await this.api.post('/message', {
          message: content,
          sessionId,
          options
        }, {
          cancelToken: cancelToken.token,
          headers: {
            'X-Request-ID': requestId
          }
        });
      });

      const { data } = response;
      
      // Create assistant message from response
      const assistantMessage: ChatMessage = {
        id: data.messageId || this.generateMessageId(),
        content: data.response || data.message || '',
        role: 'assistant',
        timestamp: new Date().toISOString(),
        metadata: {
          tokens: data.tokens,
          model: data.model,
          provider: data.provider,
          responseTime: data.responseTime,
          sources: data.sources || [],
          reasoning: data.reasoning,
          confidence: data.confidence
        }
      };

      return assistantMessage;
    } finally {
      this.activeRequests.delete(requestId);
    }
  }

  async getSession(sessionId: string): Promise<ChatSession> {
    try {
      const response = await this.retryWithBackoff(async () => {
        return await this.api.get(`/sessions/${sessionId}`);
      });

      return response.data;
    } catch (error) {
      throw new Error(`Failed to get session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async createSession(title?: string): Promise<ChatSession> {
    try {
      const response = await this.retryWithBackoff(async () => {
        return await this.api.post('/sessions', { title });
      });

      return response.data;
    } catch (error) {
      throw new Error(`Failed to create session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async updateSession(sessionId: string, updates: Partial<ChatSession>): Promise<ChatSession> {
    try {
      const response = await this.retryWithBackoff(async () => {
        return await this.api.patch(`/sessions/${sessionId}`, updates);
      });

      return response.data;
    } catch (error) {
      throw new Error(`Failed to update session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async deleteSession(sessionId: string): Promise<void> {
    try {
      await this.retryWithBackoff(async () => {
        return await this.api.delete(`/sessions/${sessionId}`);
      });
    } catch (error) {
      throw new Error(`Failed to delete session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getSessions(): Promise<ChatSession[]> {
    try {
      const response = await this.retryWithBackoff(async () => {
        return await this.api.get('/sessions');
      });

      return response.data;
    } catch (error) {
      throw new Error(`Failed to get sessions: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async streamMessage(
    content: string,
    sessionId: string,
    options: ChatOptions = {}
  ): Promise<ReadableStream> {
    const requestId = this.generateCorrelationId();
    
    try {
      const response = await fetch(`${this.baseURL}/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Request-ID': requestId,
          'X-Correlation-ID': this.generateCorrelationId()
        },
        body: JSON.stringify({
          message: content,
          sessionId,
          options
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      if (!response.body) {
        throw new Error('No response body for streaming');
      }

      return response.body;
    } catch (error) {
      throw new Error(`Failed to start streaming: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Cancel a specific request
  cancelRequest(requestId: string): void {
    const cancelToken = this.activeRequests.get(requestId);
    if (cancelToken) {
      cancelToken.cancel('Request cancelled by user');
      this.activeRequests.delete(requestId);
    }
  }

  // Cancel all active requests
  cancelAllRequests(): void {
    this.activeRequests.forEach((cancelToken) => {
      cancelToken.cancel('All requests cancelled');
    });
    this.activeRequests.clear();
  }

  // Get health status
  async getHealth(): Promise<{ status: string; timestamp: string }> {
    try {
      const response = await this.api.get('/health');
      return response.data;
    } catch (error) {
      throw new Error(`Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Get chat statistics
  async getStats(): Promise<{
    totalSessions: number;
    totalMessages: number;
    averageResponseTime: number;
    uptime: number;
  }> {
    try {
      const response = await this.api.get('/stats');
      return response.data;
    } catch (error) {
      throw new Error(`Failed to get stats: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

// Export singleton instance
export const chatApiService = new ChatApiServiceImpl();
