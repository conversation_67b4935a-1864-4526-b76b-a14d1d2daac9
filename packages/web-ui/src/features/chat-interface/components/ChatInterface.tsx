/**
 * Chat Interface Component
 * 
 * Main chat interface component with modern design and comprehensive functionality.
 */

import React, { useEffect, useCallback, useState } from 'react';
import { ChatInterfaceProps } from '../types';
import { useChat } from '../providers/ChatProvider';
import { useChatSession } from '../hooks/useChatSession';
import { useChatStreaming } from '../hooks/useChatStreaming';
import { MessageList } from './MessageList';
import { MessageInput } from './MessageInput';
import { SessionList } from './SessionList';
import { ChatSettings } from './ChatSettings';
import { ErrorBoundary } from '../../../shared/components/ErrorBoundary';
import { LoadingSpinner } from '../../../shared/components/LoadingSpinner';

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  className = '',
  style,
  placeholder = 'Ask a question about the knowledge graph...',
  maxHeight = 600,
  enableStreaming = true,
  enableSources = true,
  enableThinking = true,
  onNodeSelect,
  onError
}) => {
  const { actions, selectors } = useChat();
  const { session, messages, sendMessage } = useChatSession();
  const { isStreaming, streamingMessage, startStreaming, stopStreaming } = useChatStreaming();
  
  const [showSessions, setShowSessions] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  
  const inputValue = selectors.getInputValue();
  const isLoading = selectors.getIsLoading();
  const error = selectors.getError();
  const canSend = selectors.getCanSend();
  const sessions = selectors.getSessions();
  const showSources = selectors.getShowSources();
  const showThinking = selectors.getShowThinking();
  const showTimestamps = selectors.getShowTimestamps();
  const compactMode = selectors.getCompactMode();

  // Create initial session if none exists
  useEffect(() => {
    if (!session && sessions.length === 0) {
      actions.createSession('New Chat').catch(err => {
        console.error('Failed to create initial session:', err);
        onError?.(err);
      });
    }
  }, [session, sessions.length, actions, onError]);

  // Handle message sending
  const handleSendMessage = useCallback(async (content: string) => {
    if (!canSend || !content.trim()) return;

    try {
      if (enableStreaming) {
        await startStreaming(content);
      } else {
        await sendMessage(content);
      }
    } catch (err) {
      console.error('Failed to send message:', err);
      onError?.(err instanceof Error ? err : new Error('Failed to send message'));
    }
  }, [canSend, enableStreaming, startStreaming, sendMessage, onError]);

  // Handle input change
  const handleInputChange = useCallback((value: string) => {
    actions.setInputValue(value);
  }, [actions]);

  // Handle session selection
  const handleSessionSelect = useCallback((sessionId: string) => {
    actions.loadSession(sessionId).catch(err => {
      console.error('Failed to load session:', err);
      onError?.(err);
    });
    setShowSessions(false);
  }, [actions, onError]);

  // Handle session creation
  const handleSessionCreate = useCallback(() => {
    actions.createSession().then(() => {
      setShowSessions(false);
    }).catch(err => {
      console.error('Failed to create session:', err);
      onError?.(err);
    });
  }, [actions, onError]);

  // Handle session deletion
  const handleSessionDelete = useCallback((sessionId: string) => {
    actions.deleteSession(sessionId).catch(err => {
      console.error('Failed to delete session:', err);
      onError?.(err);
    });
  }, [actions, onError]);

  // Handle session rename
  const handleSessionRename = useCallback((sessionId: string, title: string) => {
    actions.updateSessionTitle(sessionId, title).catch(err => {
      console.error('Failed to rename session:', err);
      onError?.(err);
    });
  }, [actions, onError]);

  // Handle message actions
  const handleMessageEdit = useCallback((messageId: string, content: string) => {
    actions.editMessage(messageId, content);
  }, [actions]);

  const handleMessageDelete = useCallback((messageId: string) => {
    actions.deleteMessage(messageId);
  }, [actions]);

  const handleMessageRetry = useCallback((messageId: string) => {
    actions.retryMessage(messageId).catch(err => {
      console.error('Failed to retry message:', err);
      onError?.(err);
    });
  }, [actions, onError]);

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Escape') {
      if (isStreaming) {
        stopStreaming();
      } else if (showSessions) {
        setShowSessions(false);
      } else if (showSettings) {
        setShowSettings(false);
      }
    }
  }, [isStreaming, stopStreaming, showSessions, showSettings]);

  // Clear error when component unmounts or session changes
  useEffect(() => {
    return () => {
      actions.clearError();
    };
  }, [actions, session?.id]);

  const allMessages = [...messages];
  if (streamingMessage) {
    allMessages.push(streamingMessage);
  }

  return (
    <ErrorBoundary onError={onError}>
      <div 
        className={`chat-interface ${className}`}
        style={{
          display: 'flex',
          flexDirection: 'column',
          height: maxHeight,
          border: '1px solid #e1e5e9',
          borderRadius: '8px',
          backgroundColor: '#ffffff',
          overflow: 'hidden',
          ...style
        }}
        onKeyDown={handleKeyDown}
        tabIndex={-1}
      >
        {/* Header */}
        <div className="chat-header" style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '12px 16px',
          borderBottom: '1px solid #e1e5e9',
          backgroundColor: '#f8f9fa'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <button
              onClick={() => setShowSessions(!showSessions)}
              style={{
                padding: '6px 12px',
                border: '1px solid #d0d7de',
                borderRadius: '6px',
                backgroundColor: showSessions ? '#0969da' : '#ffffff',
                color: showSessions ? '#ffffff' : '#24292f',
                cursor: 'pointer',
                fontSize: '14px'
              }}
              title="Sessions"
            >
              💬 {sessions.length}
            </button>
            
            <h3 style={{ 
              margin: 0, 
              fontSize: '16px', 
              fontWeight: '600',
              color: '#24292f',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              maxWidth: '200px'
            }}>
              {session?.title || 'New Chat'}
            </h3>
          </div>
          
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {enableSources && (
              <button
                onClick={actions.toggleSources}
                style={{
                  padding: '4px 8px',
                  border: '1px solid #d0d7de',
                  borderRadius: '4px',
                  backgroundColor: showSources ? '#0969da' : '#ffffff',
                  color: showSources ? '#ffffff' : '#656d76',
                  cursor: 'pointer',
                  fontSize: '12px'
                }}
                title="Toggle Sources"
              >
                📚
              </button>
            )}
            
            {enableThinking && (
              <button
                onClick={actions.toggleThinking}
                style={{
                  padding: '4px 8px',
                  border: '1px solid #d0d7de',
                  borderRadius: '4px',
                  backgroundColor: showThinking ? '#0969da' : '#ffffff',
                  color: showThinking ? '#ffffff' : '#656d76',
                  cursor: 'pointer',
                  fontSize: '12px'
                }}
                title="Toggle Thinking Process"
              >
                🧠
              </button>
            )}
            
            <button
              onClick={() => setShowSettings(!showSettings)}
              style={{
                padding: '4px 8px',
                border: '1px solid #d0d7de',
                borderRadius: '4px',
                backgroundColor: showSettings ? '#0969da' : '#ffffff',
                color: showSettings ? '#ffffff' : '#656d76',
                cursor: 'pointer',
                fontSize: '12px'
              }}
              title="Settings"
            >
              ⚙️
            </button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div style={{
            padding: '8px 16px',
            backgroundColor: '#fff8f0',
            borderBottom: '1px solid #f1c40f',
            color: '#8b4513',
            fontSize: '14px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <span>⚠️ {error}</span>
            <button
              onClick={actions.clearError}
              style={{
                background: 'none',
                border: 'none',
                color: '#8b4513',
                cursor: 'pointer',
                fontSize: '16px'
              }}
            >
              ✕
            </button>
          </div>
        )}

        {/* Main Content */}
        <div style={{ 
          display: 'flex', 
          flex: 1, 
          overflow: 'hidden',
          position: 'relative'
        }}>
          {/* Sessions Sidebar */}
          {showSessions && (
            <div style={{
              width: '280px',
              borderRight: '1px solid #e1e5e9',
              backgroundColor: '#f8f9fa'
            }}>
              <SessionList
                sessions={sessions}
                currentSessionId={session?.id}
                onSessionSelect={handleSessionSelect}
                onSessionDelete={handleSessionDelete}
                onSessionCreate={handleSessionCreate}
                onSessionRename={handleSessionRename}
              />
            </div>
          )}

          {/* Chat Area */}
          <div style={{ 
            flex: 1, 
            display: 'flex', 
            flexDirection: 'column',
            overflow: 'hidden'
          }}>
            {/* Messages */}
            <div style={{ flex: 1, overflow: 'hidden' }}>
              {isLoading && allMessages.length === 0 ? (
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100%'
                }}>
                  <LoadingSpinner size="medium" />
                </div>
              ) : (
                <MessageList
                  messages={allMessages}
                  isStreaming={isStreaming}
                  showSources={showSources}
                  showThinking={showThinking}
                  showTimestamps={showTimestamps}
                  compactMode={compactMode}
                  onMessageEdit={handleMessageEdit}
                  onMessageDelete={handleMessageDelete}
                  onMessageRetry={handleMessageRetry}
                  onNodeSelect={onNodeSelect}
                />
              )}
            </div>

            {/* Input */}
            <div style={{
              borderTop: '1px solid #e1e5e9',
              backgroundColor: '#ffffff'
            }}>
              <MessageInput
                value={inputValue}
                placeholder={placeholder}
                disabled={!canSend}
                isLoading={isLoading || isStreaming}
                onSend={handleSendMessage}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
              />
            </div>
          </div>

          {/* Settings Sidebar */}
          {showSettings && (
            <div style={{
              width: '320px',
              borderLeft: '1px solid #e1e5e9',
              backgroundColor: '#f8f9fa'
            }}>
              <ChatSettings onClose={() => setShowSettings(false)} />
            </div>
          )}
        </div>
      </div>
    </ErrorBoundary>
  );
};
