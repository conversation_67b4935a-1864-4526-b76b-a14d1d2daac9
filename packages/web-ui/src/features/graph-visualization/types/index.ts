/**
 * Graph Visualization Types
 * 
 * Type definitions for the graph visualization feature.
 */

// Core graph data structures
export interface GraphNode {
  id: string;
  label: string;
  type: string;
  properties: Record<string, any>;
  position?: { x: number; y: number };
  size?: number;
  color?: string;
  shape?: string;
  selected?: boolean;
  highlighted?: boolean;
  visible?: boolean;
}

export interface GraphEdge {
  id: string;
  source: string;
  target: string;
  type: string;
  label?: string;
  properties: Record<string, any>;
  weight?: number;
  color?: string;
  style?: string;
  selected?: boolean;
  highlighted?: boolean;
  visible?: boolean;
}

export interface GraphData {
  nodes: GraphNode[];
  links: GraphEdge[];
  metadata?: {
    totalNodes: number;
    totalEdges: number;
    nodeTypes: string[];
    edgeTypes: string[];
    lastUpdated: Date;
  };
}

// Graph configuration
export interface GraphConfig {
  layout: LayoutConfig;
  interaction: InteractionConfig;
  rendering: RenderingConfig;
  performance: PerformanceConfig;
}

export interface LayoutConfig {
  algorithm: 'force' | 'hierarchical' | 'circular' | 'grid' | 'custom';
  forceStrength: number;
  linkDistance: number;
  nodeRepulsion: number;
  centerForce: number;
  collisionRadius: number;
  alphaDecay: number;
  velocityDecay: number;
  iterations: number;
  stabilization: boolean;
}

export interface InteractionConfig {
  dragging: boolean;
  zooming: boolean;
  panning: boolean;
  selection: boolean;
  multiSelect: boolean;
  hover: boolean;
  doubleClick: boolean;
  contextMenu: boolean;
  keyboard: boolean;
}

export interface RenderingConfig {
  nodeSize: { min: number; max: number; default: number };
  edgeWidth: { min: number; max: number; default: number };
  colors: {
    nodes: Record<string, string>;
    edges: Record<string, string>;
    background: string;
    selection: string;
    highlight: string;
  };
  shapes: Record<string, string>;
  labels: {
    nodes: boolean;
    edges: boolean;
    fontSize: number;
    fontFamily: string;
    color: string;
  };
  animations: {
    enabled: boolean;
    duration: number;
    easing: string;
  };
}

export interface PerformanceConfig {
  maxNodes: number;
  maxEdges: number;
  levelOfDetail: boolean;
  clustering: boolean;
  virtualization: boolean;
  webWorkers: boolean;
  batchSize: number;
  throttleMs: number;
}

// Graph state
export interface VisualizationState {
  data: GraphData;
  config: GraphConfig;
  viewport: {
    zoom: number;
    pan: { x: number; y: number };
    bounds: { width: number; height: number };
  };
  selection: {
    nodes: string[];
    edges: string[];
    mode: 'single' | 'multiple' | 'area';
  };
  interaction: {
    isDragging: boolean;
    isPanning: boolean;
    isZooming: boolean;
    hoveredNode: string | null;
    hoveredEdge: string | null;
  };
  layout: {
    isRunning: boolean;
    progress: number;
    algorithm: string;
    iterations: number;
  };
  performance: {
    fps: number;
    renderTime: number;
    nodeCount: number;
    edgeCount: number;
    visibleNodes: number;
    visibleEdges: number;
  };
}

// Graph events
export interface GraphEvent {
  type: string;
  target: 'node' | 'edge' | 'canvas' | 'viewport';
  data: any;
  position?: { x: number; y: number };
  modifiers?: {
    shift: boolean;
    ctrl: boolean;
    alt: boolean;
  };
}

// Node and edge styling
export interface NodeStyle {
  size: number;
  color: string;
  shape: string;
  borderColor?: string;
  borderWidth?: number;
  opacity?: number;
  label?: {
    text: string;
    fontSize: number;
    color: string;
    position: 'center' | 'top' | 'bottom' | 'left' | 'right';
  };
}

export interface EdgeStyle {
  width: number;
  color: string;
  style: 'solid' | 'dashed' | 'dotted';
  opacity?: number;
  arrow?: {
    enabled: boolean;
    size: number;
    type: 'triangle' | 'circle' | 'square';
  };
  label?: {
    text: string;
    fontSize: number;
    color: string;
    position: number; // 0-1, position along edge
  };
}

// Layout algorithms
export interface LayoutAlgorithm {
  name: string;
  description: string;
  parameters: Record<string, any>;
  apply: (data: GraphData, config: LayoutConfig) => Promise<GraphData>;
  stop: () => void;
  isRunning: () => boolean;
}

// Graph filters
export interface GraphFilter {
  id: string;
  name: string;
  description: string;
  type: 'node' | 'edge' | 'both';
  criteria: FilterCriteria[];
  enabled: boolean;
}

export interface FilterCriteria {
  property: string;
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'greaterThan' | 'lessThan' | 'in' | 'notIn';
  value: any;
  caseSensitive?: boolean;
}

// Graph analysis results
export interface GraphAnalysisResult {
  type: string;
  data: any;
  metadata: {
    algorithm: string;
    parameters: Record<string, any>;
    executionTime: number;
    timestamp: Date;
  };
}

// Export and import formats
export interface GraphExportOptions {
  format: 'json' | 'graphml' | 'gexf' | 'csv' | 'png' | 'svg' | 'pdf';
  includeMetadata: boolean;
  includeLayout: boolean;
  includeStyles: boolean;
  compression?: boolean;
  quality?: number; // For image formats
}

export interface GraphImportOptions {
  format: 'json' | 'graphml' | 'gexf' | 'csv';
  mapping?: {
    nodeId: string;
    nodeLabel: string;
    nodeType: string;
    edgeSource: string;
    edgeTarget: string;
    edgeType: string;
  };
  validation: boolean;
  mergeStrategy: 'replace' | 'merge' | 'append';
}

// Graph metrics
export interface GraphMetrics {
  nodes: {
    total: number;
    byType: Record<string, number>;
    isolated: number;
    maxDegree: number;
    avgDegree: number;
  };
  edges: {
    total: number;
    byType: Record<string, number>;
    selfLoops: number;
    multiEdges: number;
  };
  connectivity: {
    components: number;
    density: number;
    diameter: number;
    averagePathLength: number;
  };
  clustering: {
    coefficient: number;
    communities: number;
    modularity: number;
  };
}
