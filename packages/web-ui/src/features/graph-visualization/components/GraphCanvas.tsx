/**
 * Graph Canvas Component
 * 
 * Main canvas component for rendering the graph visualization.
 * Handles SVG rendering, interactions, and performance optimization.
 */

import React, { useRef, useEffect, useCallback, useMemo } from 'react';
import { useGraphVisualization } from '../providers/GraphVisualizationProvider';
import { GraphNode, GraphEdge, GraphEvent } from '../types';
import { FeatureComponentProps } from '../../../shared/types/feature';

interface GraphCanvasProps extends FeatureComponentProps {
  width?: number;
  height?: number;
  onNodeClick?: (node: GraphNode, event: GraphEvent) => void;
  onEdgeClick?: (edge: GraphEdge, event: GraphEvent) => void;
  onCanvasClick?: (event: GraphEvent) => void;
  onNodeHover?: (node: GraphNode | null, event: GraphEvent) => void;
  onEdgeHover?: (edge: GraphEdge | null, event: GraphEvent) => void;
}

export const GraphCanvas: React.FC<GraphCanvasProps> = ({
  width = 800,
  height = 600,
  className = '',
  style,
  testId = 'graph-canvas',
  onNodeClick,
  onEdgeClick,
  onCanvasClick,
  onNodeHover,
  onError
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const { actions, selectors } = useGraphVisualization();
  
  const graphData = selectors.getGraphData();
  const config = selectors.getConfig();
  const viewport = selectors.getViewport();
  const selection = selectors.getSelection();
  const hoveredNode = selectors.getHoveredNode();

  // Handle node click
  const handleNodeClick = useCallback((node: GraphNode, event: React.MouseEvent) => {
    try {
      const graphEvent: GraphEvent = {
        type: 'node:click',
        target: 'node',
        data: node,
        position: { x: event.clientX, y: event.clientY },
        modifiers: {
          shift: event.shiftKey,
          ctrl: event.ctrlKey,
          alt: event.altKey
        }
      };

      // Handle selection
      if (config.interaction.selection) {
        if (config.interaction.multiSelect && event.ctrlKey) {
          const currentSelection = selection.nodes;
          const newSelection = currentSelection.includes(node.id)
            ? currentSelection.filter(id => id !== node.id)
            : [...currentSelection, node.id];
          actions.selectNodes(newSelection);
        } else {
          actions.selectNodes([node.id]);
        }
      }

      onNodeClick?.(node, graphEvent);
    } catch (error) {
      onError?.(error as Error);
    }
  }, [config, selection, actions, onNodeClick, onError]);

  // Handle node hover
  const handleNodeHover = useCallback((node: GraphNode | null, event: React.MouseEvent) => {
    try {
      if (config.interaction.hover) {
        actions.setHoveredNode(node?.id || null);
      }

      if (node) {
        const graphEvent: GraphEvent = {
          type: 'node:hover',
          target: 'node',
          data: node,
          position: { x: event.clientX, y: event.clientY }
        };
        onNodeHover?.(node, graphEvent);
      } else {
        const graphEvent: GraphEvent = {
          type: 'node:hover:end',
          target: 'node',
          data: null,
          position: { x: event.clientX, y: event.clientY }
        };
        onNodeHover?.(null, graphEvent);
      }
    } catch (error) {
      onError?.(error as Error);
    }
  }, [config, actions, onNodeHover, onError]);

  // Handle canvas click
  const handleCanvasClick = useCallback((event: React.MouseEvent) => {
    try {
      if (event.target === svgRef.current) {
        actions.clearSelection();
        
        const graphEvent: GraphEvent = {
          type: 'canvas:click',
          target: 'canvas',
          data: null,
          position: { x: event.clientX, y: event.clientY },
          modifiers: {
            shift: event.shiftKey,
            ctrl: event.ctrlKey,
            alt: event.altKey
          }
        };
        
        onCanvasClick?.(graphEvent);
      }
    } catch (error) {
      onError?.(error as Error);
    }
  }, [actions, onCanvasClick, onError]);

  // Calculate node style
  const getNodeStyle = useCallback((node: GraphNode) => {
    const isSelected = selection.nodes.includes(node.id);
    const isHovered = hoveredNode === node.id;
    const baseSize = config.rendering.nodeSize.default;
    
    let color = config.rendering.colors.nodes.default;
    if (isSelected) color = config.rendering.colors.nodes.selected;
    if (isHovered) color = config.rendering.colors.nodes.highlighted;
    
    return {
      fill: node.color || color,
      stroke: isSelected ? config.rendering.colors.selection : 'none',
      strokeWidth: isSelected ? 2 : 0,
      r: node.size || baseSize,
      opacity: node.visible !== false ? 1 : 0.3
    };
  }, [config, selection, hoveredNode]);

  // Calculate edge style
  const getEdgeStyle = useCallback((edge: GraphEdge) => {
    const isSelected = selection.edges.includes(edge.id);
    const baseWidth = config.rendering.edgeWidth.default;
    
    let color = config.rendering.colors.edges.default;
    if (isSelected) color = config.rendering.colors.edges.selected;
    
    return {
      stroke: edge.color || color,
      strokeWidth: edge.weight || baseWidth,
      opacity: edge.visible !== false ? 0.6 : 0.2,
      strokeDasharray: edge.style === 'dashed' ? '5,5' : 'none'
    };
  }, [config, selection]);

  // Transform coordinates based on viewport
  const transformPoint = useCallback((x: number, y: number) => {
    const { zoom, pan } = viewport;
    return {
      x: (x + pan.x) * zoom,
      y: (y + pan.y) * zoom
    };
  }, [viewport]);

  // Render nodes
  const renderNodes = useMemo(() => {
    return graphData.nodes.map(node => {
      if (!node.position) return null;
      
      const { x, y } = transformPoint(node.position.x, node.position.y);
      const style = getNodeStyle(node);
      
      return (
        <g key={node.id} className="graph-node">
          <circle
            cx={x}
            cy={y}
            {...style}
            onClick={(e) => handleNodeClick(node, e)}
            onMouseEnter={(e) => handleNodeHover(node, e)}
            onMouseLeave={(e) => handleNodeHover(null, e)}
            style={{ cursor: config.interaction.dragging ? 'grab' : 'pointer' }}
          />
          {config.rendering.labels.nodes && (
            <text
              x={x}
              y={y + style.r + 15}
              textAnchor="middle"
              fontSize={config.rendering.labels.fontSize}
              fontFamily={config.rendering.labels.fontFamily}
              fill={config.rendering.labels.color}
              pointerEvents="none"
            >
              {node.label}
            </text>
          )}
        </g>
      );
    }).filter(Boolean);
  }, [graphData.nodes, transformPoint, getNodeStyle, handleNodeClick, handleNodeHover, config]);

  // Render edges
  const renderEdges = useMemo(() => {
    return graphData.links.map(edge => {
      const sourceNode = graphData.nodes.find(n => n.id === edge.source);
      const targetNode = graphData.nodes.find(n => n.id === edge.target);
      
      if (!sourceNode?.position || !targetNode?.position) return null;
      
      const source = transformPoint(sourceNode.position.x, sourceNode.position.y);
      const target = transformPoint(targetNode.position.x, targetNode.position.y);
      const style = getEdgeStyle(edge);
      
      return (
        <g key={edge.id} className="graph-edge">
          <line
            x1={source.x}
            y1={source.y}
            x2={target.x}
            y2={target.y}
            {...style}
            onClick={(e) => onEdgeClick?.(edge, {
              type: 'edge:click',
              target: 'edge',
              data: edge,
              position: { x: e.clientX, y: e.clientY }
            })}
            style={{ cursor: 'pointer' }}
          />
          {config.rendering.labels.edges && edge.label && (
            <text
              x={(source.x + target.x) / 2}
              y={(source.y + target.y) / 2}
              textAnchor="middle"
              fontSize={config.rendering.labels.fontSize}
              fontFamily={config.rendering.labels.fontFamily}
              fill={config.rendering.labels.color}
              pointerEvents="none"
            >
              {edge.label}
            </text>
          )}
        </g>
      );
    }).filter(Boolean);
  }, [graphData.links, graphData.nodes, transformPoint, getEdgeStyle, onEdgeClick, config]);

  // Update viewport bounds when size changes
  useEffect(() => {
    actions.updateConfig({
      ...config,
      rendering: {
        ...config.rendering,
        // Update any size-dependent settings
      }
    });
  }, [width, height]);

  return (
    <div 
      className={`graph-canvas-container ${className}`}
      style={{ width, height, ...style }}
      data-testid={testId}
    >
      <svg
        ref={svgRef}
        width={width}
        height={height}
        viewBox={`0 0 ${width} ${height}`}
        style={{ 
          background: config.rendering.colors.background,
          cursor: config.interaction.panning ? 'grab' : 'default'
        }}
        onClick={handleCanvasClick}
      >
        {/* Background grid (optional) */}
        <defs>
          <pattern
            id="grid"
            width="20"
            height="20"
            patternUnits="userSpaceOnUse"
          >
            <path
              d="M 20 0 L 0 0 0 20"
              fill="none"
              stroke="#f0f0f0"
              strokeWidth="1"
            />
          </pattern>
        </defs>
        
        {/* Render edges first (behind nodes) */}
        <g className="edges-layer">
          {renderEdges}
        </g>
        
        {/* Render nodes */}
        <g className="nodes-layer">
          {renderNodes}
        </g>
      </svg>
      
      {/* Performance overlay */}
      {process.env.NODE_ENV === 'development' && (
        <div className="performance-overlay" style={{
          position: 'absolute',
          top: 10,
          right: 10,
          background: 'rgba(0,0,0,0.7)',
          color: 'white',
          padding: '5px 10px',
          borderRadius: '3px',
          fontSize: '12px',
          fontFamily: 'monospace'
        }}>
          Nodes: {graphData.nodes.length} | Edges: {graphData.links.length}
        </div>
      )}
    </div>
  );
};
