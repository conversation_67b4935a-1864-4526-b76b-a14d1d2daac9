/**
 * Enhanced Graph Canvas Component
 *
 * Advanced SVG-based graph visualization with data management, layout algorithms,
 * and comprehensive interaction support.
 */

import React, { useRef, useEffect, useCallback, useMemo } from 'react';
import { useGraphVisualization } from '../providers/GraphVisualizationProvider';
import { useGraphData } from '../hooks/useGraphData';
import { useGraphLayout } from '../hooks/useGraphLayout';
import { useGraphInteraction } from '../hooks/useGraphInteraction';
import { GraphNode, GraphEdge, GraphEvent } from '../types';
import { FeatureComponentProps } from '../../../shared/types/feature';

interface GraphCanvasProps extends FeatureComponentProps {
  width?: number;
  height?: number;
  autoLoad?: boolean;
  layoutAlgorithm?: 'force' | 'circular' | 'grid' | 'hierarchical';
  enableInteractions?: boolean;
  enableDataManagement?: boolean;
  onNodeClick?: (node: GraphNode, event: GraphEvent) => void;
  onEdgeClick?: (edge: GraphEdge, event: GraphEvent) => void;
  onCanvasClick?: (event: GraphEvent) => void;
  onNodeHover?: (node: GraphNode | null, event: GraphEvent) => void;
  onEdgeHover?: (edge: GraphEdge | null, event: GraphEvent) => void;
  onDataLoaded?: (nodeCount: number, edgeCount: number) => void;
  onLayoutComplete?: (algorithm: string) => void;
}

export const GraphCanvas: React.FC<GraphCanvasProps> = ({
  width = 800,
  height = 600,
  className = '',
  style,
  testId = 'graph-canvas',
  autoLoad = true,
  layoutAlgorithm = 'force',
  enableInteractions = true,
  enableDataManagement = true,
  onNodeClick,
  onEdgeClick,
  onCanvasClick,
  onNodeHover,
  onDataLoaded,
  onLayoutComplete,
  onError
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const { actions, selectors } = useGraphVisualization();

  // Enhanced hooks for data management, layout, and interactions
  const [dataState, dataActions] = useGraphData({
    autoLoad: enableDataManagement ? autoLoad : false,
    cacheTimeout: 300000,
    enableRealtime: false,
    batchSize: 100
  });

  const [layoutState, layoutActions] = useGraphLayout();
  const [interactionState, interactionActions] = useGraphInteraction();

  const graphData = selectors.getGraphData();
  const config = selectors.getConfig();
  const viewport = selectors.getViewport();
  const selection = selectors.getSelection();
  const hoveredNode = selectors.getHoveredNode();

  // Handle node click
  const handleNodeClick = useCallback((node: GraphNode, event: React.MouseEvent) => {
    try {
      const graphEvent: GraphEvent = {
        type: 'node:click',
        target: 'node',
        data: node,
        position: { x: event.clientX, y: event.clientY },
        modifiers: {
          shift: event.shiftKey,
          ctrl: event.ctrlKey,
          alt: event.altKey
        }
      };

      // Handle selection
      if (config.interaction.selection) {
        if (config.interaction.multiSelect && event.ctrlKey) {
          const currentSelection = selection.nodes;
          const newSelection = currentSelection.includes(node.id)
            ? currentSelection.filter(id => id !== node.id)
            : [...currentSelection, node.id];
          actions.selectNodes(newSelection);
        } else {
          actions.selectNodes([node.id]);
        }
      }

      onNodeClick?.(node, graphEvent);
    } catch (error) {
      onError?.(error as Error);
    }
  }, [config, selection, actions, onNodeClick, onError]);

  // Handle node hover
  const handleNodeHover = useCallback((node: GraphNode | null, event: React.MouseEvent) => {
    try {
      if (config.interaction.hover) {
        actions.setHoveredNode(node?.id || null);
      }

      if (node) {
        const graphEvent: GraphEvent = {
          type: 'node:hover',
          target: 'node',
          data: node,
          position: { x: event.clientX, y: event.clientY }
        };
        onNodeHover?.(node, graphEvent);
      } else {
        const graphEvent: GraphEvent = {
          type: 'node:hover:end',
          target: 'node',
          data: null,
          position: { x: event.clientX, y: event.clientY }
        };
        onNodeHover?.(null, graphEvent);
      }
    } catch (error) {
      onError?.(error as Error);
    }
  }, [config, actions, onNodeHover, onError]);

  // Enhanced interaction handlers
  const handleEnhancedNodeMouseDown = useCallback((node: GraphNode, event: React.MouseEvent) => {
    if (enableInteractions) {
      const mouseEvent = event.nativeEvent as MouseEvent;
      interactionActions.handleNodeMouseDown(node, mouseEvent);
    }
  }, [enableInteractions, interactionActions]);

  const handleEnhancedNodeMouseUp = useCallback((node: GraphNode, event: React.MouseEvent) => {
    if (enableInteractions) {
      const mouseEvent = event.nativeEvent as MouseEvent;
      interactionActions.handleNodeMouseUp(node, mouseEvent);
    }
  }, [enableInteractions, interactionActions]);

  const handleEnhancedCanvasMouseMove = useCallback((event: React.MouseEvent) => {
    if (enableInteractions) {
      const mouseEvent = event.nativeEvent as MouseEvent;
      interactionActions.handleCanvasMouseMove(mouseEvent);
    }
  }, [enableInteractions, interactionActions]);

  const handleEnhancedCanvasMouseDown = useCallback((event: React.MouseEvent) => {
    if (enableInteractions) {
      const mouseEvent = event.nativeEvent as MouseEvent;
      interactionActions.handleCanvasMouseDown(mouseEvent);
    }
  }, [enableInteractions, interactionActions]);

  const handleEnhancedCanvasMouseUp = useCallback((event: React.MouseEvent) => {
    if (enableInteractions) {
      const mouseEvent = event.nativeEvent as MouseEvent;
      interactionActions.handleCanvasMouseUp(mouseEvent);
    }
  }, [enableInteractions, interactionActions]);

  const handleWheel = useCallback((event: React.WheelEvent) => {
    if (enableInteractions) {
      const wheelEvent = event.nativeEvent as WheelEvent;
      interactionActions.handleWheel(wheelEvent);
    }
  }, [enableInteractions, interactionActions]);

  // Handle canvas click
  const handleCanvasClick = useCallback((event: React.MouseEvent) => {
    try {
      if (event.target === svgRef.current) {
        actions.clearSelection();
        
        const graphEvent: GraphEvent = {
          type: 'canvas:click',
          target: 'canvas',
          data: null,
          position: { x: event.clientX, y: event.clientY },
          modifiers: {
            shift: event.shiftKey,
            ctrl: event.ctrlKey,
            alt: event.altKey
          }
        };
        
        onCanvasClick?.(graphEvent);
      }
    } catch (error) {
      onError?.(error as Error);
    }
  }, [actions, onCanvasClick, onError]);

  // Calculate node style
  const getNodeStyle = useCallback((node: GraphNode) => {
    const isSelected = selection.nodes.includes(node.id);
    const isHovered = hoveredNode === node.id;
    const baseSize = config.rendering.nodeSize.default;
    
    let color = config.rendering.colors.nodes.default;
    if (isSelected) color = config.rendering.colors.nodes.selected;
    if (isHovered) color = config.rendering.colors.nodes.highlighted;
    
    return {
      fill: node.color || color,
      stroke: isSelected ? config.rendering.colors.selection : 'none',
      strokeWidth: isSelected ? 2 : 0,
      r: node.size || baseSize,
      opacity: node.visible !== false ? 1 : 0.3
    };
  }, [config, selection, hoveredNode]);

  // Calculate edge style
  const getEdgeStyle = useCallback((edge: GraphEdge) => {
    const isSelected = selection.edges.includes(edge.id);
    const baseWidth = config.rendering.edgeWidth.default;
    
    let color = config.rendering.colors.edges.default;
    if (isSelected) color = config.rendering.colors.edges.selected;
    
    return {
      stroke: edge.color || color,
      strokeWidth: edge.weight || baseWidth,
      opacity: edge.visible !== false ? 0.6 : 0.2,
      strokeDasharray: edge.style === 'dashed' ? '5,5' : 'none'
    };
  }, [config, selection]);

  // Transform coordinates based on viewport
  const transformPoint = useCallback((x: number, y: number) => {
    const { zoom, pan } = viewport;
    return {
      x: (x + pan.x) * zoom,
      y: (y + pan.y) * zoom
    };
  }, [viewport]);

  // Render nodes
  const renderNodes = useMemo(() => {
    return graphData.nodes.map(node => {
      if (!node.position) return null;
      
      const { x, y } = transformPoint(node.position.x, node.position.y);
      const style = getNodeStyle(node);
      
      return (
        <g key={node.id} className="graph-node">
          <circle
            cx={x}
            cy={y}
            {...style}
            onClick={(e) => handleNodeClick(node, e)}
            onMouseDown={(e) => handleEnhancedNodeMouseDown(node, e)}
            onMouseUp={(e) => handleEnhancedNodeMouseUp(node, e)}
            onMouseEnter={(e) => handleNodeHover(node, e)}
            onMouseLeave={(e) => handleNodeHover(null, e)}
            style={{
              cursor: interactionState.isDragging && interactionState.draggedNode === node.id ? 'grabbing' :
                      config.interaction.dragging ? 'grab' : 'pointer'
            }}
          />
          {config.rendering.labels.nodes && (
            <text
              x={x}
              y={y + style.r + 15}
              textAnchor="middle"
              fontSize={config.rendering.labels.fontSize}
              fontFamily={config.rendering.labels.fontFamily}
              fill={config.rendering.labels.color}
              pointerEvents="none"
            >
              {node.label}
            </text>
          )}
        </g>
      );
    }).filter(Boolean);
  }, [graphData.nodes, transformPoint, getNodeStyle, handleNodeClick, handleNodeHover, config]);

  // Render edges
  const renderEdges = useMemo(() => {
    return graphData.links.map(edge => {
      const sourceNode = graphData.nodes.find(n => n.id === edge.source);
      const targetNode = graphData.nodes.find(n => n.id === edge.target);
      
      if (!sourceNode?.position || !targetNode?.position) return null;
      
      const source = transformPoint(sourceNode.position.x, sourceNode.position.y);
      const target = transformPoint(targetNode.position.x, targetNode.position.y);
      const style = getEdgeStyle(edge);
      
      return (
        <g key={edge.id} className="graph-edge">
          <line
            x1={source.x}
            y1={source.y}
            x2={target.x}
            y2={target.y}
            {...style}
            onClick={(e) => onEdgeClick?.(edge, {
              type: 'edge:click',
              target: 'edge',
              data: edge,
              position: { x: e.clientX, y: e.clientY }
            })}
            style={{ cursor: 'pointer' }}
          />
          {config.rendering.labels.edges && edge.label && (
            <text
              x={(source.x + target.x) / 2}
              y={(source.y + target.y) / 2}
              textAnchor="middle"
              fontSize={config.rendering.labels.fontSize}
              fontFamily={config.rendering.labels.fontFamily}
              fill={config.rendering.labels.color}
              pointerEvents="none"
            >
              {edge.label}
            </text>
          )}
        </g>
      );
    }).filter(Boolean);
  }, [graphData.links, graphData.nodes, transformPoint, getEdgeStyle, onEdgeClick, config]);

  // Enhanced effects for data management and layout
  useEffect(() => {
    if (enableDataManagement && dataState.data.nodes.length > 0) {
      // Sync data state with graph visualization state
      actions.setGraphData(dataState.data);

      // Notify parent of data loading
      onDataLoaded?.(dataState.data.nodes.length, dataState.data.links.length);
    }
  }, [enableDataManagement, dataState.data, actions, onDataLoaded]);

  useEffect(() => {
    if (dataState.error) {
      onError?.(dataState.error);
    }
  }, [dataState.error, onError]);

  useEffect(() => {
    if (layoutAlgorithm && graphData.nodes.length > 0 && !layoutState.isRunning) {
      layoutActions.applyLayout(layoutAlgorithm);
    }
  }, [layoutAlgorithm, graphData.nodes.length, layoutState.isRunning, layoutActions]);

  useEffect(() => {
    if (layoutState.stabilized && layoutState.algorithm) {
      onLayoutComplete?.(layoutState.algorithm);
    }
  }, [layoutState.stabilized, layoutState.algorithm, onLayoutComplete]);

  // Update viewport bounds when size changes
  useEffect(() => {
    actions.updateConfig({
      ...config,
      rendering: {
        ...config.rendering,
        // Update any size-dependent settings
      }
    });
  }, [width, height]);

  return (
    <div 
      className={`graph-canvas-container ${className}`}
      style={{ width, height, ...style }}
      data-testid={testId}
    >
      <svg
        ref={svgRef}
        width={width}
        height={height}
        viewBox={`0 0 ${width} ${height}`}
        style={{
          background: config.rendering.colors.background,
          cursor: interactionState.isDragging ? 'grabbing' :
                  interactionState.isPanning ? 'grab' :
                  config.interaction.panning ? 'grab' : 'default'
        }}
        onClick={handleCanvasClick}
        onMouseDown={handleEnhancedCanvasMouseDown}
        onMouseMove={handleEnhancedCanvasMouseMove}
        onMouseUp={handleEnhancedCanvasMouseUp}
        onWheel={handleWheel}
      >
        {/* Background grid (optional) */}
        <defs>
          <pattern
            id="grid"
            width="20"
            height="20"
            patternUnits="userSpaceOnUse"
          >
            <path
              d="M 20 0 L 0 0 0 20"
              fill="none"
              stroke="#f0f0f0"
              strokeWidth="1"
            />
          </pattern>
        </defs>
        
        {/* Render edges first (behind nodes) */}
        <g className="edges-layer">
          {renderEdges}
        </g>
        
        {/* Render nodes */}
        <g className="nodes-layer">
          {renderNodes}
        </g>

        {/* Selection box for area selection */}
        {interactionState.selectionBox.active &&
         interactionState.selectionBox.start &&
         interactionState.selectionBox.end && (
          <rect
            x={Math.min(interactionState.selectionBox.start.x, interactionState.selectionBox.end.x)}
            y={Math.min(interactionState.selectionBox.start.y, interactionState.selectionBox.end.y)}
            width={Math.abs(interactionState.selectionBox.end.x - interactionState.selectionBox.start.x)}
            height={Math.abs(interactionState.selectionBox.end.y - interactionState.selectionBox.start.y)}
            fill="rgba(74, 144, 226, 0.1)"
            stroke="rgba(74, 144, 226, 0.5)"
            strokeWidth="1"
            strokeDasharray="5,5"
          />
        )}
      </svg>

      {/* Enhanced performance and status overlay */}
      {(config.performance.levelOfDetail || process.env.NODE_ENV === 'development') && (
        <div className="performance-overlay" style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          background: 'rgba(0, 0, 0, 0.8)',
          color: 'white',
          padding: '8px',
          borderRadius: '4px',
          fontSize: '12px',
          fontFamily: 'monospace',
          pointerEvents: 'none',
          minWidth: '120px'
        }}>
          <div>Nodes: {graphData.nodes.length}</div>
          <div>Edges: {graphData.links.length}</div>
          <div>Zoom: {(viewport.zoom * 100).toFixed(0)}%</div>
          <div>Mode: {interactionState.mode}</div>
          {dataState.isLoading && <div style={{ color: '#FFD93D' }}>Loading...</div>}
          {layoutState.isRunning && (
            <div style={{ color: '#50E3C2' }}>
              Layout: {layoutState.algorithm} ({(layoutState.progress * 100).toFixed(0)}%)
            </div>
          )}
          {dataState.performance.fetchTime > 0 && (
            <div>Fetch: {dataState.performance.fetchTime.toFixed(0)}ms</div>
          )}
          {selection.nodes.length > 0 && (
            <div>Selected: {selection.nodes.length} nodes</div>
          )}
        </div>
      )}

      {/* Context menu */}
      {interactionState.contextMenu.visible && (
        <div
          style={{
            position: 'absolute',
            left: interactionState.contextMenu.position.x,
            top: interactionState.contextMenu.position.y,
            background: 'white',
            border: '1px solid #ccc',
            borderRadius: '4px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
            zIndex: 1000,
            minWidth: '120px'
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <div
            style={{ padding: '8px 12px', borderBottom: '1px solid #eee', cursor: 'pointer' }}
            onClick={() => {
              if (interactionState.contextMenu.target?.type === 'node' && interactionState.contextMenu.target.id) {
                dataActions.expandNode(interactionState.contextMenu.target.id);
              }
              interactionActions.closeContextMenu();
            }}
          >
            {interactionState.contextMenu.target?.type === 'node' ? 'Expand Node' : 'Add Node'}
          </div>
          <div
            style={{ padding: '8px 12px', borderBottom: '1px solid #eee', cursor: 'pointer' }}
            onClick={() => {
              if (interactionState.contextMenu.target?.type === 'canvas') {
                layoutActions.fitToView();
              }
              interactionActions.closeContextMenu();
            }}
          >
            {interactionState.contextMenu.target?.type === 'node' ? 'Hide Node' : 'Fit to View'}
          </div>
          <div
            style={{ padding: '8px 12px', cursor: 'pointer' }}
            onClick={() => {
              console.log('Properties clicked for:', interactionState.contextMenu.target);
              interactionActions.closeContextMenu();
            }}
          >
            Properties
          </div>
        </div>
      )}
    </div>
  );
};
