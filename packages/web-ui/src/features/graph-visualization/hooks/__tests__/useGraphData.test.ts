/**
 * Graph Data Hook Tests
 * 
 * Tests for the useGraphData hook functionality.
 */

import { renderHook, act } from '@testing-library/react';
import { useGraphData } from '../useGraphData';

// Mock the graph visualization provider
jest.mock('../../providers/GraphVisualizationProvider', () => ({
  useGraphVisualization: () => ({
    actions: {
      setGraphData: jest.fn(),
    },
    selectors: {
      getGraphData: () => ({ nodes: [], links: [] }),
    }
  })
}));

// Mock the feature bus
jest.mock('../../../../shared/infrastructure/FeatureBus', () => ({
  featureBus: {
    communicate: {
      publish: jest.fn()
    }
  }
}));

describe('useGraphData', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with empty data', () => {
    const { result } = renderHook(() => useGraphData({ autoLoad: false }));
    const [state] = result.current;

    expect(state.data.nodes).toEqual([]);
    expect(state.data.links).toEqual([]);
    expect(state.isLoading).toBe(false);
    expect(state.isError).toBe(false);
    expect(state.error).toBeNull();
  });

  it('should provide data management actions', () => {
    const { result } = renderHook(() => useGraphData({ autoLoad: false }));
    const [, actions] = result.current;

    expect(actions.loadInitialData).toBeDefined();
    expect(actions.searchNodes).toBeDefined();
    expect(actions.expandNode).toBeDefined();
    expect(actions.addNodes).toBeDefined();
    expect(actions.addEdges).toBeDefined();
    expect(actions.removeNodes).toBeDefined();
    expect(actions.removeEdges).toBeDefined();
    expect(actions.clearData).toBeDefined();
    expect(actions.exportData).toBeDefined();
    expect(actions.importData).toBeDefined();
  });

  it('should load initial data when autoLoad is true', async () => {
    const { result } = renderHook(() => useGraphData({ autoLoad: true }));
    
    // Wait for the async operation to complete
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 600)); // Wait for mock API delay
    });

    const [state] = result.current;
    expect(state.data.nodes.length).toBeGreaterThan(0);
    expect(state.isLoading).toBe(false);
  });

  it('should handle search functionality', async () => {
    const { result } = renderHook(() => useGraphData({ autoLoad: false }));
    const [, actions] = result.current;

    await act(async () => {
      const results = await actions.searchNodes('test');
      expect(results).toBeDefined();
      expect(Array.isArray(results)).toBe(true);
    });
  });

  it('should handle node expansion', async () => {
    const { result } = renderHook(() => useGraphData({ autoLoad: false }));
    const [, actions] = result.current;

    await act(async () => {
      await actions.expandNode('test-node');
    });

    // Should not throw an error
    expect(true).toBe(true);
  });

  it('should add and remove nodes', () => {
    const { result } = renderHook(() => useGraphData({ autoLoad: false }));
    const [initialState, actions] = result.current;

    const testNode = {
      id: 'test-node',
      label: 'Test Node',
      type: 'Test',
      properties: {},
      position: { x: 0, y: 0 },
      style: { size: 10, color: '#000', shape: 'circle' }
    };

    act(() => {
      actions.addNodes([testNode]);
    });

    const [stateAfterAdd] = result.current;
    expect(stateAfterAdd.data.nodes).toContain(testNode);

    act(() => {
      actions.removeNodes(['test-node']);
    });

    const [stateAfterRemove] = result.current;
    expect(stateAfterRemove.data.nodes).not.toContain(testNode);
  });

  it('should export data as JSON', () => {
    const { result } = renderHook(() => useGraphData({ autoLoad: false }));
    const [, actions] = result.current;

    const exported = actions.exportData('json');
    expect(typeof exported).toBe('string');
    
    const parsed = JSON.parse(exported);
    expect(parsed).toHaveProperty('nodes');
    expect(parsed).toHaveProperty('links');
  });

  it('should import JSON data', async () => {
    const { result } = renderHook(() => useGraphData({ autoLoad: false }));
    const [, actions] = result.current;

    const testData = {
      nodes: [{ id: 'imported', label: 'Imported Node', type: 'Test', properties: {}, position: { x: 0, y: 0 }, style: { size: 10, color: '#000', shape: 'circle' } }],
      links: []
    };

    await act(async () => {
      await actions.importData(JSON.stringify(testData), 'json');
    });

    const [state] = result.current;
    expect(state.data.nodes).toEqual(testData.nodes);
  });

  it('should clear data', () => {
    const { result } = renderHook(() => useGraphData({ autoLoad: false }));
    const [, actions] = result.current;

    // Add some data first
    act(() => {
      actions.addNodes([{
        id: 'test',
        label: 'Test',
        type: 'Test',
        properties: {},
        position: { x: 0, y: 0 },
        style: { size: 10, color: '#000', shape: 'circle' }
      }]);
    });

    act(() => {
      actions.clearData();
    });

    const [state] = result.current;
    expect(state.data.nodes).toEqual([]);
    expect(state.data.links).toEqual([]);
  });

  it('should handle performance tracking', async () => {
    const { result } = renderHook(() => useGraphData({ autoLoad: true }));
    
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 600));
    });

    const [state] = result.current;
    expect(state.performance.fetchTime).toBeGreaterThan(0);
  });

  it('should handle caching', async () => {
    const { result: result1 } = renderHook(() => useGraphData({ autoLoad: false }));
    const [, actions1] = result1.current;

    // Load data first time
    await act(async () => {
      await actions1.loadInitialData(10);
    });

    const [state1] = result1.current;
    expect(state1.cacheHit).toBe(false);

    // Load same data again - should hit cache
    const { result: result2 } = renderHook(() => useGraphData({ autoLoad: false }));
    const [, actions2] = result2.current;

    await act(async () => {
      await actions2.loadInitialData(10);
    });

    const [state2] = result2.current;
    expect(state2.cacheHit).toBe(true);
  });
});
