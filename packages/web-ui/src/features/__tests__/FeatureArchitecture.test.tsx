/**
 * Feature Architecture Tests
 * 
 * Tests for the feature-slice architecture foundation.
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { GraphVisualizationProvider, useGraphVisualization } from '../graph-visualization/providers/GraphVisualizationProvider';
import { GraphCanvas } from '../graph-visualization/components/GraphCanvas';
import { featureBus } from '../shared/infrastructure/FeatureBus';
import { FEATURE_REGISTRY, getFeatureMetadata, checkFeatureHealth } from '../index';

// Mock React for testing
jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useRef: () => ({ current: null }),
  useEffect: jest.fn(),
  useCallback: (fn: any) => fn,
  useMemo: (fn: any) => fn()
}));

// Test component that uses graph visualization
const TestGraphComponent: React.FC = () => {
  const { state, actions, selectors } = useGraphVisualization();
  
  return (
    <div data-testid="test-graph-component">
      <div data-testid="node-count">{selectors.getGraphData().nodes.length}</div>
      <div data-testid="edge-count">{selectors.getGraphData().links.length}</div>
      <button 
        data-testid="add-node-button"
        onClick={() => actions.setGraphData({
          nodes: [{ id: '1', label: 'Test Node', type: 'test', properties: {} }],
          links: []
        })}
      >
        Add Node
      </button>
      <button 
        data-testid="clear-selection-button"
        onClick={() => actions.clearSelection()}
      >
        Clear Selection
      </button>
    </div>
  );
};

describe('Feature Architecture', () => {
  beforeEach(() => {
    // Clean up feature bus before each test
    featureBus.cleanup();
  });

  afterEach(() => {
    // Clean up after each test
    featureBus.cleanup();
  });

  describe('Feature Registry', () => {
    it('should have all expected features registered', () => {
      expect(FEATURE_REGISTRY).toHaveProperty('graph-visualization');
      expect(FEATURE_REGISTRY).toHaveProperty('chat-interface');
      expect(FEATURE_REGISTRY).toHaveProperty('analysis-tools');
      expect(FEATURE_REGISTRY).toHaveProperty('search-filter');
    });

    it('should provide feature metadata', () => {
      const graphMetadata = getFeatureMetadata('graph-visualization');
      
      expect(graphMetadata).toBeDefined();
      expect(graphMetadata?.name).toBe('graph-visualization');
      expect(graphMetadata?.version).toBe('1.0.0');
      expect(graphMetadata?.permissions).toContain('graph:read');
      expect(graphMetadata?.permissions).toContain('graph:interact');
    });

    it('should check feature health', () => {
      const health = checkFeatureHealth();
      
      expect(health).toHaveProperty('graph-visualization');
      expect(health).toHaveProperty('chat-interface');
      expect(health).toHaveProperty('analysis-tools');
      expect(health).toHaveProperty('search-filter');
      
      expect(health['graph-visualization'].available).toBe(true);
      expect(health['analysis-tools'].dependencies).toHaveLength(1);
    });
  });

  describe('Graph Visualization Feature', () => {
    it('should render provider without errors', () => {
      render(
        <GraphVisualizationProvider>
          <div data-testid="child">Test Child</div>
        </GraphVisualizationProvider>
      );
      
      expect(screen.getByTestId('child')).toBeInTheDocument();
    });

    it('should provide graph visualization context', () => {
      render(
        <GraphVisualizationProvider>
          <TestGraphComponent />
        </GraphVisualizationProvider>
      );
      
      expect(screen.getByTestId('test-graph-component')).toBeInTheDocument();
      expect(screen.getByTestId('node-count')).toHaveTextContent('0');
      expect(screen.getByTestId('edge-count')).toHaveTextContent('0');
    });

    it('should handle graph data updates', () => {
      render(
        <GraphVisualizationProvider>
          <TestGraphComponent />
        </GraphVisualizationProvider>
      );
      
      const addButton = screen.getByTestId('add-node-button');
      fireEvent.click(addButton);
      
      expect(screen.getByTestId('node-count')).toHaveTextContent('1');
    });

    it('should handle selection actions', () => {
      render(
        <GraphVisualizationProvider>
          <TestGraphComponent />
        </GraphVisualizationProvider>
      );
      
      const clearButton = screen.getByTestId('clear-selection-button');
      
      // Should not throw error when clearing empty selection
      expect(() => fireEvent.click(clearButton)).not.toThrow();
    });
  });

  describe('Graph Canvas Component', () => {
    it('should render canvas with default props', () => {
      render(
        <GraphVisualizationProvider>
          <GraphCanvas />
        </GraphVisualizationProvider>
      );
      
      expect(screen.getByTestId('graph-canvas')).toBeInTheDocument();
    });

    it('should render canvas with custom dimensions', () => {
      render(
        <GraphVisualizationProvider>
          <GraphCanvas width={1000} height={800} testId="custom-canvas" />
        </GraphVisualizationProvider>
      );
      
      const canvas = screen.getByTestId('custom-canvas');
      expect(canvas).toBeInTheDocument();
      
      const svg = canvas.querySelector('svg');
      expect(svg).toHaveAttribute('width', '1000');
      expect(svg).toHaveAttribute('height', '800');
    });

    it('should handle canvas click events', () => {
      const onCanvasClick = jest.fn();
      
      render(
        <GraphVisualizationProvider>
          <GraphCanvas onCanvasClick={onCanvasClick} />
        </GraphVisualizationProvider>
      );
      
      const svg = screen.getByTestId('graph-canvas').querySelector('svg');
      if (svg) {
        fireEvent.click(svg);
        expect(onCanvasClick).toHaveBeenCalled();
      }
    });

    it('should display performance overlay in development', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';
      
      render(
        <GraphVisualizationProvider>
          <GraphCanvas />
        </GraphVisualizationProvider>
      );
      
      const overlay = screen.getByText(/Nodes: 0 \| Edges: 0/);
      expect(overlay).toBeInTheDocument();
      
      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('Feature Bus Communication', () => {
    it('should initialize feature bus', () => {
      expect(featureBus).toBeDefined();
      expect(featureBus.communicate).toBeDefined();
    });

    it('should handle feature registration', () => {
      const mockFeature = {
        metadata: {
          name: 'test-feature',
          version: '1.0.0',
          description: 'Test feature',
          dependencies: [],
          isolationLevel: 'partial' as any,
          permissions: [],
          tags: []
        },
        provider: {} as any,
        hooks: {},
        services: {},
        components: {}
      };
      
      expect(() => featureBus.register(mockFeature)).not.toThrow();
      expect(featureBus.getFeature('test-feature')).toBeDefined();
    });

    it('should handle feature communication', () => {
      const eventHandler = jest.fn();
      
      const unsubscribe = featureBus.communicate.subscribe('test:event', eventHandler);
      
      featureBus.communicate.publish({
        type: 'test:event',
        payload: { message: 'test' },
        timestamp: new Date(),
        source: 'test'
      });
      
      expect(eventHandler).toHaveBeenCalled();
      
      unsubscribe();
    });

    it('should get feature health status', () => {
      const health = featureBus.getFeatureHealth();
      expect(health).toBeDefined();
      expect(typeof health).toBe('object');
    });
  });

  describe('Feature Isolation', () => {
    it('should enforce isolation policies', () => {
      const feature1 = {
        metadata: {
          name: 'isolated-feature',
          version: '1.0.0',
          description: 'Isolated test feature',
          dependencies: [],
          isolationLevel: 'full' as any,
          permissions: [],
          tags: []
        },
        provider: {} as any,
        hooks: {},
        services: {},
        components: {}
      };
      
      featureBus.register(feature1);
      
      // Full isolation should not allow access to other features
      expect(featureBus.checkIsolationPolicy('isolated-feature', 'other-feature')).toBe(false);
    });

    it('should allow partial isolation access', () => {
      const feature2 = {
        metadata: {
          name: 'partial-feature',
          version: '1.0.0',
          description: 'Partially isolated test feature',
          dependencies: [],
          isolationLevel: 'partial' as any,
          permissions: [],
          tags: []
        },
        provider: {} as any,
        hooks: {},
        services: {},
        components: {}
      };
      
      featureBus.register(feature2);
      
      // Partial isolation should allow self-access
      expect(featureBus.checkIsolationPolicy('partial-feature', 'partial-feature')).toBe(true);
    });
  });
});
