# Knowledge Graph Visualizer - Monorepo

This is the new monorepo structure for the Knowledge Graph Visualizer project,
implementing a modern microservices architecture with proper separation of
concerns.

## 📁 Package Structure

```
packages/
├── shared/          # Shared utilities, types, and constants
├── graph-api/       # Backend API service (Node.js/Express)
├── web-ui/          # Frontend application (React/Vite)
└── chat-service/    # Chat service (Node.js/Express)
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm 9+
- Neo4j Desktop (running)

### Installation

```bash
# Install all dependencies
npm run install:all

# Build shared package first
npm run build:shared

# Build all packages
npm run build
```

### Development

```bash
# Start all services in development mode
npm run dev

# Start individual services
npm run dev:api      # Backend API only
npm run dev:ui       # Frontend only
npm run dev:chat     # Chat service only
```

### Production

```bash
# Build for production
npm run build

# Start production services
npm run start:production
```

## 📦 Package Details

### @kg-visualizer/shared

Common utilities, types, and constants shared across all packages.

**Key exports:**

- TypeScript types and interfaces
- Utility functions
- Constants and configuration
- Feature flag definitions

### @kg-visualizer/graph-api

Backend API service handling graph operations, database connections, and API
endpoints.

**Features:**

- Express.js server with TypeScript
- Neo4j database integration
- Feature flag support
- Health monitoring
- API documentation with Swagger

### @kg-visualizer/web-ui

React-based frontend application with modern tooling.

**Features:**

- React 18 with TypeScript
- Vite for fast development
- Feature-slice architecture
- Component library
- End-to-end testing with Playwright

### @kg-visualizer/chat-service

Chat service for handling LLM interactions and knowledge graph queries.

**Features:**

- Express.js with TypeScript
- Multiple LLM provider support
- Circuit breaker pattern
- Knowledge graph integration

## 🔧 Development Scripts

### Root Level Commands

```bash
npm run build              # Build all packages
npm run test               # Run all tests
npm run lint               # Lint all packages
npm run type-check         # TypeScript type checking
npm run clean              # Clean all build artifacts
```

### Migration Commands

```bash
npm run migration:baseline    # Collect performance baseline
npm run migration:monitor     # Start migration monitoring
npm run migration:rollback    # Emergency rollback
npm run migration:health      # Health check
```

### Docker Commands

```bash
npm run docker:build      # Build Docker images
npm run docker:up          # Start containers
npm run docker:down        # Stop containers
npm run docker:logs        # View logs
```

## 🏗️ Architecture

### Migration Strategy

This monorepo implements a gradual migration strategy using:

- **Feature Flags**: Control rollout of new features
- **Strangler Fig Pattern**: Gradually replace legacy code
- **Dual Execution**: Validate new implementations
- **Circuit Breakers**: Fallback to legacy systems
- **Monitoring**: Real-time migration tracking

### Package Dependencies

```
graph-api  ──┐
web-ui     ──┼──► shared
chat-service ─┘
```

All packages depend on the shared package for common utilities and types.

## 🧪 Testing

### Unit Tests

```bash
npm run test               # All packages
npm run test --workspace=shared
npm run test --workspace=graph-api
```

### Integration Tests

```bash
npm run test:integration
```

### End-to-End Tests

```bash
npm run test:e2e
```

## 📊 Monitoring

### Migration Dashboard

Access the real-time migration dashboard at:

```
http://localhost:3002/monitoring/migration-dashboard.html
```

### Health Checks

```bash
# Check system health
npm run migration:health

# Monitor migration progress
npm run migration:monitor
```

## 🚨 Emergency Procedures

### Rollback

```bash
# Emergency rollback
npm run migration:rollback "Reason for rollback"

# Test rollback procedures
npm run migration:rollback test
```

### Monitoring

```bash
# View current status
node ../tools/monitoring/monitor.cjs status

# View recent alerts
node ../tools/monitoring/monitor.cjs alerts
```

## 🔄 Migration Progress

Current migration status can be viewed at:

- Dashboard: http://localhost:3002/monitoring/migration-dashboard.html
- API: http://localhost:3002/api/feature-flags

## 📚 Documentation

- [Architecture Documentation](../docs/architecture/)
- [API Documentation](../docs/api/)
- [Emergency Procedures](../docs/emergency-procedures.md)
- [Migration Plan](../README.md)

## 🤝 Contributing

1. Make changes in the appropriate package
2. Run tests: `npm run test`
3. Build: `npm run build`
4. Test migration safety: `npm run migration:health`
5. Submit pull request

## 📄 License

MIT License - see [LICENSE](../LICENSE) file for details.
