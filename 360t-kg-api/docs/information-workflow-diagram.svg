<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2745.798095703125 764.02734375" style="max-width: 2745.798095703125px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440"><style>#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .error-icon{fill:#552222;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .error-text{fill:#552222;stroke:#552222;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .edge-thickness-normal{stroke-width:1px;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .marker{fill:#333333;stroke:#333333;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .marker.cross{stroke:#333333;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 p{margin:0;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .cluster-label text{fill:#333;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .cluster-label span{color:#333;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .cluster-label span p{background-color:transparent;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .label text,#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 span{fill:#333;color:#333;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .node rect,#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .node circle,#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .node ellipse,#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .node polygon,#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .rough-node .label text,#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .node .label text,#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .image-shape .label,#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .icon-shape .label{text-anchor:middle;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .rough-node .label,#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .node .label,#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .image-shape .label,#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .icon-shape .label{text-align:center;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .node.clickable{cursor:pointer;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .arrowheadPath{fill:#333333;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .edgePath .path{stroke:#333333;stroke-width:2.0px;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .flowchart-link{stroke:#333333;fill:none;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .cluster text{fill:#333;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .cluster span{color:#333;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 rect.text{fill:none;stroke-width:0;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .icon-shape,#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .icon-shape p,#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .icon-shape rect,#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .primary&gt;*{fill:#00973A!important;color:white!important;stroke:#005722!important;stroke-width:2px!important;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .primary span{fill:#00973A!important;color:white!important;stroke:#005722!important;stroke-width:2px!important;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .primary tspan{fill:white!important;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .secondary&gt;*{fill:#f5f5f5!important;color:#999!important;stroke:#ddd!important;stroke-width:1px!important;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .secondary span{fill:#f5f5f5!important;color:#999!important;stroke:#ddd!important;stroke-width:1px!important;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .secondary tspan{fill:#999!important;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .processing&gt;*{fill:#e1f5fe!important;color:#0277bd!important;stroke:#0277bd!important;stroke-width:1px!important;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .processing span{fill:#e1f5fe!important;color:#0277bd!important;stroke:#0277bd!important;stroke-width:1px!important;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .processing tspan{fill:#0277bd!important;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .storage&gt;*{fill:#fff8e1!important;color:#ff8f00!important;stroke:#ff8f00!important;stroke-width:1px!important;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .storage span{fill:#fff8e1!important;color:#ff8f00!important;stroke:#ff8f00!important;stroke-width:1px!important;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .storage tspan{fill:#ff8f00!important;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .frontend&gt;*{fill:#f3e5f5!important;color:#7b1fa2!important;stroke:#7b1fa2!important;stroke-width:1px!important;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .frontend span{fill:#f3e5f5!important;color:#7b1fa2!important;stroke:#7b1fa2!important;stroke-width:1px!important;}#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440 .frontend tspan{fill:#7b1fa2!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph4" class="cluster"><rect height="436.01560974121094" width="292.0442657470703" y="154.0058536529541" x="2445.7538833618164" style=""></rect><g transform="translate(2511.6230239868164, 154.0058536529541)" class="cluster-label"><foreignObject height="24.00390625" width="160.3059844970703"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Frontend Visualization</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="406.0156135559082" width="811.1197814941406" y="169.00585174560547" x="1584.6341018676758" style=""></rect><g transform="translate(1942.899070739746, 169.00585174560547)" class="cluster-label"><foreignObject height="24.00390625" width="94.58984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Query Engine</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="269.44834899902344" width="176.19791412353516" y="246.31020545959473" x="1358.4361877441406" style=""></rect><g transform="translate(1397.446605682373, 246.31020545959473)" class="cluster-label"><foreignObject height="24.00390625" width="98.17707824707031"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Storage Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="684.0234146118164" width="958.6262969970703" y="30.001951217651367" x="349.8098907470703" style=""></rect><g transform="translate(773.2701759338379, 30.001951217651367)" class="cluster-label"><foreignObject height="24.00390625" width="111.70572662353516"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data Processing</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="748.0273170471191" width="291.8098907470703" y="8" x="8" style=""></rect><g transform="translate(107.82421875, 8)" class="cluster-label"><foreignObject height="24.00390625" width="92.16145324707031"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data Sources</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DS1_P1_0" d="M224.94,70.002L237.418,70.002C249.897,70.002,274.853,70.002,291.498,70.002C308.143,70.002,316.477,70.002,324.81,70.002C333.143,70.002,341.477,70.002,367.69,116.9C393.904,163.799,437.997,257.595,460.044,304.493L482.091,351.392"></path><path marker-end="url(#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DS2_P1_1" d="M272.206,174.006L276.806,174.006C281.407,174.006,290.609,174.006,299.376,174.006C308.143,174.006,316.477,174.006,324.81,174.006C333.143,174.006,341.477,174.006,366.532,203.629C391.587,233.251,433.364,292.497,454.252,322.12L475.141,351.743"></path><path marker-end="url(#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DS3_P1_2" d="M269.82,278.01L274.818,278.01C279.816,278.01,289.813,278.01,298.978,278.01C308.143,278.01,316.477,278.01,324.81,278.01C333.143,278.01,341.477,278.01,363.199,290.458C384.921,302.906,420.032,327.802,437.587,340.25L455.143,352.698"></path><path marker-end="url(#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DS4_P1_3" d="M274.81,382.014L278.977,382.014C283.143,382.014,291.477,382.014,299.81,382.014C308.143,382.014,316.477,382.014,324.81,382.014C333.143,382.014,341.477,382.014,349.143,382.014C356.81,382.014,363.81,382.014,367.31,382.014L370.81,382.014"></path><path marker-end="url(#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DS5_P1_4" d="M251.672,486.018L259.695,486.018C267.718,486.018,283.764,486.018,295.954,486.018C308.143,486.018,316.477,486.018,324.81,486.018C333.143,486.018,341.477,486.018,363.199,473.57C384.921,461.121,420.032,436.225,437.587,423.777L455.143,411.329"></path><path marker-end="url(#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DS6_P1_5" d="M223.944,590.021L236.588,590.021C249.233,590.021,274.521,590.021,291.332,590.021C308.143,590.021,316.477,590.021,324.81,590.021C333.143,590.021,341.477,590.021,366.532,560.399C391.587,530.776,433.364,471.53,454.252,441.907L475.141,412.285"></path><path marker-end="url(#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DS7_P1_6" d="M262.766,694.025L268.94,694.025C275.114,694.025,287.462,694.025,297.803,694.025C308.143,694.025,316.477,694.025,324.81,694.025C333.143,694.025,341.477,694.025,367.69,647.127C393.904,600.229,437.997,506.432,460.044,459.534L482.091,412.636"></path><path marker-end="url(#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P1_P2_7" d="M618.163,382.014L622.329,382.014C626.496,382.014,634.829,382.014,642.496,382.014C650.163,382.014,657.163,382.014,660.663,382.014L664.163,382.014"></path><path marker-end="url(#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P2_P3_8" d="M895.106,343.01L904.782,339.117C914.458,335.224,933.811,327.438,946.987,323.545C960.163,319.652,967.163,319.652,970.663,319.652L974.163,319.652"></path><path marker-end="url(#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P2_P4_9" d="M895.106,421.018L904.782,424.911C914.458,428.804,933.811,436.59,951.249,440.483C968.687,444.376,984.211,444.376,991.974,444.376L999.736,444.376"></path><path marker-end="url(#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P3_S1_10" d="M1283.436,319.652L1287.603,319.652C1291.77,319.652,1300.103,319.652,1308.436,319.652C1316.77,319.652,1325.103,319.652,1333.436,319.652C1341.77,319.652,1350.103,319.652,1357.77,319.652C1365.436,319.652,1372.436,319.652,1375.936,319.652L1379.436,319.652"></path><path marker-end="url(#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P4_S2_11" d="M1257.863,444.376L1266.292,444.376C1274.721,444.376,1291.579,444.376,1304.174,444.376C1316.77,444.376,1325.103,444.376,1333.436,444.376C1341.77,444.376,1350.103,444.376,1359.758,444.376C1369.413,444.376,1380.39,444.376,1385.878,444.376L1391.367,444.376"></path><path marker-end="url(#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_S1_Q1_12" d="M1509.634,319.652L1513.801,319.652C1517.967,319.652,1526.301,319.652,1534.634,319.652C1542.967,319.652,1551.301,319.652,1559.634,319.652C1567.967,319.652,1576.301,319.652,1592.182,325.257C1608.062,330.863,1631.49,342.074,1643.204,347.679L1654.919,353.285"></path><path marker-end="url(#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_S2_Q1_13" d="M1497.704,444.376L1503.859,444.376C1510.014,444.376,1522.324,444.376,1532.646,444.376C1542.967,444.376,1551.301,444.376,1559.634,444.376C1567.967,444.376,1576.301,444.376,1592.182,438.77C1608.062,433.165,1631.49,421.953,1643.204,416.348L1654.919,410.742"></path><path marker-end="url(#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q1_Q2_14" d="M1820.272,382.014L1824.439,382.014C1828.605,382.014,1836.939,382.014,1844.605,382.014C1852.272,382.014,1859.272,382.014,1862.772,382.014L1866.272,382.014"></path><path marker-end="url(#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q2_Q3_15" d="M2130.272,382.014L2134.439,382.014C2138.605,382.014,2146.939,382.014,2154.605,382.014C2162.272,382.014,2169.272,382.014,2172.772,382.014L2176.272,382.014"></path><path marker-end="url(#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q3_F1_16" d="M2295.071,355.012L2311.851,331.844C2328.632,308.677,2362.193,262.342,2383.14,239.175C2404.087,216.008,2412.421,216.008,2420.754,216.008C2429.087,216.008,2437.421,216.008,2451.637,216.008C2465.853,216.008,2485.952,216.008,2496.001,216.008L2506.051,216.008"></path><path marker-end="url(#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q3_F2_17" d="M2327.878,355.012L2339.191,349.178C2350.503,343.345,2373.129,331.678,2388.608,325.845C2404.087,320.012,2412.421,320.012,2420.754,320.012C2429.087,320.012,2437.421,320.012,2445.087,320.012C2452.754,320.012,2459.754,320.012,2463.254,320.012L2466.754,320.012"></path><path marker-end="url(#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q3_F3_18" d="M2352.813,409.016L2359.97,411.516C2367.126,414.016,2381.44,419.016,2392.764,421.516C2404.087,424.016,2412.421,424.016,2420.754,424.016C2429.087,424.016,2437.421,424.016,2447.476,424.016C2457.531,424.016,2469.309,424.016,2475.198,424.016L2481.087,424.016"></path><path marker-end="url(#mermaid-ee3b1d9c-e0db-4025-a7cc-e6496149a440_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q3_F4_19" d="M2297.75,409.016L2314.084,428.85C2330.418,448.684,2363.086,488.352,2383.587,508.186C2404.087,528.02,2412.421,528.02,2420.754,528.02C2429.087,528.02,2437.421,528.02,2448.802,528.02C2460.184,528.02,2474.615,528.02,2481.83,528.02L2489.046,528.02"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(153.90494537353516, 70.00195121765137)" id="flowchart-DS1-161" class="node default primary"><rect height="54.00390625" width="142.0703125" y="-27.001953125" x="-71.03515625" style="fill:#00973A !important;stroke:#005722 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-41.03515625, -12.001953125)" style="color:white !important" class="label"><rect></rect><foreignObject height="24.00390625" width="82.0703125"><div xmlns="http://www.w3.org/1999/xhtml" style="color: white !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:white !important"><p>User guides</p></span></div></foreignObject></g></g><g transform="translate(153.90494537353516, 174.0058536529541)" id="flowchart-DS2-162" class="node default secondary"><rect height="54.00390625" width="236.6015625" y="-27.001953125" x="-118.30078125" style="fill:#f5f5f5 !important;stroke:#ddd !important;stroke-width:1px !important" class="basic label-container"></rect><g transform="translate(-88.30078125, -12.001953125)" style="color:#999 !important" class="label"><rect></rect><foreignObject height="24.00390625" width="176.6015625"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(153, 153, 153) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#999 !important"><p>Cloudera documentation</p></span></div></foreignObject></g></g><g transform="translate(153.90494537353516, 278.00975608825684)" id="flowchart-DS3-163" class="node default secondary"><rect height="54.00390625" width="231.8294219970703" y="-27.001953125" x="-115.91471099853516" style="fill:#f5f5f5 !important;stroke:#ddd !important;stroke-width:1px !important" class="basic label-container"></rect><g transform="translate(-85.91471099853516, -12.001953125)" style="color:#999 !important" class="label"><rect></rect><foreignObject height="24.00390625" width="171.8294219970703"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(153, 153, 153) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#999 !important"><p>Salesforce Einstein data</p></span></div></foreignObject></g></g><g transform="translate(153.90494537353516, 382.01365852355957)" id="flowchart-DS4-164" class="node default secondary"><rect height="54.00390625" width="241.8098907470703" y="-27.001953125" x="-120.90494537353516" style="fill:#f5f5f5 !important;stroke:#ddd !important;stroke-width:1px !important" class="basic label-container"></rect><g transform="translate(-90.90494537353516, -12.001953125)" style="color:#999 !important" class="label"><rect></rect><foreignObject height="24.00390625" width="181.8098907470703"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(153, 153, 153) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#999 !important"><p>MCP tools documentation</p></span></div></foreignObject></g></g><g transform="translate(153.90494537353516, 486.0175609588623)" id="flowchart-DS5-165" class="node default secondary"><rect height="54.00390625" width="195.53384399414062" y="-27.001953125" x="-97.76692199707031" style="fill:#f5f5f5 !important;stroke:#ddd !important;stroke-width:1px !important" class="basic label-container"></rect><g transform="translate(-67.76692199707031, -12.001953125)" style="color:#999 !important" class="label"><rect></rect><foreignObject height="24.00390625" width="135.53384399414062"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(153, 153, 153) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#999 !important"><p>API documentation</p></span></div></foreignObject></g></g><g transform="translate(153.90494537353516, 590.021463394165)" id="flowchart-DS6-166" class="node default secondary"><rect height="54.00390625" width="140.078125" y="-27.001953125" x="-70.0390625" style="fill:#f5f5f5 !important;stroke:#ddd !important;stroke-width:1px !important" class="basic label-container"></rect><g transform="translate(-40.0390625, -12.001953125)" style="color:#999 !important" class="label"><rect></rect><foreignObject height="24.00390625" width="80.078125"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(153, 153, 153) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#999 !important"><p>Jira tickets</p></span></div></foreignObject></g></g><g transform="translate(153.90494537353516, 694.0253658294678)" id="flowchart-DS7-167" class="node default secondary"><rect height="54.00390625" width="217.72134399414062" y="-27.001953125" x="-108.86067199707031" style="fill:#f5f5f5 !important;stroke:#ddd !important;stroke-width:1px !important" class="basic label-container"></rect><g transform="translate(-78.86067199707031, -12.001953125)" style="color:#999 !important" class="label"><rect></rect><foreignObject height="24.00390625" width="157.72134399414062"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(153, 153, 153) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#999 !important"><p>Atlassian wiki content</p></span></div></foreignObject></g></g><g transform="translate(496.48632049560547, 382.01365852355957)" id="flowchart-P1-168" class="node default processing"><rect height="54.00390625" width="243.3528594970703" y="-27.001953125" x="-121.67642974853516" style="fill:#e1f5fe !important;stroke:#0277bd !important;stroke-width:1px !important" class="basic label-container"></rect><g transform="translate(-91.67642974853516, -12.001953125)" style="color:#0277bd !important" class="label"><rect></rect><foreignObject height="24.00390625" width="183.3528594970703"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(2, 119, 189) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#0277bd !important"><p>Data ingestion &amp; cleaning</p></span></div></foreignObject></g></g><g transform="translate(798.1627502441406, 382.01365852355957)" id="flowchart-P2-169" class="node default processing"><rect height="78.0078125" width="260" y="-39.00390625" x="-130" style="fill:#e1f5fe !important;stroke:#0277bd !important;stroke-width:1px !important" class="basic label-container"></rect><g transform="translate(-100, -24.00390625)" style="color:#0277bd !important" class="label"><rect></rect><foreignObject height="48.0078125" width="200"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(2, 119, 189) !important; display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;"><span class="nodeLabel" style="color:#0277bd !important"><p>Text chunking with SemanticChunker</p></span></div></foreignObject></g></g><g transform="translate(1130.7994689941406, 319.6515712738037)" id="flowchart-P3-170" class="node default processing"><rect height="78.0078125" width="305.2734375" y="-39.00390625" x="-152.63671875" style="fill:#e1f5fe !important;stroke:#0277bd !important;stroke-width:1px !important" class="basic label-container"></rect><g transform="translate(-122.63671875, -24.00390625)" style="color:#0277bd !important" class="label"><rect></rect><foreignObject height="48.0078125" width="245.2734375"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(2, 119, 189) !important; display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;"><span class="nodeLabel" style="color:#0277bd !important"><p>NER &amp; relationship extraction\nLLMGraphTransformer</p></span></div></foreignObject></g></g><g transform="translate(1130.7994689941406, 444.37574577331543)" id="flowchart-P4-171" class="node default processing"><rect height="54.00390625" width="254.12759399414062" y="-27.001953125" x="-127.06379699707031" style="fill:#e1f5fe !important;stroke:#0277bd !important;stroke-width:1px !important" class="basic label-container"></rect><g transform="translate(-97.06379699707031, -12.001953125)" style="color:#0277bd !important" class="label"><rect></rect><foreignObject height="24.00390625" width="194.12759399414062"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(2, 119, 189) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#0277bd !important"><p>Vector embedding creation</p></span></div></foreignObject></g></g><g transform="translate(1446.5351448059082, 319.6515712738037)" id="flowchart-S1-172" class="node default storage"><path transform="translate(-63.09895706176758, -38.34136823775941)" style="fill:#fff8e1 !important;stroke:#ff8f00 !important;stroke-width:1px !important" class="basic label-container" d="M0,12.55961007517294 a63.09895706176758,12.55961007517294 0,0,0 126.19791412353516,0 a63.09895706176758,12.55961007517294 0,0,0 -126.19791412353516,0 l0,51.56351632517294 a63.09895706176758,12.55961007517294 0,0,0 126.19791412353516,0 l0,-51.56351632517294"></path><g transform="translate(-55.59895706176758, -2.001953125)" style="color:#ff8f00 !important" class="label"><rect></rect><foreignObject height="24.00390625" width="111.19791412353516"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(255, 143, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#ff8f00 !important"><p>Neo4j database</p></span></div></foreignObject></g></g><g transform="translate(1446.5351448059082, 444.37574577331543)" id="flowchart-S2-173" class="node default storage"><path transform="translate(-51.168617248535156, -36.38280549452063)" style="fill:#fff8e1 !important;stroke:#ff8f00 !important;stroke-width:1px !important" class="basic label-container" d="M0,11.253901579680422 a51.168617248535156,11.253901579680422 0,0,0 102.33723449707031,0 a51.168617248535156,11.253901579680422 0,0,0 -102.33723449707031,0 l0,50.257807829680424 a51.168617248535156,11.253901579680422 0,0,0 102.33723449707031,0 l0,-50.257807829680424"></path><g transform="translate(-43.668617248535156, -2.001953125)" style="color:#ff8f00 !important" class="label"><rect></rect><foreignObject height="24.00390625" width="87.33723449707031"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(255, 143, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#ff8f00 !important"><p>Vector store</p></span></div></foreignObject></g></g><g transform="translate(1714.953109741211, 382.01365852355957)" id="flowchart-Q1-174" class="node default processing"><rect height="54.00390625" width="210.6380157470703" y="-27.001953125" x="-105.31900787353516" style="fill:#e1f5fe !important;stroke:#0277bd !important;stroke-width:1px !important" class="basic label-container"></rect><g transform="translate(-75.31900787353516, -12.001953125)" style="color:#0277bd !important" class="label"><rect></rect><foreignObject height="24.00390625" width="150.6380157470703"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(2, 119, 189) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#0277bd !important"><p>Hybrid search engine</p></span></div></foreignObject></g></g><g transform="translate(2000.272117614746, 382.01365852355957)" id="flowchart-Q2-175" class="node default processing"><rect height="78.0078125" width="260" y="-39.00390625" x="-130" style="fill:#e1f5fe !important;stroke:#0277bd !important;stroke-width:1px !important" class="basic label-container"></rect><g transform="translate(-100, -24.00390625)" style="color:#0277bd !important" class="label"><rect></rect><foreignObject height="48.0078125" width="200"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(2, 119, 189) !important; display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;"><span class="nodeLabel" style="color:#0277bd !important"><p>LLM-enhanced query processing</p></span></div></foreignObject></g></g><g transform="translate(2275.5130004882812, 382.01365852355957)" id="flowchart-Q3-176" class="node default processing"><rect height="54.00390625" width="190.4817657470703" y="-27.001953125" x="-95.24088287353516" style="fill:#e1f5fe !important;stroke:#0277bd !important;stroke-width:1px !important" class="basic label-container"></rect><g transform="translate(-65.24088287353516, -12.001953125)" style="color:#0277bd !important" class="label"><rect></rect><foreignObject height="24.00390625" width="130.4817657470703"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(2, 119, 189) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#0277bd !important"><p>Source attribution</p></span></div></foreignObject></g></g><g transform="translate(2591.7760162353516, 216.00780487060547)" id="flowchart-F1-177" class="node default frontend"><rect height="54.00390625" width="163.4505157470703" y="-27.001953125" x="-81.72525787353516" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:1px !important" class="basic label-container"></rect><g transform="translate(-51.725257873535156, -12.001953125)" style="color:#7b1fa2 !important" class="label"><rect></rect><foreignObject height="24.00390625" width="103.45051574707031"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(123, 31, 162) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#7b1fa2 !important"><p>Chat interface</p></span></div></foreignObject></g></g><g transform="translate(2591.7760162353516, 320.0117073059082)" id="flowchart-F2-178" class="node default frontend"><rect height="54.00390625" width="242.0442657470703" y="-27.001953125" x="-121.02213287353516" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:1px !important" class="basic label-container"></rect><g transform="translate(-91.02213287353516, -12.001953125)" style="color:#7b1fa2 !important" class="label"><rect></rect><foreignObject height="24.00390625" width="182.0442657470703"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(123, 31, 162) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#7b1fa2 !important"><p>Source node visualization</p></span></div></foreignObject></g></g><g transform="translate(2591.7760162353516, 424.01560974121094)" id="flowchart-F3-179" class="node default frontend"><rect height="54.00390625" width="213.37890625" y="-27.001953125" x="-106.689453125" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:1px !important" class="basic label-container"></rect><g transform="translate(-76.689453125, -12.001953125)" style="color:#7b1fa2 !important" class="label"><rect></rect><foreignObject height="24.00390625" width="153.37890625"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(123, 31, 162) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#7b1fa2 !important"><p>Document references</p></span></div></foreignObject></g></g><g transform="translate(2591.7760162353516, 528.0195121765137)" id="flowchart-F4-180" class="node default frontend"><rect height="54.00390625" width="197.4609375" y="-27.001953125" x="-98.73046875" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:1px !important" class="basic label-container"></rect><g transform="translate(-68.73046875, -12.001953125)" style="color:#7b1fa2 !important" class="label"><rect></rect><foreignObject height="24.00390625" width="137.4609375"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(123, 31, 162) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#7b1fa2 !important"><p>Graph visualization</p></span></div></foreignObject></g></g></g></g></g></svg>