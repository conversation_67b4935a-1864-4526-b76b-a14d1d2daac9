{"name": "360t-kg-api", "version": "1.0.0", "description": "Backend API for 360T Knowledge Graph", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "apply-schema": "node scripts/applySchema.js", "load-data": "node scripts/loadData.js", "load-additional-data": "node scripts/loadAdditionalData.js", "verify-data": "node scripts/verifyData.js", "analyze-graph": "node scripts/analyzeGraph.js", "generate-dashboard": "node scripts/generateDashboard.js", "validate-data": "node scripts/validateData.js", "monitor-system": "node scripts/monitorSystem.js", "test": "jest --coverage", "test:watch": "jest --watch", "smoke-test": "node scripts/smoke-test-gds.js", "neo4j": "node scripts/neo4j-manager.js", "neo4j:restart": "node scripts/neo4j-manager.js restart", "neo4j:status": "node scripts/neo4j-manager.js status", "lint": "eslint .", "init-db": "node scripts/init-db.js", "migrate": "node scripts/migrate.js", "migrate:down": "node scripts/migrate.js down", "docs": "swagger-jsdoc -d swagger.yaml -o public/swagger.json && swagger-ui-express serve public/swagger.json"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "express-validator": "^7.0.1", "helmet": "^7.0.0", "morgan": "^1.10.0", "neo4j-driver": "^5.15.0", "prom-client": "^15.0.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^4.6.3", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^8.57.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "jest": {"testEnvironment": "node", "coverageDirectory": "./coverage", "collectCoverageFrom": ["**/*.js", "!**/node_modules/**", "!**/coverage/**", "!**/public/**"]}}