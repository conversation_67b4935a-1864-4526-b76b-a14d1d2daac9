const logger = require('./logger');

/**
 * Comprehensive timing utility for measuring performance across the chat pipeline
 * Provides detailed timing measurements and centralized logging
 */
class TimingTracker {
    constructor(operationId = null) {
        this.operationId = operationId || `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        this.startTime = Date.now();
        this.stages = new Map();
        this.metadata = {};
    }

    /**
     * Start timing a specific stage
     * @param {string} stageName - Name of the stage
     * @param {object} metadata - Additional metadata for this stage
     */
    startStage(stageName, metadata = {}) {
        const timestamp = Date.now();
        this.stages.set(stageName, {
            startTime: timestamp,
            metadata,
            status: 'running'
        });
        
        logger.info(`Stage started: ${stageName}`, {
            operationId: this.operationId,
            stageName,
            timestamp,
            metadata
        });
        
        return timestamp;
    }

    /**
     * End timing a specific stage
     * @param {string} stageName - Name of the stage
     * @param {object} result - Result data from the stage
     */
    endStage(stageName, result = {}) {
        const timestamp = Date.now();
        const stage = this.stages.get(stageName);
        
        if (!stage) {
            logger.warn(`Attempted to end non-existent stage: ${stageName}`, {
                operationId: this.operationId
            });
            return null;
        }

        const duration = timestamp - stage.startTime;
        stage.endTime = timestamp;
        stage.duration = duration;
        stage.result = result;
        stage.status = 'completed';

        logger.info(`Stage completed: ${stageName}`, {
            operationId: this.operationId,
            stageName,
            duration,
            durationMs: duration,
            durationSeconds: (duration / 1000).toFixed(3),
            result,
            slow: duration > 1000 // Flag slow operations
        });

        return duration;
    }

    /**
     * Mark a stage as failed
     * @param {string} stageName - Name of the stage
     * @param {Error|string} error - Error that occurred
     */
    failStage(stageName, error) {
        const timestamp = Date.now();
        const stage = this.stages.get(stageName);
        
        if (stage) {
            const duration = timestamp - stage.startTime;
            stage.endTime = timestamp;
            stage.duration = duration;
            stage.error = error;
            stage.status = 'failed';
        }

        logger.error(`Stage failed: ${stageName}`, {
            operationId: this.operationId,
            stageName,
            error: error.toString(),
            duration: stage ? stage.duration : null
        });
    }

    /**
     * Add metadata to the operation
     * @param {object} metadata - Metadata to add
     */
    addMetadata(metadata) {
        Object.assign(this.metadata, metadata);
    }

    /**
     * Get timing summary for the entire operation
     */
    getSummary() {
        const totalDuration = Date.now() - this.startTime;
        const stagesSummary = Array.from(this.stages.entries()).map(([name, stage]) => ({
            name,
            duration: stage.duration || null,
            status: stage.status,
            percentage: stage.duration ? ((stage.duration / totalDuration) * 100).toFixed(1) : null
        }));

        return {
            operationId: this.operationId,
            totalDuration,
            totalDurationSeconds: (totalDuration / 1000).toFixed(3),
            stages: stagesSummary,
            metadata: this.metadata,
            completedAt: new Date().toISOString()
        };
    }

    /**
     * Complete the operation and log final summary
     * @param {object} finalResult - Final result of the operation
     */
    complete(finalResult = {}) {
        const summary = this.getSummary();
        summary.result = finalResult;

        logger.info('Operation completed', {
            ...summary,
            performance: {
                totalTime: summary.totalDuration,
                slowStages: summary.stages.filter(s => s.duration > 1000),
                fastestStage: summary.stages.reduce((min, stage) => 
                    stage.duration && (!min || stage.duration < min.duration) ? stage : min, null),
                slowestStage: summary.stages.reduce((max, stage) => 
                    stage.duration && (!max || stage.duration > max.duration) ? stage : max, null)
            }
        });

        return summary;
    }

    /**
     * Create a child timer for nested operations
     * @param {string} childName - Name for the child operation
     */
    createChild(childName) {
        const childId = `${this.operationId}_${childName}`;
        const child = new TimingTracker(childId);
        child.addMetadata({ parentOperation: this.operationId });
        return child;
    }
}

/**
 * Simple timing decorator for functions
 * @param {string} operationName - Name of the operation
 * @param {Function} fn - Function to time
 * @param {object} metadata - Additional metadata
 */
async function timeOperation(operationName, fn, metadata = {}) {
    const timer = new TimingTracker();
    timer.addMetadata(metadata);
    timer.startStage(operationName);
    
    try {
        const result = await fn();
        timer.endStage(operationName, { success: true });
        timer.complete(result);
        return result;
    } catch (error) {
        timer.failStage(operationName, error);
        timer.complete({ success: false, error: error.toString() });
        throw error;
    }
}

/**
 * Middleware for Express to time HTTP requests
 */
function timingMiddleware(req, res, next) {
    const timer = new TimingTracker();
    timer.addMetadata({
        method: req.method,
        url: req.url,
        userAgent: req.get('User-Agent'),
        contentLength: req.get('Content-Length')
    });
    
    req.timer = timer;
    timer.startStage('http_request');
    
    // Override res.end to capture response timing
    const originalEnd = res.end;
    res.end = function(...args) {
        timer.endStage('http_request', {
            statusCode: res.statusCode,
            responseSize: res.get('Content-Length')
        });
        timer.complete({
            statusCode: res.statusCode,
            success: res.statusCode < 400
        });
        originalEnd.apply(res, args);
    };
    
    next();
}

/**
 * Neo4j Query Timer - specialized for database operations
 */
class Neo4jQueryTimer {
    constructor(operationId = null) {
        this.operationId = operationId || `neo4j_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        this.queries = [];
        this.startTime = Date.now();
    }

    /**
     * Time a Neo4j query execution
     * @param {string} query - The Cypher query
     * @param {object} parameters - Query parameters
     * @param {Function} queryFunction - Function that executes the query
     */
    async timeQuery(query, parameters = {}, queryFunction) {
        const queryId = `query_${this.queries.length + 1}`;
        const startTime = Date.now();

        const queryInfo = {
            queryId,
            query: query.substring(0, 200) + (query.length > 200 ? '...' : ''), // Truncate long queries
            parameters: Object.keys(parameters),
            startTime,
            status: 'running'
        };

        this.queries.push(queryInfo);

        logger.info('Neo4j query started', {
            operationId: this.operationId,
            queryId,
            query: queryInfo.query,
            parameterCount: Object.keys(parameters).length
        });

        try {
            const result = await queryFunction();

            const endTime = Date.now();
            const duration = endTime - startTime;

            queryInfo.endTime = endTime;
            queryInfo.duration = duration;
            queryInfo.status = 'completed';
            queryInfo.recordCount = result?.records?.length || 0;
            queryInfo.summary = result?.summary ? {
                queryType: result.summary.queryType,
                counters: result.summary.counters,
                resultAvailableAfter: result.summary.resultAvailableAfter?.toNumber(),
                resultConsumedAfter: result.summary.resultConsumedAfter?.toNumber()
            } : null;

            logger.info('Neo4j query completed', {
                operationId: this.operationId,
                queryId,
                duration,
                durationMs: duration,
                recordCount: queryInfo.recordCount,
                queryType: queryInfo.summary?.queryType,
                slow: duration > 1000
            });

            return result;
        } catch (error) {
            const endTime = Date.now();
            const duration = endTime - startTime;

            queryInfo.endTime = endTime;
            queryInfo.duration = duration;
            queryInfo.status = 'failed';
            queryInfo.error = error.message;

            logger.error('Neo4j query failed', {
                operationId: this.operationId,
                queryId,
                duration,
                error: error.message,
                query: queryInfo.query
            });

            throw error;
        }
    }

    /**
     * Get summary of all queries in this operation
     */
    getSummary() {
        const totalDuration = Date.now() - this.startTime;
        const completedQueries = this.queries.filter(q => q.status === 'completed');
        const failedQueries = this.queries.filter(q => q.status === 'failed');

        const queryDurations = completedQueries.map(q => q.duration);
        const totalQueryTime = queryDurations.reduce((sum, duration) => sum + duration, 0);

        return {
            operationId: this.operationId,
            totalDuration,
            totalQueryTime,
            queryCount: this.queries.length,
            completedQueries: completedQueries.length,
            failedQueries: failedQueries.length,
            averageQueryTime: completedQueries.length > 0 ? totalQueryTime / completedQueries.length : 0,
            slowestQuery: queryDurations.length > 0 ? Math.max(...queryDurations) : 0,
            fastestQuery: queryDurations.length > 0 ? Math.min(...queryDurations) : 0,
            queries: this.queries.map(q => ({
                queryId: q.queryId,
                duration: q.duration,
                status: q.status,
                recordCount: q.recordCount,
                queryType: q.summary?.queryType,
                error: q.error
            }))
        };
    }
}

/**
 * Performance monitoring utilities
 */
const PerformanceMonitor = {
    /**
     * Track memory usage at a point in time
     */
    getMemoryUsage() {
        const usage = process.memoryUsage();
        return {
            rss: Math.round(usage.rss / 1024 / 1024), // MB
            heapTotal: Math.round(usage.heapTotal / 1024 / 1024), // MB
            heapUsed: Math.round(usage.heapUsed / 1024 / 1024), // MB
            external: Math.round(usage.external / 1024 / 1024), // MB
        };
    },

    /**
     * Get system load information
     */
    getSystemLoad() {
        const os = require('os');
        return {
            loadAverage: os.loadavg(),
            cpuCount: os.cpus().length,
            freeMemory: Math.round(os.freemem() / 1024 / 1024), // MB
            totalMemory: Math.round(os.totalmem() / 1024 / 1024), // MB
        };
    }
};

module.exports = {
    TimingTracker,
    Neo4jQueryTimer,
    timeOperation,
    timingMiddleware,
    PerformanceMonitor
};
