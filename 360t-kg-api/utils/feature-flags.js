/**
 * Simple Feature Flag System for Migration
 */

class FeatureFlags {
    constructor() {
        this.flags = {
            // Backend Architecture Migration
            NEW_CONTROLLER_LAYER: this.getFlag('NEW_CONTROLLER_LAYER', false),
            NEW_SERVICE_LAYER: this.getFlag('NEW_SERVICE_LAYER', false),
            NEW_REPOSITORY_PATTERN: this.getFlag('NEW_REPOSITORY_PATTERN', false),
            NEW_MIDDLEWARE_STACK: this.getFlag('NEW_MIDDLEWARE_STACK', false),
            
            // Service Extraction
            NEW_CHAT_SERVICE: this.getFlag('NEW_CHAT_SERVICE', false),
            CIRCUIT_BREAKER_ENABLED: this.getFlag('CIRCUIT_BREAKER_ENABLED', true),
            
            // Migration Safety
            DUAL_EXECUTION_MODE: this.getFlag('DUAL_EXECUTION_MODE', true),
            PERFORMANCE_MONITORING: this.getFlag('PERFORMANCE_MONITORING', true),
            AUTOMATIC_ROLLBACK: this.getFlag('AUTOMATIC_ROLLBACK', true),
            
            // Traffic Routing
            TRAFFIC_PERCENTAGE_NEW_API: this.getFlag('TRAFFIC_PERCENTAGE_NEW_API', 0),
            
            // Development & Testing
            DEBUG_MODE: this.getFlag('DEBUG_MODE', false),
            VERBOSE_LOGGING: this.getFlag('VERBOSE_LOGGING', false),
            MIGRATION_METRICS: this.getFlag('MIGRATION_METRICS', true)
        };
    }
    
    getFlag(flagName, defaultValue) {
        const envKey = `FEATURE_FLAG_${flagName}`;
        const envValue = process.env[envKey];
        
        if (envValue !== undefined) {
            if (envValue.toLowerCase() === 'true') return true;
            if (envValue.toLowerCase() === 'false') return false;
            const numValue = Number(envValue);
            if (!isNaN(numValue)) return numValue;
            return envValue;
        }
        
        return defaultValue;
    }
    
    isEnabled(flagName) {
        return Boolean(this.flags[flagName]);
    }
    
    getTrafficPercentage(flagName) {
        const value = this.flags[flagName];
        return Math.max(0, Math.min(100, Number(value) || 0));
    }
    
    shouldUseNewFeature(flagName, userId = null) {
        const percentage = this.getTrafficPercentage(flagName);
        
        if (percentage === 0) return false;
        if (percentage === 100) return true;
        
        const hash = userId ? this.hashUserId(userId) : Math.random() * 100;
        return hash < percentage;
    }
    
    hashUserId(userId) {
        let hash = 0;
        for (let i = 0; i < userId.length; i++) {
            const char = userId.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash) % 100;
    }
    
    getAllFlags() {
        return { ...this.flags };
    }
    
    setFlag(flagName, value) {
        if (this.flags.hasOwnProperty(flagName)) {
            this.flags[flagName] = value;
        } else {
            throw new Error(`Unknown feature flag: ${flagName}`);
        }
    }
    
    getMigrationPhase() {
        const backendFlags = [
            'NEW_CONTROLLER_LAYER',
            'NEW_SERVICE_LAYER', 
            'NEW_REPOSITORY_PATTERN',
            'NEW_MIDDLEWARE_STACK'
        ];
        
        const backendEnabled = backendFlags.filter(flag => this.isEnabled(flag)).length;
        
        return {
            backend: {
                enabled: backendEnabled,
                total: backendFlags.length,
                percentage: Math.round((backendEnabled / backendFlags.length) * 100)
            },
            services: {
                chatService: this.isEnabled('NEW_CHAT_SERVICE'),
                circuitBreaker: this.isEnabled('CIRCUIT_BREAKER_ENABLED')
            },
            traffic: {
                api: this.getTrafficPercentage('TRAFFIC_PERCENTAGE_NEW_API')
            }
        };
    }
}

const featureFlags = new FeatureFlags();

module.exports = featureFlags;
