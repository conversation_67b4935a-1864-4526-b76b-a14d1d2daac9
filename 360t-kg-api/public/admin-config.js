class KGConfigManager {
    constructor() {
        this.currentConfig = {};
        this.searchRecipes = {};
        this.llmProviders = {};
        this.init();
    }

    async init() {
        await this.loadInitialData();
        this.setupEventListeners();
        this.startPeriodicRefresh();
    }

    async loadInitialData() {
        try {
            // Load search recipes
            const recipesResponse = await fetch('/api/config/search-recipes');
            this.searchRecipes = await recipesResponse.json();
            this.populateSearchRecipes();

            // Load LLM providers
            const providersResponse = await fetch('/api/config/llm-providers');
            this.llmProviders = await providersResponse.json();
            this.populateLLMProviders();

            // Load current configuration
            await this.loadCurrentConfig();

            // Load initial metrics
            await this.refreshMetrics();

        } catch (error) {
            this.showAlert('Error loading initial data: ' + error.message, 'error');
        }
    }

    async loadCurrentConfig() {
        try {
            const response = await fetch('/api/config/current');
            this.currentConfig = await response.json();
            this.updateUIFromConfig();
        } catch (error) {
            this.showAlert('Error loading current configuration: ' + error.message, 'error');
        }
    }

    populateSearchRecipes() {
        const select = document.getElementById('searchRecipe');
        select.innerHTML = '';

        // Add options grouped by category
        Object.entries(this.searchRecipes).forEach(([category, recipes]) => {
            const optgroup = document.createElement('optgroup');
            optgroup.label = this.formatCategoryName(category);
            
            recipes.forEach(recipe => {
                const option = document.createElement('option');
                option.value = recipe.key;
                option.textContent = recipe.name;
                optgroup.appendChild(option);
            });
            
            select.appendChild(optgroup);
        });

        // Also populate the detailed view
        this.populateRecipeCategories();
    }

    populateRecipeCategories() {
        const container = document.getElementById('recipeCategoriesContent');
        container.innerHTML = '';

        Object.entries(this.searchRecipes).forEach(([category, recipes]) => {
            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'recipe-category';
            
            const title = document.createElement('h4');
            title.textContent = this.formatCategoryName(category) + ` (${recipes.length} options)`;
            categoryDiv.appendChild(title);

            const optionsDiv = document.createElement('div');
            optionsDiv.className = 'recipe-options';

            recipes.forEach(recipe => {
                const optionDiv = document.createElement('div');
                optionDiv.className = 'recipe-option';
                optionDiv.dataset.recipe = recipe.key;
                
                optionDiv.innerHTML = `
                    <div class="recipe-name">${recipe.name}</div>
                    <div class="recipe-description">${recipe.description}</div>
                `;

                optionDiv.addEventListener('click', () => {
                    document.getElementById('searchRecipe').value = recipe.key;
                    this.updateRecipeSelection();
                });

                optionsDiv.appendChild(optionDiv);
            });

            categoryDiv.appendChild(optionsDiv);
            container.appendChild(categoryDiv);
        });

        document.getElementById('recipeCategories').classList.remove('hidden');
    }

    populateLLMProviders() {
        const select = document.getElementById('llmProvider');
        select.innerHTML = '';

        this.llmProviders.forEach(provider => {
            const option = document.createElement('option');
            option.value = provider.key;
            option.textContent = `${provider.name} ${provider.hasApiKey ? '' : '(Missing API Key)'}`;
            option.disabled = !provider.hasApiKey;
            select.appendChild(option);
        });
    }

    updateUIFromConfig() {
        document.getElementById('searchRecipe').value = this.currentConfig.searchRecipe || '';
        document.getElementById('resultsLimit').value = this.currentConfig.resultsLimit || 10;
        document.getElementById('llmProvider').value = this.currentConfig.llmProvider || '';
        
        this.updateRecipeSelection();
        this.updateProviderModels();
        this.updateProviderStatus();
    }

    updateRecipeSelection() {
        const selectedRecipe = document.getElementById('searchRecipe').value;
        
        // Update visual selection in detailed view
        document.querySelectorAll('.recipe-option').forEach(option => {
            option.classList.toggle('selected', option.dataset.recipe === selectedRecipe);
        });
    }

    updateProviderModels() {
        const selectedProvider = document.getElementById('llmProvider').value;
        const modelSelect = document.getElementById('llmModel');
        
        modelSelect.innerHTML = '';

        if (selectedProvider) {
            const provider = this.llmProviders.find(p => p.key === selectedProvider);
            if (provider && provider.models) {
                provider.models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model;
                    option.textContent = model;
                    modelSelect.appendChild(option);
                });

                // Set current model if available
                if (this.currentConfig.llmModel) {
                    modelSelect.value = this.currentConfig.llmModel;
                }
            }
        } else {
            const option = document.createElement('option');
            option.value = '';
            option.textContent = 'Select provider first';
            modelSelect.appendChild(option);
        }
    }

    updateProviderStatus() {
        const selectedProvider = document.getElementById('llmProvider').value;
        const statusDiv = document.getElementById('providerStatus');
        
        if (selectedProvider) {
            const provider = this.llmProviders.find(p => p.key === selectedProvider);
            if (provider) {
                const statusClass = provider.hasApiKey ? 'status-connected' : 'status-disconnected';
                const statusText = provider.hasApiKey ? 'Connected' : 'Missing API Key';
                
                statusDiv.innerHTML = `
                    <span class="status-indicator ${statusClass}"></span>
                    Status: ${statusText}
                `;
            }
        } else {
            statusDiv.innerHTML = `
                <span class="status-indicator status-disconnected"></span>
                Status: No provider selected
            `;
        }
    }

    setupEventListeners() {
        // Search recipe change
        document.getElementById('searchRecipe').addEventListener('change', () => {
            this.updateRecipeSelection();
        });

        // LLM provider change
        document.getElementById('llmProvider').addEventListener('change', () => {
            this.updateProviderModels();
            this.updateProviderStatus();
        });

        // Apply search configuration
        document.getElementById('applySearchConfig').addEventListener('click', () => {
            this.applySearchConfig();
        });

        // Apply LLM configuration
        document.getElementById('applyLLMConfig').addEventListener('click', () => {
            this.applyLLMConfig();
        });

        // Test provider
        document.getElementById('testProvider').addEventListener('click', () => {
            this.testProvider();
        });

        // Refresh metrics
        document.getElementById('refreshMetrics').addEventListener('click', () => {
            this.refreshMetrics();
        });
    }

    async applySearchConfig() {
        const button = document.getElementById('applySearchConfig');
        const loading = button.querySelector('.loading');
        
        try {
            this.setLoading(button, true);
            
            const recipe = document.getElementById('searchRecipe').value;
            const resultsLimit = parseInt(document.getElementById('resultsLimit').value);

            const response = await fetch('/api/config/search-recipe', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ recipe, resultsLimit })
            });

            if (response.ok) {
                this.currentConfig = await response.json();
                this.showAlert('Search configuration updated successfully!', 'success');
                await this.refreshMetrics();
            } else {
                const error = await response.json();
                throw new Error(error.error || 'Failed to update search configuration');
            }
        } catch (error) {
            this.showAlert('Error updating search configuration: ' + error.message, 'error');
        } finally {
            this.setLoading(button, false);
        }
    }

    async applyLLMConfig() {
        const button = document.getElementById('applyLLMConfig');
        
        try {
            this.setLoading(button, true);
            
            const provider = document.getElementById('llmProvider').value;
            const model = document.getElementById('llmModel').value;

            if (!provider || !model) {
                throw new Error('Please select both provider and model');
            }

            const response = await fetch('/api/config/llm-provider', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ provider, model })
            });

            if (response.ok) {
                this.currentConfig = await response.json();
                this.showAlert('LLM configuration updated successfully!', 'success');
                await this.refreshMetrics();
            } else {
                const error = await response.json();
                throw new Error(error.error || 'Failed to update LLM configuration');
            }
        } catch (error) {
            this.showAlert('Error updating LLM configuration: ' + error.message, 'error');
        } finally {
            this.setLoading(button, false);
        }
    }

    async testProvider() {
        const button = document.getElementById('testProvider');
        
        try {
            this.setLoading(button, true);
            
            const provider = document.getElementById('llmProvider').value;
            const model = document.getElementById('llmModel').value;

            if (!provider || !model) {
                throw new Error('Please select both provider and model');
            }

            const response = await fetch('/api/config/test-provider', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ provider, model })
            });

            if (response.ok) {
                const result = await response.json();
                this.showAlert(
                    `Provider test successful! Latency: ${Math.round(result.latency)}ms`, 
                    'success'
                );
            } else {
                const error = await response.json();
                throw new Error(error.error || 'Provider test failed');
            }
        } catch (error) {
            this.showAlert('Provider test failed: ' + error.message, 'error');
        } finally {
            this.setLoading(button, false);
        }
    }

    async refreshMetrics() {
        const button = document.getElementById('refreshMetrics');
        
        try {
            this.setLoading(button, true);
            
            const response = await fetch('/api/config/status');
            if (response.ok) {
                const status = await response.json();
                this.updateMetricsDisplay(status);
            } else {
                throw new Error('Failed to fetch metrics');
            }
        } catch (error) {
            this.showAlert('Error refreshing metrics: ' + error.message, 'error');
        } finally {
            this.setLoading(button, false);
        }
    }

    updateMetricsDisplay(status) {
        document.getElementById('lastQueryTime').textContent = 'N/A'; // Would come from performance data
        document.getElementById('currentRecipe').textContent = this.formatRecipeName(status.searchRecipe);
        document.getElementById('resultsReturned').textContent = `${status.resultsLimit} max`;
        document.getElementById('currentProvider').textContent = `${status.llmProvider} (${status.llmModel})`;
    }

    formatCategoryName(category) {
        const names = {
            'combined': 'Combined Search',
            'edge': 'Edge-Focused Search',
            'node': 'Node-Focused Search',
            'community': 'Community Search'
        };
        return names[category] || category;
    }

    formatRecipeName(recipe) {
        return recipe ? recipe.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'N/A';
    }

    setLoading(button, loading) {
        const loadingSpinner = button.querySelector('.loading');
        if (loading) {
            loadingSpinner.classList.remove('hidden');
            button.disabled = true;
        } else {
            loadingSpinner.classList.add('hidden');
            button.disabled = false;
        }
    }

    showAlert(message, type) {
        const alertsContainer = document.getElementById('alerts');
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.textContent = message;
        
        alertsContainer.appendChild(alert);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 5000);
    }

    startPeriodicRefresh() {
        // Refresh metrics every 30 seconds
        setInterval(() => {
            this.refreshMetrics();
        }, 30000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new KGConfigManager();
});
