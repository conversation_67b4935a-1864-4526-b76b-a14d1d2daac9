<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knowledge Graph Configuration</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .content {
            padding: 40px;
        }

        .config-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 4px solid #3498db;
        }

        .config-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #34495e;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
            transform: translateY(-2px);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-connected {
            background: #27ae60;
        }

        .status-disconnected {
            background: #e74c3c;
        }

        .status-warning {
            background: #f39c12;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e1e8ed;
            text-align: center;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }

        .metric-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .recipe-category {
            margin-bottom: 15px;
        }

        .recipe-category h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .recipe-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 10px;
        }

        .recipe-option {
            padding: 10px;
            border: 1px solid #e1e8ed;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .recipe-option:hover {
            background: #f8f9fa;
            border-color: #3498db;
        }

        .recipe-option.selected {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .recipe-name {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .recipe-description {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .alert {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        .provider-models {
            margin-top: 10px;
        }

        .model-option {
            padding: 8px 12px;
            margin: 5px;
            background: #f8f9fa;
            border: 1px solid #e1e8ed;
            border-radius: 4px;
            cursor: pointer;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .model-option:hover {
            background: #e9ecef;
        }

        .model-option.selected {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }
            
            .recipe-options {
                grid-template-columns: 1fr;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Knowledge Graph Configuration</h1>
            <p>Configure search recipes and LLM providers for optimal performance</p>
        </div>

        <div class="content">
            <!-- Alerts -->
            <div id="alerts"></div>

            <div class="two-column">
                <!-- Search Configuration -->
                <div class="config-section">
                    <h2>Search Configuration</h2>
                    
                    <div class="form-group">
                        <label for="searchRecipe">Search Recipe:</label>
                        <select id="searchRecipe" class="form-control">
                            <option value="">Loading...</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="resultsLimit">Results Limit:</label>
                        <input type="number" id="resultsLimit" class="form-control" min="1" max="50" value="10">
                    </div>

                    <div class="form-group">
                        <button id="applySearchConfig" class="btn btn-primary">
                            <span class="loading hidden"></span>
                            Apply Search Configuration
                        </button>
                    </div>

                    <!-- Recipe Categories -->
                    <div id="recipeCategories" class="hidden">
                        <h3>Available Recipes by Category:</h3>
                        <div id="recipeCategoriesContent"></div>
                    </div>
                </div>

                <!-- LLM Provider Configuration -->
                <div class="config-section">
                    <h2>LLM Provider Configuration</h2>
                    
                    <div class="form-group">
                        <label for="llmProvider">Provider:</label>
                        <select id="llmProvider" class="form-control">
                            <option value="">Loading...</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="llmModel">Model:</label>
                        <select id="llmModel" class="form-control">
                            <option value="">Select provider first</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <div id="providerStatus">
                            <span class="status-indicator status-disconnected"></span>
                            Status: Not connected
                        </div>
                    </div>

                    <div class="form-group">
                        <button id="testProvider" class="btn btn-warning">
                            <span class="loading hidden"></span>
                            Test Provider
                        </button>
                        <button id="applyLLMConfig" class="btn btn-primary">
                            <span class="loading hidden"></span>
                            Apply LLM Configuration
                        </button>
                    </div>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="config-section">
                <h2>Performance Metrics</h2>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value" id="lastQueryTime">--</div>
                        <div class="metric-label">Last Query Time</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="currentRecipe">--</div>
                        <div class="metric-label">Current Recipe</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="resultsReturned">--</div>
                        <div class="metric-label">Results Returned</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="currentProvider">--</div>
                        <div class="metric-label">Current Provider</div>
                    </div>
                </div>
                
                <div class="form-group" style="margin-top: 20px;">
                    <button id="refreshMetrics" class="btn btn-success">
                        <span class="loading hidden"></span>
                        Refresh Metrics
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="admin-config.js"></script>
</body>
</html>
