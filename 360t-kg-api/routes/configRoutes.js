const express = require('express');
const fs = require('fs').promises;
const path = require('path');

module.exports = (driver) => {
    const router = express.Router();
    const configPath = path.join(__dirname, '..', 'config', 'kg-config.json');

    // Default configuration
    const defaultConfig = {
        searchRecipe: 'COMBINED_HYBRID_SEARCH_RRF',
        llmProvider: 'ollama',
        llmModel: 'gemma3:12b',
        resultsLimit: 10,
        customPrompt: '',
        lastUpdated: new Date().toISOString()
    };

    // Default prompt template
    const defaultPrompt = `You are a FOREX domain knowledge expert answering questions using *only* the supplied context.

=== CONTEXT ===
{context}
=== QUESTION ===
{question}

Instructions:
1. Begin with a concise answer, then elaborate.
2. Cite facts using the bracket numbers provided in CONTEXT (e.g. [1]).
3. Format output in clean Markdown with headings, lists, and **bold** keywords.
4. If context is insufficient, state so clearly.
5. Finish with a "### 💡 Related" section suggesting 2 follow‑up questions.`;

    // Available search recipes with metadata
    const searchRecipes = {
        // Combined Search
        'COMBINED_HYBRID_SEARCH_RRF': {
            name: 'Combined Hybrid Search (RRF)',
            category: 'combined',
            description: 'Hybrid search with RRF reranking over edges, nodes, and communities',
            scopes: ['edges', 'nodes', 'communities']
        },
        'COMBINED_HYBRID_SEARCH_MMR': {
            name: 'Combined Hybrid Search (MMR)',
            category: 'combined',
            description: 'Hybrid search with MMR reranking over edges, nodes, and communities',
            scopes: ['edges', 'nodes', 'communities']
        },
        'COMBINED_HYBRID_SEARCH_CROSS_ENCODER': {
            name: 'Combined Hybrid Search (Cross-Encoder)',
            category: 'combined',
            description: 'Full-text + similarity + BFS with cross-encoder reranking',
            scopes: ['edges', 'nodes', 'communities']
        },
        
        // Edge-Focused Search
        'EDGE_HYBRID_SEARCH_RRF': {
            name: 'Edge Hybrid Search (RRF)',
            category: 'edge',
            description: 'Hybrid edge search with RRF reranking',
            scopes: ['edges']
        },
        'EDGE_HYBRID_SEARCH_MMR': {
            name: 'Edge Hybrid Search (MMR)',
            category: 'edge',
            description: 'Hybrid edge search with MMR reranking',
            scopes: ['edges']
        },
        'EDGE_HYBRID_SEARCH_NODE_DISTANCE': {
            name: 'Edge Hybrid Search (Node Distance)',
            category: 'edge',
            description: 'Hybrid edge search with node distance reranking',
            scopes: ['edges']
        },
        'EDGE_HYBRID_SEARCH_EPISODE_MENTIONS': {
            name: 'Edge Hybrid Search (Episode Mentions)',
            category: 'edge',
            description: 'Hybrid edge search with episode mention reranking',
            scopes: ['edges']
        },
        'EDGE_HYBRID_SEARCH_CROSS_ENCODER': {
            name: 'Edge Hybrid Search (Cross-Encoder)',
            category: 'edge',
            description: 'Hybrid edge search with cross-encoder reranking',
            scopes: ['edges']
        },

        // Node-Focused Search
        'NODE_HYBRID_SEARCH_RRF': {
            name: 'Node Hybrid Search (RRF)',
            category: 'node',
            description: 'Hybrid node search with RRF reranking',
            scopes: ['nodes']
        },
        'NODE_HYBRID_SEARCH_MMR': {
            name: 'Node Hybrid Search (MMR)',
            category: 'node',
            description: 'Hybrid node search with MMR reranking',
            scopes: ['nodes']
        },
        'NODE_HYBRID_SEARCH_NODE_DISTANCE': {
            name: 'Node Hybrid Search (Node Distance)',
            category: 'node',
            description: 'Hybrid node search with node distance reranking',
            scopes: ['nodes']
        },
        'NODE_HYBRID_SEARCH_EPISODE_MENTIONS': {
            name: 'Node Hybrid Search (Episode Mentions)',
            category: 'node',
            description: 'Hybrid node search with episode mentions reranking',
            scopes: ['nodes']
        },
        'NODE_HYBRID_SEARCH_CROSS_ENCODER': {
            name: 'Node Hybrid Search (Cross-Encoder)',
            category: 'node',
            description: 'Hybrid node search with cross-encoder reranking',
            scopes: ['nodes']
        },

        // Community Search
        'COMMUNITY_HYBRID_SEARCH_RRF': {
            name: 'Community Hybrid Search (RRF)',
            category: 'community',
            description: 'Hybrid community search with RRF reranking',
            scopes: ['communities']
        },
        'COMMUNITY_HYBRID_SEARCH_MMR': {
            name: 'Community Hybrid Search (MMR)',
            category: 'community',
            description: 'Hybrid community search with MMR reranking',
            scopes: ['communities']
        },
        'COMMUNITY_HYBRID_SEARCH_CROSS_ENCODER': {
            name: 'Community Hybrid Search (Cross-Encoder)',
            category: 'community',
            description: 'Hybrid community search with cross-encoder reranking',
            scopes: ['communities']
        }
    };

    // Available LLM providers
    const llmProviders = {
        'ollama': {
            name: 'Ollama (Local)',
            type: 'local',
            baseUrl: 'http://localhost:11434/v1',
            models: ['gemma3:12b', 'deepseek-r1:7b', 'deepseek-r1:32b'],
            requiresApiKey: false
        },
        'google': {
            name: 'Google Gemini',
            type: 'cloud',
            baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
            models: ['gemini-2.0-flash-exp', 'gemini-1.5-pro'],
            requiresApiKey: true,
            envVar: 'GOOGLE_API_KEY'
        },
        'openai': {
            name: 'OpenAI',
            type: 'cloud',
            baseUrl: 'https://api.openai.com/v1',
            models: ['gpt-4o-mini', 'gpt-4o', 'gpt-3.5-turbo'],
            requiresApiKey: true,
            envVar: 'OPENAI_API_KEY'
        }
    };

    // Ensure config directory exists
    const ensureConfigDir = async () => {
        const configDir = path.dirname(configPath);
        try {
            await fs.access(configDir);
        } catch {
            await fs.mkdir(configDir, { recursive: true });
        }
    };

    // Load configuration
    const loadConfig = async () => {
        try {
            await ensureConfigDir();
            const data = await fs.readFile(configPath, 'utf8');
            return { ...defaultConfig, ...JSON.parse(data) };
        } catch (error) {
            // Return default config if file doesn't exist
            return defaultConfig;
        }
    };

    // Save configuration
    const saveConfig = async (config) => {
        await ensureConfigDir();
        const configToSave = {
            ...config,
            lastUpdated: new Date().toISOString()
        };
        await fs.writeFile(configPath, JSON.stringify(configToSave, null, 2));
        return configToSave;
    };

    // GET /api/config/search-recipes - List all available search recipes
    router.get('/search-recipes', (req, res) => {
        const categorized = {
            combined: [],
            edge: [],
            node: [],
            community: []
        };

        Object.entries(searchRecipes).forEach(([key, recipe]) => {
            categorized[recipe.category].push({
                key,
                ...recipe
            });
        });

        res.json(categorized);
    });

    // GET /api/config/llm-providers - List available LLM providers
    router.get('/llm-providers', (req, res) => {
        const providersWithStatus = Object.entries(llmProviders).map(([key, provider]) => {
            let hasApiKey = true;
            if (provider.requiresApiKey) {
                hasApiKey = !!process.env[provider.envVar];
            }

            return {
                key,
                ...provider,
                hasApiKey,
                status: hasApiKey ? 'available' : 'missing_api_key'
            };
        });

        res.json(providersWithStatus);
    });

    // GET /api/config/current - Get current configuration
    router.get('/current', async (req, res) => {
        try {
            const config = await loadConfig();
            res.json(config);
        } catch (error) {
            console.error('Error loading configuration:', error);
            res.status(500).json({ error: 'Failed to load configuration' });
        }
    });

    // POST /api/config/search-recipe - Update search recipe
    router.post('/search-recipe', async (req, res) => {
        try {
            const { recipe, resultsLimit } = req.body;

            if (!searchRecipes[recipe]) {
                return res.status(400).json({ error: 'Invalid search recipe' });
            }

            const config = await loadConfig();
            config.searchRecipe = recipe;
            if (resultsLimit !== undefined) {
                config.resultsLimit = Math.max(1, Math.min(50, parseInt(resultsLimit)));
            }

            const savedConfig = await saveConfig(config);
            res.json(savedConfig);
        } catch (error) {
            console.error('Error updating search recipe:', error);
            res.status(500).json({ error: 'Failed to update search recipe' });
        }
    });

    // POST /api/config/llm-provider - Update LLM provider
    router.post('/llm-provider', async (req, res) => {
        try {
            const { provider, model } = req.body;

            if (!llmProviders[provider]) {
                return res.status(400).json({ error: 'Invalid LLM provider' });
            }

            const providerConfig = llmProviders[provider];
            if (!providerConfig.models.includes(model)) {
                return res.status(400).json({ error: 'Invalid model for provider' });
            }

            const config = await loadConfig();
            config.llmProvider = provider;
            config.llmModel = model;

            const savedConfig = await saveConfig(config);
            res.json(savedConfig);
        } catch (error) {
            console.error('Error updating LLM provider:', error);
            res.status(500).json({ error: 'Failed to update LLM provider' });
        }
    });

    // POST /api/config/test-provider - Test provider connection
    router.post('/test-provider', async (req, res) => {
        try {
            const { provider, model } = req.body;
            const providerConfig = llmProviders[provider];

            if (!providerConfig) {
                return res.status(400).json({ error: 'Invalid provider' });
            }

            // Test connection logic would go here
            // For now, return mock success
            res.json({
                success: true,
                provider,
                model,
                latency: Math.random() * 1000 + 500, // Mock latency
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('Error testing provider:', error);
            res.status(500).json({ error: 'Failed to test provider' });
        }
    });

    // GET /api/config/status - Get system status
    router.get('/status', async (req, res) => {
        try {
            const config = await loadConfig();
            const providerConfig = llmProviders[config.llmProvider];
            
            res.json({
                searchRecipe: config.searchRecipe,
                llmProvider: config.llmProvider,
                llmModel: config.llmModel,
                resultsLimit: config.resultsLimit,
                providerStatus: providerConfig?.requiresApiKey ? 
                    (process.env[providerConfig.envVar] ? 'connected' : 'missing_api_key') : 
                    'connected',
                lastUpdated: config.lastUpdated
            });
        } catch (error) {
            console.error('Error getting status:', error);
            res.status(500).json({ error: 'Failed to get status' });
        }
    });

    // GET /api/config/prompt - Get current prompt configuration
    router.get('/prompt', async (req, res) => {
        try {
            const config = await loadConfig();
            res.json({
                customPrompt: config.customPrompt || '',
                defaultPrompt: defaultPrompt
            });
        } catch (error) {
            console.error('Error getting prompt config:', error);
            res.status(500).json({ error: 'Failed to get prompt configuration' });
        }
    });

    // POST /api/config/prompt - Update prompt configuration
    router.post('/prompt', async (req, res) => {
        try {
            const { customPrompt } = req.body;

            const config = await loadConfig();
            config.customPrompt = customPrompt || '';
            config.lastUpdated = new Date().toISOString();

            const savedConfig = await saveConfig(config);
            res.json({
                customPrompt: savedConfig.customPrompt,
                message: 'Prompt configuration updated successfully'
            });
        } catch (error) {
            console.error('Error updating prompt config:', error);
            res.status(500).json({ error: 'Failed to update prompt configuration' });
        }
    });

    // POST /api/config/prompt/reset - Reset prompt to default
    router.post('/prompt/reset', async (req, res) => {
        try {
            const config = await loadConfig();
            config.customPrompt = '';
            config.lastUpdated = new Date().toISOString();

            const savedConfig = await saveConfig(config);
            res.json({
                customPrompt: '',
                defaultPrompt: defaultPrompt,
                message: 'Prompt reset to default successfully'
            });
        } catch (error) {
            console.error('Error resetting prompt config:', error);
            res.status(500).json({ error: 'Failed to reset prompt configuration' });
        }
    });

    return router;
};
