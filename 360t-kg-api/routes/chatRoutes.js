const express = require('express');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs').promises;
const crypto = require('crypto');
const { TimingTracker, PerformanceMonitor } = require('../utils/timing');
const Neo4jService = require('../services/neo4jService');
const logger = require('../utils/logger');
const router = express.Router();

/**
 * Parse debug output from Python script to extract thinking data
 */
function parseThinkingData(output) {
  const thinkingData = {};

  // Extract search debug information
  const searchDebugMatch = output.match(/=== GRAPHITI SEARCH DEBUG ===([\s\S]*?)=== END GRAPHITI SEARCH DEBUG ===/);
  if (searchDebugMatch) {
    const searchContent = searchDebugMatch[1];
    thinkingData.searchDebug = parseSearchDebug(searchContent);
  }

  // Extract context information
  const contextMatch = output.match(/=== CONTEXT RECEIVED ===([\s\S]*?)=== CITATIONS ===/);
  if (contextMatch) {
    const contextContent = contextMatch[1];
    thinkingData.context = parseContextInfo(contextContent);
  }

  // Extract citations
  const citationsMatch = output.match(/=== CITATIONS ===([\s\S]*?)=== FULL PROMPT SENT TO LLM ===/);
  if (citationsMatch) {
    const citationsContent = citationsMatch[1];
    thinkingData.citations = parseCitations(citationsContent);
  }

  // Extract prompt information
  const promptMatch = output.match(/=== FULL PROMPT SENT TO LLM ===([\s\S]*?)=== END PROMPT ===/);
  if (promptMatch) {
    const promptContent = promptMatch[1];
    thinkingData.prompt = parsePromptInfo(promptContent);
  }

  // Extract LLM provider information
  const llmProviderMatch = output.match(/=== LLM PROVIDER INFO ===([\s\S]*?)=== END LLM PROVIDER INFO ===/);
  if (llmProviderMatch) {
    const llmProviderContent = llmProviderMatch[1];
    thinkingData.llmProvider = parseLLMProviderInfo(llmProviderContent);
  }

  return thinkingData;
}

function parseSearchDebug(content) {
  const debug = {};

  const queryMatch = content.match(/Query: (.+)/);
  if (queryMatch) debug.query = queryMatch[1];

  const recipeMatch = content.match(/Search Recipe: (.+)/);
  if (recipeMatch) debug.searchRecipe = recipeMatch[1];

  const limitMatch = content.match(/Results Limit: (.+)/);
  if (limitMatch) debug.resultsLimit = parseInt(limitMatch[1]);

  const groupIdsMatch = content.match(/Found (\d+) group_ids containing '([^']+)': (\[.*?\])/);
  if (groupIdsMatch) {
    debug.groupIdFilter = groupIdsMatch[2];
    try {
      debug.groupIds = JSON.parse(groupIdsMatch[3]);
    } catch (e) {
      debug.groupIds = [];
    }
  }

  // Parse detailed search results
  const detailedResultsMatch = content.match(/Search Results Details: (\[.*?\])/);
  if (detailedResultsMatch) {
    try {
      debug.searchResults = JSON.parse(detailedResultsMatch[1]);
    } catch (e) {
      debug.searchResults = [];
    }
  }

  // Parse total results count
  const totalResultsMatch = content.match(/Total Results Found: (\d+)/);
  if (totalResultsMatch) {
    debug.totalResultsFound = parseInt(totalResultsMatch[1]);
  }

  return debug;
}

function parseContextInfo(content) {
  const context = {};

  const lengthMatch = content.match(/Context length: (\d+)/);
  if (lengthMatch) context.length = parseInt(lengthMatch[1]);

  const contentMatch = content.match(/Context content:\n([\s\S]*)/);
  if (contentMatch) context.content = contentMatch[1].trim();

  return context;
}

function parseCitations(content) {
  const citationsMatch = content.match(/Citations: (\[.*?\])/);
  if (citationsMatch) {
    try {
      return JSON.parse(citationsMatch[1]);
    } catch (e) {
      return [];
    }
  }
  return [];
}

function parsePromptInfo(content) {
  const prompt = {};

  const lengthMatch = content.match(/Prompt length: (\d+)/);
  if (lengthMatch) prompt.length = parseInt(lengthMatch[1]);

  const contentMatch = content.match(/Prompt content:\n([\s\S]*)/);
  if (contentMatch) prompt.content = contentMatch[1].trim();

  return prompt;
}

function parseLLMProviderInfo(content) {
  const llmProvider = {};

  const providerMatch = content.match(/Provider: (.+)/);
  if (providerMatch) llmProvider.provider = providerMatch[1].trim();

  const modelMatch = content.match(/Model: (.+)/);
  if (modelMatch) llmProvider.model = modelMatch[1].trim();

  const urlMatch = content.match(/URL: (.+)/);
  if (urlMatch) llmProvider.url = urlMatch[1].trim();

  return llmProvider;
}

module.exports = function(driver) {
  const HISTORY_DIR = path.join(__dirname, '..', 'data', 'chat_history');

  // Initialize Neo4j service with timing
  const neo4jService = new Neo4jService(driver, process.env.NEO4J_DATABASE || 'neo4j');

  // Ensure history directory exists
  const ensureHistoryDir = async () => {
    try {
      await fs.access(HISTORY_DIR);
    } catch (error) {
      await fs.mkdir(HISTORY_DIR, { recursive: true });
    }
  };
  ensureHistoryDir().catch(console.error);

  // Helper to generate a unique ID
  const generateId = () => crypto.randomBytes(8).toString('hex');

  // POST /api/chat/stream
  // Streaming version - receives a message and streams the response in real-time
  router.post('/stream', async (req, res, next) => {
    // Initialize comprehensive timing tracker
    const timer = new TimingTracker();
    timer.addMetadata({
      endpoint: '/api/chat/stream',
      method: 'POST',
      userAgent: req.get('User-Agent'),
      contentLength: req.get('Content-Length'),
      systemLoad: PerformanceMonitor.getSystemLoad(),
      memoryUsage: PerformanceMonitor.getMemoryUsage(),
      streaming: true
    });

    timer.startStage('request_processing');

    logger.info('Chat streaming request received', {
      operationId: timer.operationId,
      messageLength: req.body.message?.length,
      historyLength: req.body.history?.length || 0,
      timestamp: new Date().toISOString()
    });

    const { message, history } = req.body;

    if (!message) {
      timer.failStage('request_processing', 'Message is required');
      logger.warn('Chat streaming request failed: Message is required', {
        operationId: timer.operationId
      });
      return res.status(400).json({
        error: 'Message is required',
        operationId: timer.operationId
      });
    }

    timer.endStage('request_processing');

    // Set up Server-Sent Events
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // Send initial metadata
    res.write(`data: ${JSON.stringify({
      type: 'start',
      operationId: timer.operationId,
      timestamp: new Date().toISOString()
    })}\n\n`);

    try {
      timer.startStage('python_streaming_execution');

      // Call the streaming Python script
      await callPythonStreamingPipeline(message, history, timer, res);

      timer.endStage('python_streaming_execution', { success: true });

      // Send completion event
      const summary = timer.complete({ streaming: true });
      res.write(`data: ${JSON.stringify({
        type: 'complete',
        operationId: timer.operationId,
        performance: summary,
        timestamp: new Date().toISOString()
      })}\n\n`);

      logger.info('Chat streaming request completed successfully', {
        operationId: timer.operationId,
        totalDuration: summary.totalDuration,
        stages: summary.stages
      });

    } catch (error) {
      timer.failStage('python_streaming_execution', error);

      logger.error('Error in chat streaming pipeline', {
        operationId: timer.operationId,
        error: error.message,
        stack: error.stack
      });

      // Send error event
      res.write(`data: ${JSON.stringify({
        type: 'error',
        operationId: timer.operationId,
        error: error.message,
        timestamp: new Date().toISOString()
      })}\n\n`);
    } finally {
      res.end();
    }
  });

  // POST /api/chat/message
  // Receives a message from the user, processes it, and returns a response.
  router.post('/message', async (req, res, next) => {
    // Initialize comprehensive timing tracker
    const timer = new TimingTracker();
    timer.addMetadata({
      endpoint: '/api/chat/message',
      method: 'POST',
      userAgent: req.get('User-Agent'),
      contentLength: req.get('Content-Length'),
      systemLoad: PerformanceMonitor.getSystemLoad(),
      memoryUsage: PerformanceMonitor.getMemoryUsage()
    });

    timer.startStage('request_processing');

    logger.info('Chat message request received', {
      operationId: timer.operationId,
      messageLength: req.body.message?.length,
      historyLength: req.body.history?.length || 0,
      timestamp: new Date().toISOString()
    });

    const { message, history } = req.body;

    if (!message) {
      timer.failStage('request_processing', 'Message is required');
      logger.warn('Chat request failed: Message is required', {
        operationId: timer.operationId
      });
      return res.status(400).json({
        error: 'Message is required',
        operationId: timer.operationId
      });
    }

    timer.endStage('request_processing');

    try {
      timer.startStage('python_pipeline_execution');

      // Call the Python QA pipeline directly with timing
      const result = await callPythonQAPipelineWithTiming(message, history, timer);

      timer.endStage('python_pipeline_execution', {
        success: true,
        answerLength: result.answer?.length,
        sourceDocumentsCount: result.source_documents?.length || 0,
        sourceNodesCount: result.source_nodes?.length || 0
      });

      timer.startStage('response_formatting');

      const responseMessage = {
        role: 'assistant',
        content: result.answer,
        timestamp: new Date().toISOString(),
        // Include source documents if available
        sourceDocuments: result.source_documents || [],
        // Include source nodes if available
        sourceNodes: result.source_nodes || [],
        // Include timing information
        timing: result.timing || {},
        operationId: timer.operationId
      };

      const response = {
        response: responseMessage,
        updatedHistory: [...(history || []),
          { role: 'user', content: message, timestamp: new Date().toISOString() },
          responseMessage
        ],
        performance: timer.getSummary()
      };

      timer.endStage('response_formatting');
      const summary = timer.complete(response);

      logger.info('Chat request completed successfully', {
        operationId: timer.operationId,
        totalDuration: summary.totalDuration,
        stages: summary.stages
      });

      res.json(response);
    } catch (error) {
      timer.failStage('python_pipeline_execution', error);

      logger.error('Error in chat pipeline', {
        operationId: timer.operationId,
        error: error.message,
        stack: error.stack
      });

      timer.startStage('fallback_response');

      // Fallback to a basic response if the Python service fails
      const fallbackMessage = {
        role: 'assistant',
        content: 'I apologize, but I\'m having trouble processing your question right now. The knowledge graph system is temporarily unavailable. Please try again in a moment.',
        timestamp: new Date().toISOString(),
        sourceDocuments: [],
        sourceNodes: [],
        operationId: timer.operationId,
        error: true
      };

      const response = {
        response: fallbackMessage,
        updatedHistory: [...(history || []),
          { role: 'user', content: message, timestamp: new Date().toISOString() },
          fallbackMessage
        ],
        performance: timer.getSummary()
      };

      timer.endStage('fallback_response');
      timer.complete(response);

      res.json(response);
    }
  });

  // Enhanced function to call the Python QA pipeline with detailed timing
  async function callPythonQAPipelineWithTiming(question, history = [], parentTimer) {
    const timer = parentTimer.createChild('python_pipeline');
    timer.addMetadata({
      questionLength: question.length,
      historyLength: history.length,
      scriptPath: 'graphiti_standard_search.py'
    });

    return new Promise((resolve, reject) => {
      timer.startStage('script_setup');

      // Use the simple Python script approach
      const scriptPath = path.resolve('..', 'graphiti_standard_search.py');

      const scriptArgs = [
        scriptPath,
        question,
        '--uri', process.env.NEO4J_URI,
        '--user', process.env.NEO4J_USERNAME,
        '--password', process.env.NEO4J_PASSWORD,
        '--database', process.env.NEO4J_DATABASE || 'neo4j',
        '--timing', 'true' // Enable timing in Python script
      ];

      timer.endStage('script_setup');
      timer.startStage('process_spawn');

      // Execute the Python script with the question and connection details as arguments
      const pythonProcess = spawn('python3', scriptArgs, {
        cwd: path.resolve('..'),
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
            ...process.env, // Inherit parent process environment for other things like PATH
            GOOGLE_API_KEY: process.env.GOOGLE_API_KEY,
            OPERATION_ID: timer.operationId // Pass operation ID to Python
        }
      });

      timer.endStage('process_spawn');
      timer.startStage('python_execution');

      let output = '';
      let errorOutput = '';
      let dataReceived = false;

      pythonProcess.stdout.on('data', (data) => {
        if (!dataReceived) {
          dataReceived = true;
          timer.startStage('data_streaming');
        }
        output += data.toString();
      });

      pythonProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      pythonProcess.on('close', (code) => {
        timer.endStage('python_execution');

        if (dataReceived) {
          timer.endStage('data_streaming');
        }

        timer.startStage('result_parsing');

        if (code !== 0) {
          timer.failStage('result_parsing', `Python script failed with code ${code}: ${errorOutput}`);
          logger.error('Python script execution failed', {
            operationId: timer.operationId,
            exitCode: code,
            errorOutput,
            scriptPath
          });
          reject(new Error(`Python script failed with code ${code}: ${errorOutput}`));
          return;
        }

        try {
          // Parse the JSON output
          const result = JSON.parse(output.trim());

          timer.endStage('result_parsing', {
            success: true,
            resultSize: output.length,
            hasAnswer: !!result.answer,
            hasTiming: !!result.timing
          });

          if (result.error) {
            timer.failStage('python_execution', result.error);
            reject(new Error(result.error));
          } else {
            // Include timing information from Python script
            const enhancedResult = {
              answer: result.answer || 'I could not generate a response.',
              source_documents: result.source_documents || [],
              source_nodes: result.source_nodes || [],
              timing: {
                ...result.timing,
                nodeJsStages: timer.getSummary()
              }
            };

            timer.complete(enhancedResult);
            resolve(enhancedResult);
          }
        } catch (parseError) {
          timer.failStage('result_parsing', parseError);
          logger.error('Failed to parse Python script output', {
            operationId: timer.operationId,
            parseError: parseError.message,
            output: output.substring(0, 500) // Log first 500 chars for debugging
          });
          reject(new Error(`Failed to parse Python script output: ${parseError.message}`));
        }
      });

      // Handle process errors
      pythonProcess.on('error', (error) => {
        timer.failStage('process_spawn', error);
        logger.error('Python process spawn error', {
          operationId: timer.operationId,
          error: error.message
        });
        reject(new Error(`Failed to spawn Python process: ${error.message}`));
      });
    });
  }

  // Streaming Python pipeline function
  async function callPythonStreamingPipeline(question, history = [], parentTimer, res) {
    const timer = parentTimer.createChild('python_streaming_pipeline');
    timer.addMetadata({
      questionLength: question.length,
      historyLength: history.length,
      scriptPath: 'graphiti_streaming_search.py'
    });

    return new Promise((resolve, reject) => {
      timer.startStage('script_setup');

      // Use the streaming Python script
      const scriptPath = path.resolve('..', 'graphiti_streaming_search.py');

      const scriptArgs = [
        scriptPath,
        question,
        '--uri', process.env.NEO4J_URI,
        '--user', process.env.NEO4J_USERNAME,
        '--password', process.env.NEO4J_PASSWORD,
        '--database', process.env.NEO4J_DATABASE || 'neo4j',
        '--timing', 'true'
      ];

      timer.endStage('script_setup');
      timer.startStage('process_spawn');

      // Execute the streaming Python script
      const pythonProcess = spawn('python3', scriptArgs, {
        cwd: path.resolve('..'),
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
            ...process.env,
            GOOGLE_API_KEY: process.env.GOOGLE_API_KEY,
            OPERATION_ID: timer.operationId
        }
      });

      timer.endStage('process_spawn');
      timer.startStage('python_execution');

      let output = '';
      let errorOutput = '';
      let streamingStarted = false;
      let thinkingDataSent = false;

      // Handle streaming output from Python script
      pythonProcess.stdout.on('data', (data) => {
        const chunk = data.toString();
        output += chunk;

        // Check if we've hit the JSON result separator
        if (chunk.includes('---JSON_RESULT---')) {
          if (streamingStarted) {
            timer.endStage('token_streaming');
            streamingStarted = false;
          }
          return; // Don't process the separator line
        }

        // Check for thinking data (debug output) and send it
        if (!thinkingDataSent && output.includes('=== GRAPHITI SEARCH DEBUG ===')) {
          const thinkingData = parseThinkingData(output);
          if (thinkingData && Object.keys(thinkingData).length > 0) {
            res.write(`data: ${JSON.stringify({
              type: 'thinking',
              stage: 'search_debug',
              data: thinkingData,
              operationId: timer.operationId,
              timestamp: new Date().toISOString()
            })}\n\n`);
            thinkingDataSent = true;
          }
        }

        // Check if this looks like streaming tokens (before JSON result)
        if (!streamingStarted && !chunk.trim().startsWith('{') && !chunk.includes('---JSON_RESULT---')) {
          streamingStarted = true;
          timer.startStage('token_streaming');

          // Send streaming start event
          res.write(`data: ${JSON.stringify({
            type: 'stream_start',
            operationId: timer.operationId,
            timestamp: new Date().toISOString()
          })}\n\n`);
        }

        // If we're in streaming mode, send tokens
        if (streamingStarted && !chunk.trim().startsWith('{') && !chunk.includes('---JSON_RESULT---')) {
          // Send each character/token as it comes
          for (const char of chunk) {
            res.write(`data: ${JSON.stringify({
              type: 'token',
              token: char,
              operationId: timer.operationId,
              timestamp: new Date().toISOString()
            })}\n\n`);
          }
        }
      });

      pythonProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      pythonProcess.on('close', (code) => {
        timer.endStage('python_execution');

        if (streamingStarted) {
          timer.endStage('token_streaming');
        }

        timer.startStage('result_parsing');

        if (code !== 0) {
          timer.failStage('result_parsing', `Python script failed with code ${code}: ${errorOutput}`);
          logger.error('Python streaming script execution failed', {
            operationId: timer.operationId,
            exitCode: code,
            errorOutput,
            scriptPath
          });
          reject(new Error(`Python script failed with code ${code}: ${errorOutput}`));
          return;
        }

        try {
          // Extract JSON result after the separator
          let jsonOutput = output;
          const jsonStartIndex = output.indexOf('---JSON_RESULT---');

          if (jsonStartIndex !== -1) {
            // Extract only the JSON part after the separator
            jsonOutput = output.substring(jsonStartIndex + '---JSON_RESULT---'.length).trim();
          }

          // Try to parse JSON result
          let jsonResult = null;

          try {
            jsonResult = JSON.parse(jsonOutput);
          } catch (e) {
            // Fallback: try to find JSON at the end of output (old behavior)
            const lines = output.trim().split('\n');
            for (let i = lines.length - 1; i >= 0; i--) {
              try {
                jsonResult = JSON.parse(lines[i]);
                break;
              } catch (e) {
                continue;
              }
            }
          }

          if (!jsonResult) {
            throw new Error('No valid JSON result found in output');
          }

          timer.endStage('result_parsing', {
            success: true,
            hasAnswer: !!jsonResult.answer,
            hasStreaming: !!jsonResult.streaming
          });

          // Send the final result
          res.write(`data: ${JSON.stringify({
            type: 'result',
            result: jsonResult,
            operationId: timer.operationId,
            timestamp: new Date().toISOString()
          })}\n\n`);

          timer.complete(jsonResult);
          resolve(jsonResult);

        } catch (parseError) {
          timer.failStage('result_parsing', parseError);
          logger.error('Failed to parse Python streaming script output', {
            operationId: timer.operationId,
            parseError: parseError.message,
            output: output.substring(0, 500)
          });
          reject(new Error(`Failed to parse Python script output: ${parseError.message}`));
        }
      });

      // Handle process errors
      pythonProcess.on('error', (error) => {
        timer.failStage('process_spawn', error);
        logger.error('Python streaming process spawn error', {
          operationId: timer.operationId,
          error: error.message
        });
        reject(new Error(`Failed to spawn Python process: ${error.message}`));
      });
    });
  }

  // Legacy function for backward compatibility
  async function callPythonQAPipeline(question, history = []) {
    return new Promise((resolve, reject) => {
      // Use the simple Python script approach
      const scriptPath = path.resolve('..', 'graphiti_standard_search.py');
      
      const scriptArgs = [
        scriptPath,
        question,
        '--uri', process.env.NEO4J_URI,
        '--user', process.env.NEO4J_USERNAME,
        '--password', process.env.NEO4J_PASSWORD,
        '--database', process.env.NEO4J_DATABASE || 'neo4j'
      ];

      // Execute the Python script with the question and connection details as arguments
      const pythonProcess = spawn('python3', scriptArgs, {
        cwd: path.resolve('..'),
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
            ...process.env, // Inherit parent process environment for other things like PATH
            GOOGLE_API_KEY: process.env.GOOGLE_API_KEY,
        }
      });

      let output = '';
      let errorOutput = '';

      pythonProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      pythonProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      pythonProcess.on('close', (code) => {
        if (code !== 0) {
          console.error('Python script error:', errorOutput);
          reject(new Error(`Python script failed with code ${code}: ${errorOutput}`));
          return;
        }

        try {
          // Parse the JSON output
          const result = JSON.parse(output.trim());
          if (result.error) {
            reject(new Error(result.error));
          } else {
            // Return the complete result object instead of just the answer
            resolve({
              answer: result.answer || 'I could not generate a response.',
              source_documents: result.source_documents || [],
              source_nodes: result.source_nodes || [],
              kg_success: result.kg_success,
              documents_found: result.documents_found,
              llm_used: result.llm_used
            });
          }
        } catch (parseError) {
          console.error('Failed to parse Python output:', output);
          reject(new Error('Failed to parse response from knowledge graph system'));
        }
      });

      // Set a timeout for the Python process (increased for LLM processing)
      setTimeout(() => {
        pythonProcess.kill();
        reject(new Error('Python QA pipeline timed out'));
      }, 120000); // 2 minute timeout for enhanced Graphiti + DeepSeek processing
    });
  }

  // --- New Conversation Management Endpoints ---

  /**
   * GET /api/chat/conversations
   * List all saved conversations
   */
  router.get('/conversations', async (req, res, next) => {
    try {
      const files = await fs.readdir(HISTORY_DIR);
      const conversationFiles = files.filter(file => file.endsWith('.json'));

      const conversations = await Promise.all(
        conversationFiles.map(async (file) => {
          const filePath = path.join(HISTORY_DIR, file);
          const data = await fs.readFile(filePath, 'utf8');
          const conversation = JSON.parse(data);
          return {
            id: conversation.id,
            name: conversation.name || `Conversation from ${new Date(conversation.createdAt).toLocaleString()}`,
            createdAt: conversation.createdAt,
            messageCount: conversation.history.length,
          };
        })
      );

      // Sort by most recently created
      conversations.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      res.json(conversations);
    } catch (error) {
      next(error);
    }
  });

  /**
   * POST /api/chat/conversations
   * Create a new conversation
   */
  router.post('/conversations', async (req, res, next) => {
    try {
      const { name } = req.body;
      const id = generateId();
      const createdAt = new Date().toISOString();
      const newConversation = {
        id,
        name: name || `Conversation from ${new Date(createdAt).toLocaleString()}`,
        createdAt,
        history: [{
          role: 'assistant',
          content: 'Welcome! How can I help you with the knowledge graph today?',
          timestamp: createdAt,
        }],
      };

      const filePath = path.join(HISTORY_DIR, `${id}.json`);
      await fs.writeFile(filePath, JSON.stringify(newConversation, null, 2));

      res.status(201).json(newConversation);
    } catch (error) {
      next(error);
    }
  });

  /**
   * GET /api/chat/conversations/:id
   * Retrieve a specific conversation
   */
  router.get('/conversations/:id', async (req, res, next) => {
    try {
      const { id } = req.params;
      const filePath = path.join(HISTORY_DIR, `${id}.json`);
      const data = await fs.readFile(filePath, 'utf8');
      const conversation = JSON.parse(data);
      res.json(conversation);
    } catch (error) {
      if (error.code === 'ENOENT') {
        return res.status(404).json({ error: 'Conversation not found' });
      }
      next(error);
    }
  });

  /**
   * PUT /api/chat/conversations/:id
   * Update (save) a conversation's history
   */
  router.put('/conversations/:id', async (req, res, next) => {
    try {
      const { id } = req.params;
      const { history, name } = req.body;

      // Validate that at least one field is being updated
      if (!history && !name) {
        return res.status(400).json({ error: 'History or name is required for an update.' });
      }

      const filePath = path.join(HISTORY_DIR, `${id}.json`);
      const data = await fs.readFile(filePath, 'utf8');
      const conversation = JSON.parse(data);

      // Update fields if provided
      if (history && Array.isArray(history)) {
        conversation.history = history;
      }
      if (name) {
        conversation.name = name;
      }
      
      conversation.updatedAt = new Date().toISOString();

      await fs.writeFile(filePath, JSON.stringify(conversation, null, 2));

      res.json({ id, message: 'Conversation updated successfully' });
    } catch (error) {
      if (error.code === 'ENOENT') {
        return res.status(404).json({ error: 'Conversation not found' });
      }
      next(error);
    }
  });
  
  /**
   * DELETE /api/chat/conversations/:id
   * Delete a conversation
   */
  router.delete('/conversations/:id', async (req, res, next) => {
    try {
      const { id } = req.params;
      const filePath = path.join(HISTORY_DIR, `${id}.json`);
      await fs.unlink(filePath);
      res.status(200).json({ message: 'Conversation deleted successfully' });
    } catch (error) {
      if (error.code === 'ENOENT') {
        return res.status(404).json({ error: 'Conversation not found' });
      }
      next(error);
    }
  });

  // GET /api/chat/performance
  // Returns Neo4j and system performance statistics
  router.get('/performance', async (req, res) => {
    try {
      const timer = new TimingTracker();
      timer.addMetadata({
        endpoint: '/api/chat/performance',
        method: 'GET'
      });

      timer.startStage('performance_collection');

      // Get Neo4j performance stats
      const neo4jStats = neo4jService.getPerformanceStats();

      // Test Neo4j connectivity
      const connectionTest = await neo4jService.testConnection(timer.operationId);

      // Get system performance
      const systemStats = {
        memory: PerformanceMonitor.getMemoryUsage(),
        system: PerformanceMonitor.getSystemLoad(),
        timestamp: new Date().toISOString()
      };

      timer.endStage('performance_collection');
      const summary = timer.complete();

      res.json({
        neo4j: {
          ...neo4jStats,
          connectivity: connectionTest
        },
        system: systemStats,
        timing: summary
      });

    } catch (error) {
      logger.error('Failed to get performance stats', {
        error: error.message,
        stack: error.stack
      });

      res.status(500).json({
        error: 'Failed to retrieve performance statistics',
        message: error.message
      });
    }
  });

  // POST /api/chat/performance/reset
  // Reset Neo4j performance statistics
  router.post('/performance/reset', (req, res) => {
    try {
      neo4jService.resetStats();

      res.json({
        success: true,
        message: 'Performance statistics reset successfully',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Failed to reset performance stats', {
        error: error.message
      });

      res.status(500).json({
        error: 'Failed to reset performance statistics',
        message: error.message
      });
    }
  });

  // Deprecating old history endpoints
  router.get('/history', (req, res) => {
    res.status(410).json({ error: 'This endpoint is deprecated. Please use /api/chat/conversations.' });
  });

  router.delete('/history', (req, res) => {
    res.status(410).json({ error: 'This endpoint is deprecated. Please use /api/chat/conversations.' });
  });

  return router;
}; 