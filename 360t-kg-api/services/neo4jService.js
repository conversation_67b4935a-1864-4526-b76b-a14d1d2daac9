const { Neo4jQueryTimer } = require('../utils/timing');
const logger = require('../utils/logger');

/**
 * Neo4j Service with comprehensive timing and monitoring
 * Wraps Neo4j driver operations with detailed performance tracking
 */
class Neo4jService {
    constructor(driver, database = 'neo4j') {
        this.driver = driver;
        this.database = database;
        this.queryStats = {
            totalQueries: 0,
            totalDuration: 0,
            slowQueries: [],
            recentQueries: []
        };
    }

    /**
     * Execute a read query with timing
     * @param {string} query - Cypher query
     * @param {object} parameters - Query parameters
     * @param {string} operationId - Parent operation ID for correlation
     */
    async executeRead(query, parameters = {}, operationId = null) {
        const timer = new Neo4jQueryTimer(operationId);
        const session = this.driver.session({ database: this.database });

        try {
            const result = await timer.timeQuery(query, parameters, async () => {
                return await session.executeRead(tx => tx.run(query, parameters));
            });

            this._updateStats(timer.getSummary());
            return result;
        } finally {
            await session.close();
        }
    }

    /**
     * Execute a write query with timing
     * @param {string} query - Cypher query
     * @param {object} parameters - Query parameters
     * @param {string} operationId - Parent operation ID for correlation
     */
    async executeWrite(query, parameters = {}, operationId = null) {
        const timer = new Neo4jQueryTimer(operationId);
        const session = this.driver.session({ database: this.database });

        try {
            const result = await timer.timeQuery(query, parameters, async () => {
                return await session.executeWrite(tx => tx.run(query, parameters));
            });

            this._updateStats(timer.getSummary());
            return result;
        } finally {
            await session.close();
        }
    }

    /**
     * Execute multiple queries in a transaction with timing
     * @param {Array} queries - Array of {query, parameters} objects
     * @param {string} operationId - Parent operation ID for correlation
     * @param {boolean} isWrite - Whether this is a write transaction
     */
    async executeTransaction(queries, operationId = null, isWrite = false) {
        const timer = new Neo4jQueryTimer(operationId);
        const session = this.driver.session({ database: this.database });

        try {
            const results = [];
            
            const transactionFunction = async (tx) => {
                for (const { query, parameters = {} } of queries) {
                    const result = await timer.timeQuery(query, parameters, async () => {
                        return await tx.run(query, parameters);
                    });
                    results.push(result);
                }
                return results;
            };

            const finalResults = isWrite 
                ? await session.executeWrite(transactionFunction)
                : await session.executeRead(transactionFunction);

            this._updateStats(timer.getSummary());
            return finalResults;
        } finally {
            await session.close();
        }
    }

    /**
     * Get database statistics with timing
     * @param {string} operationId - Parent operation ID for correlation
     */
    async getDatabaseStats(operationId = null) {
        const queries = [
            {
                name: 'nodeCount',
                query: 'MATCH (n) RETURN count(n) as count',
                parameters: {}
            },
            {
                name: 'relationshipCount', 
                query: 'MATCH ()-[r]->() RETURN count(r) as count',
                parameters: {}
            },
            {
                name: 'labelStats',
                query: 'CALL db.labels() YIELD label RETURN label',
                parameters: {}
            },
            {
                name: 'relationshipTypes',
                query: 'CALL db.relationshipTypes() YIELD relationshipType RETURN relationshipType',
                parameters: {}
            }
        ];

        const timer = new Neo4jQueryTimer(operationId);
        const session = this.driver.session({ database: this.database });
        const stats = {};

        try {
            for (const { name, query, parameters } of queries) {
                const result = await timer.timeQuery(query, parameters, async () => {
                    return await session.executeRead(tx => tx.run(query, parameters));
                });
                
                if (name === 'nodeCount' || name === 'relationshipCount') {
                    stats[name] = result.records[0]?.get('count')?.toNumber() || 0;
                } else {
                    stats[name] = result.records.map(record => 
                        record.get(name === 'labelStats' ? 'label' : 'relationshipType')
                    );
                }
            }

            const summary = timer.getSummary();
            this._updateStats(summary);

            return {
                stats,
                timing: summary
            };
        } finally {
            await session.close();
        }
    }

    /**
     * Test database connectivity with timing
     * @param {string} operationId - Parent operation ID for correlation
     */
    async testConnection(operationId = null) {
        const timer = new Neo4jQueryTimer(operationId);
        
        try {
            await timer.timeQuery('RETURN 1 as test', {}, async () => {
                return await this.driver.verifyConnectivity();
            });

            const summary = timer.getSummary();
            this._updateStats(summary);

            return {
                connected: true,
                timing: summary
            };
        } catch (error) {
            logger.error('Neo4j connection test failed', {
                operationId,
                error: error.message
            });
            
            return {
                connected: false,
                error: error.message,
                timing: timer.getSummary()
            };
        }
    }

    /**
     * Get performance statistics
     */
    getPerformanceStats() {
        const avgDuration = this.queryStats.totalQueries > 0 
            ? this.queryStats.totalDuration / this.queryStats.totalQueries 
            : 0;

        return {
            totalQueries: this.queryStats.totalQueries,
            averageQueryTime: Math.round(avgDuration),
            slowQueriesCount: this.queryStats.slowQueries.length,
            recentQueries: this.queryStats.recentQueries.slice(-10), // Last 10 queries
            slowQueries: this.queryStats.slowQueries.slice(-5) // Last 5 slow queries
        };
    }

    /**
     * Update internal statistics
     * @private
     */
    _updateStats(summary) {
        this.queryStats.totalQueries += summary.queryCount;
        this.queryStats.totalDuration += summary.totalQueryTime;

        // Track recent queries
        this.queryStats.recentQueries.push({
            timestamp: new Date().toISOString(),
            operationId: summary.operationId,
            queryCount: summary.queryCount,
            totalDuration: summary.totalQueryTime,
            averageDuration: summary.averageQueryTime
        });

        // Keep only last 50 recent queries
        if (this.queryStats.recentQueries.length > 50) {
            this.queryStats.recentQueries = this.queryStats.recentQueries.slice(-50);
        }

        // Track slow queries (>1000ms)
        const slowQueries = summary.queries.filter(q => q.duration > 1000);
        if (slowQueries.length > 0) {
            this.queryStats.slowQueries.push({
                timestamp: new Date().toISOString(),
                operationId: summary.operationId,
                queries: slowQueries
            });

            // Keep only last 20 slow query records
            if (this.queryStats.slowQueries.length > 20) {
                this.queryStats.slowQueries = this.queryStats.slowQueries.slice(-20);
            }
        }
    }

    /**
     * Reset performance statistics
     */
    resetStats() {
        this.queryStats = {
            totalQueries: 0,
            totalDuration: 0,
            slowQueries: [],
            recentQueries: []
        };
    }
}

module.exports = Neo4jService;
