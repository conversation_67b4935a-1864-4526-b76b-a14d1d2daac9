#!/bin/bash
set -e

# Emergency Rollback Script
# Provides immediate recovery from failed migrations or breaking changes

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_emergency() {
    echo -e "${RED}🚨 EMERGENCY: $1${NC}"
}

# Function to show usage
show_usage() {
    cat << EOF
🚨 Emergency Rollback Script

Usage: $0 [OPTIONS]

Options:
    --backup-dir DIR    Specify backup directory to restore from
    --service SERVICE   Rollback specific service only (api|ui|proxy|database|all)
    --dry-run          Show what would be done without executing
    --force            Skip confirmation prompts
    --help             Show this help message

Examples:
    $0                                    # Full system rollback from latest backup
    $0 --service api                      # Rollback API service only
    $0 --backup-dir backups/20231201_120000  # Rollback from specific backup
    $0 --dry-run                          # Preview rollback actions

Emergency Contact:
    If this script fails, manually restore from: backups/latest/
EOF
}

# Function to validate backup directory
validate_backup() {
    local backup_dir="$1"
    
    if [ ! -d "$backup_dir" ]; then
        log_error "Backup directory not found: $backup_dir"
        return 1
    fi
    
    if [ ! -f "$backup_dir/backup-manifest.txt" ]; then
        log_error "Backup manifest not found in: $backup_dir"
        return 1
    fi
    
    log_success "Backup directory validated: $backup_dir"
    return 0
}

# Function to stop all services
stop_services() {
    local service="$1"
    
    log_info "Stopping services..."
    
    case "$service" in
        "all"|"")
            docker-compose down 2>/dev/null || log_warning "Docker compose not running"
            ;;
        "api")
            docker-compose stop api 2>/dev/null || log_warning "API service not running"
            ;;
        "ui")
            # UI is typically a dev server, try to kill it
            pkill -f "vite" 2>/dev/null || log_warning "UI dev server not running"
            ;;
        "proxy")
            docker-compose stop proxy 2>/dev/null || log_warning "Proxy service not running"
            ;;
        "database")
            docker-compose stop neo4j 2>/dev/null || log_warning "Database service not running"
            ;;
        *)
            log_error "Unknown service: $service"
            return 1
            ;;
    esac
    
    log_success "Services stopped"
}

# Function to restore configuration files
restore_configuration() {
    local backup_dir="$1"
    local service="$2"
    
    log_info "Restoring configuration files..."
    
    case "$service" in
        "all"|"")
            # Restore root configuration files
            cp "$backup_dir"/.env* . 2>/dev/null || log_warning "No root .env files to restore"
            cp "$backup_dir"/docker-compose.yml . 2>/dev/null || log_warning "No docker-compose.yml to restore"
            cp "$backup_dir"/package.json . 2>/dev/null || log_warning "No package.json to restore"
            cp "$backup_dir"/requirements.txt . 2>/dev/null || log_warning "No requirements.txt to restore"
            
            # Restore service configurations
            if [ -d "360t-kg-api" ]; then
                cp "$backup_dir"/.env* 360t-kg-api/ 2>/dev/null || log_warning "No API .env files to restore"
            fi
            if [ -d "360t-kg-ui" ]; then
                cp "$backup_dir"/.env* 360t-kg-ui/ 2>/dev/null || log_warning "No UI .env files to restore"
            fi
            if [ -d "proxy-server" ]; then
                cp "$backup_dir"/.env* proxy-server/ 2>/dev/null || log_warning "No proxy .env files to restore"
            fi
            
            # Restore config directory if it exists in backup
            if [ -d "$backup_dir/config" ]; then
                log_info "Restoring config directory..."
                rm -rf config/
                cp -r "$backup_dir/config" .
            fi
            ;;
        "api")
            if [ -d "360t-kg-api" ]; then
                cp "$backup_dir"/.env* 360t-kg-api/ 2>/dev/null || log_warning "No API .env files to restore"
            fi
            ;;
        "ui")
            if [ -d "360t-kg-ui" ]; then
                cp "$backup_dir"/.env* 360t-kg-ui/ 2>/dev/null || log_warning "No UI .env files to restore"
            fi
            ;;
        "proxy")
            if [ -d "proxy-server" ]; then
                cp "$backup_dir"/.env* proxy-server/ 2>/dev/null || log_warning "No proxy .env files to restore"
            fi
            ;;
        "database")
            # Database configuration is typically in root .env
            cp "$backup_dir"/.env* . 2>/dev/null || log_warning "No .env files to restore"
            ;;
    esac
    
    log_success "Configuration files restored"
}

# Function to restore database
restore_database() {
    local backup_dir="$1"
    
    if [ ! -f "$backup_dir/neo4j.dump" ]; then
        log_warning "No database dump found in backup, skipping database restore"
        return 0
    fi
    
    log_info "Restoring database..."
    
    # Start Neo4j service
    docker-compose up -d neo4j
    
    # Wait for Neo4j to be ready
    log_info "Waiting for Neo4j to be ready..."
    local retries=0
    local max_retries=30
    
    while [ $retries -lt $max_retries ]; do
        if docker exec kg_neo4j cypher-shell -u neo4j -p development_password "RETURN 1" >/dev/null 2>&1; then
            break
        fi
        sleep 2
        retries=$((retries + 1))
    done
    
    if [ $retries -eq $max_retries ]; then
        log_error "Neo4j failed to start within timeout"
        return 1
    fi
    
    # Copy dump to container and restore
    docker cp "$backup_dir/neo4j.dump" kg_neo4j:/var/lib/neo4j/
    docker exec kg_neo4j neo4j-admin database load neo4j --from-path=/var/lib/neo4j/ --overwrite-destination=true 2>/dev/null || {
        log_error "Database restore failed"
        return 1
    }
    
    # Restart Neo4j
    docker-compose restart neo4j
    
    log_success "Database restored successfully"
}

# Function to start services
start_services() {
    local service="$1"
    
    log_info "Starting services..."
    
    case "$service" in
        "all"|"")
            docker-compose up -d
            ;;
        "api")
            docker-compose up -d api
            ;;
        "ui")
            log_info "UI service needs to be started manually with: cd 360t-kg-ui && npm run dev"
            ;;
        "proxy")
            docker-compose up -d proxy
            ;;
        "database")
            docker-compose up -d neo4j
            ;;
    esac
    
    log_success "Services started"
}

# Function to validate rollback
validate_rollback() {
    local service="$1"
    
    log_info "Validating rollback..."
    
    # Wait for services to start
    sleep 10
    
    # Run health checks
    if [ -f "$SCRIPT_DIR/health-check.cjs" ]; then
        log_info "Running health checks..."
        if node "$SCRIPT_DIR/health-check.cjs" >/dev/null 2>&1; then
            log_success "Health checks passed"
        else
            log_warning "Some health checks failed, but rollback completed"
        fi
    else
        log_warning "Health check script not found, skipping validation"
    fi
    
    log_success "Rollback validation completed"
}

# Function to create pre-rollback backup
create_pre_rollback_backup() {
    log_info "Creating pre-rollback backup..."
    
    if [ -f "$SCRIPT_DIR/backup-system.sh" ]; then
        "$SCRIPT_DIR/backup-system.sh" >/dev/null 2>&1 || {
            log_warning "Failed to create pre-rollback backup"
            return 1
        }
        
        # Rename the latest backup to indicate it's a pre-rollback backup
        local latest_backup=$(ls -t backups/ | grep -E '^[0-9]{8}_[0-9]{6}$' | head -n1)
        if [ -n "$latest_backup" ]; then
            mv "backups/$latest_backup" "backups/pre-rollback-$TIMESTAMP"
            log_success "Pre-rollback backup created: backups/pre-rollback-$TIMESTAMP"
        fi
    else
        log_warning "Backup script not found, skipping pre-rollback backup"
    fi
}

# Main rollback function
perform_rollback() {
    local backup_dir="$1"
    local service="$2"
    local dry_run="$3"
    local force="$4"
    
    log_emergency "EMERGENCY ROLLBACK INITIATED"
    log_info "Timestamp: $(date)"
    log_info "Backup source: $backup_dir"
    log_info "Service scope: ${service:-all}"
    
    if [ "$dry_run" = "true" ]; then
        log_info "DRY RUN MODE - No changes will be made"
        log_info "Would perform the following actions:"
        log_info "1. Stop services: ${service:-all}"
        log_info "2. Create pre-rollback backup"
        log_info "3. Restore configuration from: $backup_dir"
        if [ "$service" = "all" ] || [ "$service" = "database" ] || [ -z "$service" ]; then
            log_info "4. Restore database from: $backup_dir/neo4j.dump"
        fi
        log_info "5. Start services: ${service:-all}"
        log_info "6. Validate rollback"
        return 0
    fi
    
    if [ "$force" != "true" ]; then
        echo
        log_warning "This will restore the system from backup: $backup_dir"
        log_warning "Current state will be backed up before rollback"
        echo
        read -p "Are you sure you want to proceed? (yes/no): " confirm
        if [ "$confirm" != "yes" ]; then
            log_info "Rollback cancelled by user"
            exit 0
        fi
    fi
    
    # Validate backup before proceeding
    validate_backup "$backup_dir" || exit 1
    
    # Create pre-rollback backup
    create_pre_rollback_backup
    
    # Stop services
    stop_services "$service"
    
    # Restore configuration
    restore_configuration "$backup_dir" "$service"
    
    # Restore database if needed
    if [ "$service" = "all" ] || [ "$service" = "database" ] || [ -z "$service" ]; then
        restore_database "$backup_dir"
    fi
    
    # Start services
    start_services "$service"
    
    # Validate rollback
    validate_rollback "$service"
    
    log_success "EMERGENCY ROLLBACK COMPLETED SUCCESSFULLY"
    log_info "Pre-rollback state saved to: backups/pre-rollback-$TIMESTAMP"
    log_info "System restored from: $backup_dir"
}

# Parse command line arguments
BACKUP_DIR=""
SERVICE=""
DRY_RUN="false"
FORCE="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        --backup-dir)
            BACKUP_DIR="$2"
            shift 2
            ;;
        --service)
            SERVICE="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN="true"
            shift
            ;;
        --force)
            FORCE="true"
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Set default backup directory if not specified
if [ -z "$BACKUP_DIR" ]; then
    if [ -L "backups/latest" ]; then
        BACKUP_DIR="backups/$(readlink backups/latest)"
    else
        # Find the most recent backup
        LATEST_BACKUP=$(ls -t backups/ | grep -E '^[0-9]{8}_[0-9]{6}$' | head -n1)
        if [ -n "$LATEST_BACKUP" ]; then
            BACKUP_DIR="backups/$LATEST_BACKUP"
        else
            log_error "No backup directory found. Please specify --backup-dir"
            exit 1
        fi
    fi
fi

# Change to project root
cd "$PROJECT_ROOT"

# Perform rollback
perform_rollback "$BACKUP_DIR" "$SERVICE" "$DRY_RUN" "$FORCE"
