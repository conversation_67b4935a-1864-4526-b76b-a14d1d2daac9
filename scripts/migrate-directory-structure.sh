#!/bin/bash
set -e

# Directory Structure Migration Script
# Migrates from current structure to new organized structure

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to show usage
show_usage() {
    cat << EOF
📁 Directory Structure Migration Script

Usage: $0 [OPTIONS]

Options:
    --dry-run          Show what would be done without executing
    --backup           Create backup before migration (default: true)
    --force            Skip confirmation prompts
    --phase PHASE      Migration phase (1|2|3|all)
    --help             Show this help message

Migration Phases:
    Phase 1: Create new directory structure
    Phase 2: Copy files to new structure (parallel)
    Phase 3: Update import paths and references

Examples:
    $0                                    # Full migration with backup
    $0 --dry-run                          # Preview migration changes
    $0 --phase 1                          # Create directory structure only
    $0 --phase 2 --force                  # Copy files without prompts

This script migrates the project to a more organized directory structure.
EOF
}

# Function to create new directory structure
create_directory_structure() {
    local dry_run="$1"
    
    log_info "Creating new directory structure..."
    
    local directories=(
        "src"
        "src/components"
        "src/components/graph"
        "src/components/analysis"
        "src/components/chat"
        "src/components/settings"
        "src/components/layout"
        "src/components/common"
        "src/services"
        "src/services/api"
        "src/services/graph"
        "src/services/analysis"
        "src/services/chat"
        "src/services/storage"
        "src/utils"
        "src/types"
        "src/hooks"
        "src/contexts"
        "src/assets"
        "src/assets/images"
        "src/assets/styles"
        "src/assets/icons"
        "lib"
        "lib/shared"
        "lib/config"
        "lib/api"
        "lib/utils"
        "docs"
        "docs/api"
        "docs/architecture"
        "docs/guides"
        "tools"
        "tools/scripts"
        "tools/generators"
        "tools/validators"
    )
    
    if [ "$dry_run" = "true" ]; then
        log_info "Would create the following directories:"
        for dir in "${directories[@]}"; do
            log_info "  - $dir"
        done
        return 0
    fi
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_success "Created directory: $dir"
        else
            log_info "Directory already exists: $dir"
        fi
    done
    
    log_success "Directory structure created successfully"
}

# Function to analyze current structure
analyze_current_structure() {
    log_info "Analyzing current directory structure..."
    
    local file_count=0
    local component_files=0
    local service_files=0
    local config_files=0
    
    # Count files in 360t-kg-ui
    if [ -d "360t-kg-ui/src" ]; then
        component_files=$(find 360t-kg-ui/src -name "*.jsx" -o -name "*.tsx" -o -name "*.js" -o -name "*.ts" | wc -l)
        log_info "Found $component_files component files in 360t-kg-ui/src"
    fi
    
    # Count files in 360t-kg-api
    if [ -d "360t-kg-api/src" ]; then
        service_files=$(find 360t-kg-api/src -name "*.py" -o -name "*.js" -o -name "*.ts" | wc -l)
        log_info "Found $service_files service files in 360t-kg-api/src"
    fi
    
    # Count configuration files
    config_files=$(find . -maxdepth 2 -name "*.env*" -o -name "*.json" -o -name "*.yml" -o -name "*.yaml" | grep -v node_modules | wc -l)
    log_info "Found $config_files configuration files"
    
    # Count shared files
    if [ -d "shared" ]; then
        local shared_files=$(find shared -name "*.js" -o -name "*.ts" -o -name "*.py" | wc -l)
        log_info "Found $shared_files shared files"
    fi
    
    log_success "Current structure analysis completed"
}

# Function to create migration mapping
create_migration_mapping() {
    local dry_run="$1"
    
    log_info "Creating migration mapping..."
    
    if [ "$dry_run" = "true" ]; then
        log_info "Would create migration mapping file: migration-mapping.json"
        return 0
    fi
    
    cat > migration-mapping.json << 'EOF'
{
  "migration": {
    "version": "1.0.0",
    "timestamp": "",
    "mappings": {
      "components": {
        "source": "360t-kg-ui/src",
        "target": "src/components",
        "patterns": [
          {
            "from": "360t-kg-ui/src/components/Graph*.jsx",
            "to": "src/components/graph/"
          },
          {
            "from": "360t-kg-ui/src/components/Analysis*.jsx",
            "to": "src/components/analysis/"
          },
          {
            "from": "360t-kg-ui/src/components/Chat*.jsx",
            "to": "src/components/chat/"
          },
          {
            "from": "360t-kg-ui/src/components/Settings*.jsx",
            "to": "src/components/settings/"
          },
          {
            "from": "360t-kg-ui/src/components/Layout*.jsx",
            "to": "src/components/layout/"
          },
          {
            "from": "360t-kg-ui/src/components/*.jsx",
            "to": "src/components/common/"
          }
        ]
      },
      "services": {
        "source": "360t-kg-api/src",
        "target": "src/services",
        "patterns": [
          {
            "from": "360t-kg-api/src/graph*.py",
            "to": "src/services/graph/"
          },
          {
            "from": "360t-kg-api/src/analysis*.py",
            "to": "src/services/analysis/"
          },
          {
            "from": "360t-kg-api/src/chat*.py",
            "to": "src/services/chat/"
          },
          {
            "from": "360t-kg-api/src/api*.py",
            "to": "src/services/api/"
          }
        ]
      },
      "shared": {
        "source": "shared",
        "target": "lib/shared",
        "patterns": [
          {
            "from": "shared/config/*",
            "to": "lib/config/"
          },
          {
            "from": "shared/api/*",
            "to": "lib/api/"
          },
          {
            "from": "shared/utils/*",
            "to": "lib/utils/"
          }
        ]
      },
      "configuration": {
        "patterns": [
          {
            "from": ".env*",
            "to": "config/"
          },
          {
            "from": "docker-compose*.yml",
            "to": "config/"
          },
          {
            "from": "*.config.js",
            "to": "config/"
          }
        ]
      }
    }
  }
}
EOF
    
    # Update timestamp
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    sed -i.bak "s/\"timestamp\": \"\"/\"timestamp\": \"$timestamp\"/" migration-mapping.json
    rm -f migration-mapping.json.bak
    
    log_success "Migration mapping created: migration-mapping.json"
}

# Function to copy files to new structure
copy_files_parallel() {
    local dry_run="$1"
    
    log_info "Copying files to new structure (parallel mode)..."
    
    if [ "$dry_run" = "true" ]; then
        log_info "Would copy files according to migration mapping"
        log_info "Files would be copied, not moved (parallel mode)"
        return 0
    fi
    
    # Copy UI components
    if [ -d "360t-kg-ui/src" ]; then
        log_info "Copying UI components..."
        
        # Copy all component files to common first
        if [ -d "360t-kg-ui/src/components" ]; then
            cp -r 360t-kg-ui/src/components/* src/components/common/ 2>/dev/null || true
        fi
        
        # Copy specific component types to their directories
        find 360t-kg-ui/src -name "*Graph*" -type f -exec cp {} src/components/graph/ \; 2>/dev/null || true
        find 360t-kg-ui/src -name "*Analysis*" -type f -exec cp {} src/components/analysis/ \; 2>/dev/null || true
        find 360t-kg-ui/src -name "*Chat*" -type f -exec cp {} src/components/chat/ \; 2>/dev/null || true
        find 360t-kg-ui/src -name "*Settings*" -type f -exec cp {} src/components/settings/ \; 2>/dev/null || true
        find 360t-kg-ui/src -name "*Layout*" -type f -exec cp {} src/components/layout/ \; 2>/dev/null || true
        
        # Copy other UI files
        if [ -d "360t-kg-ui/src/hooks" ]; then
            cp -r 360t-kg-ui/src/hooks/* src/hooks/ 2>/dev/null || true
        fi
        
        if [ -d "360t-kg-ui/src/utils" ]; then
            cp -r 360t-kg-ui/src/utils/* src/utils/ 2>/dev/null || true
        fi
        
        if [ -d "360t-kg-ui/src/types" ]; then
            cp -r 360t-kg-ui/src/types/* src/types/ 2>/dev/null || true
        fi
        
        log_success "UI components copied"
    fi
    
    # Copy API services
    if [ -d "360t-kg-api/src" ]; then
        log_info "Copying API services..."
        
        find 360t-kg-api/src -name "*graph*" -type f -exec cp {} src/services/graph/ \; 2>/dev/null || true
        find 360t-kg-api/src -name "*analysis*" -type f -exec cp {} src/services/analysis/ \; 2>/dev/null || true
        find 360t-kg-api/src -name "*chat*" -type f -exec cp {} src/services/chat/ \; 2>/dev/null || true
        find 360t-kg-api/src -name "*api*" -type f -exec cp {} src/services/api/ \; 2>/dev/null || true
        
        log_success "API services copied"
    fi
    
    # Copy shared files
    if [ -d "shared" ]; then
        log_info "Copying shared files..."
        
        cp -r shared/* lib/shared/ 2>/dev/null || true
        
        # Copy specific shared modules to lib
        if [ -d "shared/config" ]; then
            cp -r shared/config/* lib/config/ 2>/dev/null || true
        fi
        
        if [ -d "shared/api" ]; then
            cp -r shared/api/* lib/api/ 2>/dev/null || true
        fi
        
        log_success "Shared files copied"
    fi
    
    log_success "File copying completed (parallel mode)"
}

# Function to validate new structure
validate_new_structure() {
    log_info "Validating new directory structure..."
    
    local validation_passed=true
    
    # Check if key directories exist
    local required_dirs=(
        "src/components"
        "src/services"
        "src/utils"
        "lib/shared"
        "lib/config"
        "lib/api"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            log_error "Required directory missing: $dir"
            validation_passed=false
        else
            local file_count=$(find "$dir" -type f | wc -l)
            log_info "Directory $dir contains $file_count files"
        fi
    done
    
    # Check for duplicate files
    log_info "Checking for potential conflicts..."
    
    if [ -d "src/components/common" ] && [ -d "src/components/graph" ]; then
        local common_files=$(ls src/components/common/ 2>/dev/null | wc -l)
        local graph_files=$(ls src/components/graph/ 2>/dev/null | wc -l)
        
        if [ $common_files -gt 0 ] && [ $graph_files -gt 0 ]; then
            log_warning "Both common and graph components exist - review for duplicates"
        fi
    fi
    
    if [ "$validation_passed" = true ]; then
        log_success "Directory structure validation passed"
        return 0
    else
        log_error "Directory structure validation failed"
        return 1
    fi
}

# Function to generate import path updates
generate_import_updates() {
    local dry_run="$1"
    
    log_info "Generating import path updates..."
    
    if [ "$dry_run" = "true" ]; then
        log_info "Would generate import-updates.json with path mappings"
        return 0
    fi
    
    cat > import-updates.json << 'EOF'
{
  "importUpdates": {
    "version": "1.0.0",
    "timestamp": "",
    "patterns": [
      {
        "from": "import.*from ['\"]../components/",
        "to": "import.*from '@/components/",
        "description": "Update relative component imports to absolute"
      },
      {
        "from": "import.*from ['\"]../services/",
        "to": "import.*from '@/services/",
        "description": "Update relative service imports to absolute"
      },
      {
        "from": "import.*from ['\"]../utils/",
        "to": "import.*from '@/utils/",
        "description": "Update relative utility imports to absolute"
      },
      {
        "from": "import.*from ['\"]shared/",
        "to": "import.*from '@shared/",
        "description": "Update shared imports to use alias"
      }
    ],
    "aliases": {
      "@": "./src",
      "@shared": "./lib/shared",
      "@config": "./lib/config",
      "@api": "./lib/api"
    }
  }
}
EOF
    
    # Update timestamp
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    sed -i.bak "s/\"timestamp\": \"\"/\"timestamp\": \"$timestamp\"/" import-updates.json
    rm -f import-updates.json.bak
    
    log_success "Import updates generated: import-updates.json"
}

# Main migration function
perform_migration() {
    local phase="$1"
    local dry_run="$2"
    local backup="$3"
    local force="$4"
    
    log_info "📁 Starting directory structure migration..."
    log_info "Phase: $phase"
    log_info "Dry run: $dry_run"
    log_info "Backup: $backup"
    
    if [ "$dry_run" != "true" ] && [ "$force" != "true" ]; then
        echo
        log_warning "This will migrate the directory structure"
        log_warning "Files will be copied (not moved) to maintain parallel structure"
        echo
        read -p "Are you sure you want to proceed? (yes/no): " confirm
        if [ "$confirm" != "yes" ]; then
            log_info "Migration cancelled by user"
            return 0
        fi
    fi
    
    # Create backup if requested
    if [ "$backup" = "true" ] && [ "$dry_run" != "true" ]; then
        log_info "Creating backup before migration..."
        ./scripts/backup-system.sh >/dev/null
        log_success "Backup created"
    fi
    
    # Execute migration phases
    case "$phase" in
        "1"|"all")
            analyze_current_structure
            create_directory_structure "$dry_run"
            create_migration_mapping "$dry_run"
            ;;
    esac
    
    case "$phase" in
        "2"|"all")
            copy_files_parallel "$dry_run"
            validate_new_structure
            ;;
    esac
    
    case "$phase" in
        "3"|"all")
            generate_import_updates "$dry_run"
            ;;
    esac
    
    if [ "$dry_run" = "true" ]; then
        log_success "🎉 Migration preview completed successfully"
        log_info "Run without --dry-run to execute the migration"
    else
        log_success "🎉 Directory structure migration completed successfully"
        log_info "Next steps:"
        log_info "1. Review the new directory structure"
        log_info "2. Update import paths using: import-updates.json"
        log_info "3. Test all services with new structure"
        log_info "4. Update build configurations if needed"
    fi
}

# Parse command line arguments
PHASE="all"
DRY_RUN="false"
BACKUP="true"
FORCE="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        --phase)
            PHASE="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN="true"
            shift
            ;;
        --backup)
            BACKUP="true"
            shift
            ;;
        --no-backup)
            BACKUP="false"
            shift
            ;;
        --force)
            FORCE="true"
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate phase
case "$PHASE" in
    1|2|3|all)
        ;;
    *)
        log_error "Invalid phase: $PHASE"
        log_info "Valid phases: 1, 2, 3, all"
        exit 1
        ;;
esac

# Change to project root
cd "$PROJECT_ROOT"

# Perform migration
perform_migration "$PHASE" "$DRY_RUN" "$BACKUP" "$FORCE"
