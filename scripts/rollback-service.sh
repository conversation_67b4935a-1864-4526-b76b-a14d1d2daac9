#!/bin/bash
set -e

# Service-Specific Rollback Script
# Provides targeted rollback for individual services

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to show usage
show_usage() {
    cat << EOF
🔧 Service-Specific Rollback Script

Usage: $0 SERVICE [OPTIONS]

Services:
    api         Rollback API service (360t-kg-api)
    ui          Rollback UI service (360t-kg-ui)
    proxy       Rollback proxy service (proxy-server)
    database    Rollback database service (Neo4j)
    config      Rollback configuration files only

Options:
    --backup-dir DIR    Specify backup directory to restore from
    --dry-run          Show what would be done without executing
    --force            Skip confirmation prompts
    --help             Show this help message

Examples:
    $0 api                                # Rollback API service from latest backup
    $0 database --backup-dir backups/20231201_120000  # Rollback database from specific backup
    $0 config --dry-run                   # Preview configuration rollback

Note: This script performs targeted rollbacks. For full system rollback, use emergency-rollback.sh
EOF
}

# Function to rollback API service
rollback_api() {
    local backup_dir="$1"
    local dry_run="$2"
    
    log_info "Rolling back API service..."
    
    if [ "$dry_run" = "true" ]; then
        log_info "Would perform the following actions:"
        log_info "1. Stop API container"
        log_info "2. Restore API configuration from: $backup_dir"
        log_info "3. Rebuild and restart API container"
        return 0
    fi
    
    # Stop API service
    log_info "Stopping API service..."
    docker-compose stop api 2>/dev/null || log_warning "API service not running"
    
    # Restore API configuration
    if [ -d "360t-kg-api" ] && [ -f "$backup_dir/.env" ]; then
        log_info "Restoring API configuration..."
        cp "$backup_dir"/.env* 360t-kg-api/ 2>/dev/null || log_warning "No API .env files to restore"
    fi
    
    # Restart API service
    log_info "Restarting API service..."
    docker-compose up -d api --build
    
    # Wait and validate
    sleep 10
    if curl -f http://localhost:3002/api/health >/dev/null 2>&1; then
        log_success "API service rollback completed and validated"
    else
        log_warning "API service rollback completed but health check failed"
    fi
}

# Function to rollback UI service
rollback_ui() {
    local backup_dir="$1"
    local dry_run="$2"
    
    log_info "Rolling back UI service..."
    
    if [ "$dry_run" = "true" ]; then
        log_info "Would perform the following actions:"
        log_info "1. Stop UI dev server"
        log_info "2. Restore UI configuration from: $backup_dir"
        log_info "3. Restart UI dev server (manual step required)"
        return 0
    fi
    
    # Stop UI dev server
    log_info "Stopping UI dev server..."
    pkill -f "vite" 2>/dev/null || log_warning "UI dev server not running"
    
    # Restore UI configuration
    if [ -d "360t-kg-ui" ] && [ -f "$backup_dir/.env" ]; then
        log_info "Restoring UI configuration..."
        cp "$backup_dir"/.env* 360t-kg-ui/ 2>/dev/null || log_warning "No UI .env files to restore"
    fi
    
    log_success "UI service rollback completed"
    log_info "To restart UI service, run: cd 360t-kg-ui && npm run dev"
}

# Function to rollback proxy service
rollback_proxy() {
    local backup_dir="$1"
    local dry_run="$2"
    
    log_info "Rolling back proxy service..."
    
    if [ "$dry_run" = "true" ]; then
        log_info "Would perform the following actions:"
        log_info "1. Stop proxy container"
        log_info "2. Restore proxy configuration from: $backup_dir"
        log_info "3. Rebuild and restart proxy container"
        return 0
    fi
    
    # Stop proxy service
    log_info "Stopping proxy service..."
    docker-compose stop proxy 2>/dev/null || log_warning "Proxy service not running"
    
    # Restore proxy configuration
    if [ -d "proxy-server" ] && [ -f "$backup_dir/.env" ]; then
        log_info "Restoring proxy configuration..."
        cp "$backup_dir"/.env* proxy-server/ 2>/dev/null || log_warning "No proxy .env files to restore"
    fi
    
    # Restart proxy service
    log_info "Restarting proxy service..."
    docker-compose up -d proxy --build
    
    # Wait and validate
    sleep 10
    if curl -f http://localhost:3001/health >/dev/null 2>&1; then
        log_success "Proxy service rollback completed and validated"
    else
        log_warning "Proxy service rollback completed but health check failed"
    fi
}

# Function to rollback database
rollback_database() {
    local backup_dir="$1"
    local dry_run="$2"
    
    log_info "Rolling back database service..."
    
    if [ "$dry_run" = "true" ]; then
        log_info "Would perform the following actions:"
        log_info "1. Stop Neo4j container"
        log_info "2. Restore database configuration from: $backup_dir"
        if [ -f "$backup_dir/neo4j.dump" ]; then
            log_info "3. Restore database from dump: $backup_dir/neo4j.dump"
        fi
        log_info "4. Restart Neo4j container"
        return 0
    fi
    
    # Stop database service
    log_info "Stopping database service..."
    docker-compose stop neo4j 2>/dev/null || log_warning "Database service not running"
    
    # Restore database configuration
    log_info "Restoring database configuration..."
    cp "$backup_dir"/.env* . 2>/dev/null || log_warning "No .env files to restore"
    
    # Restore database dump if available
    if [ -f "$backup_dir/neo4j.dump" ]; then
        log_info "Restoring database from dump..."
        
        # Start Neo4j
        docker-compose up -d neo4j
        
        # Wait for Neo4j to be ready
        log_info "Waiting for Neo4j to be ready..."
        local retries=0
        local max_retries=30
        
        while [ $retries -lt $max_retries ]; do
            if docker exec kg_neo4j cypher-shell -u neo4j -p development_password "RETURN 1" >/dev/null 2>&1; then
                break
            fi
            sleep 2
            retries=$((retries + 1))
        done
        
        if [ $retries -eq $max_retries ]; then
            log_error "Neo4j failed to start within timeout"
            return 1
        fi
        
        # Restore database
        docker cp "$backup_dir/neo4j.dump" kg_neo4j:/var/lib/neo4j/
        docker exec kg_neo4j neo4j-admin database load neo4j --from-path=/var/lib/neo4j/ --overwrite-destination=true 2>/dev/null || {
            log_error "Database restore failed"
            return 1
        }
        
        # Restart Neo4j
        docker-compose restart neo4j
    else
        # Just restart with new configuration
        docker-compose up -d neo4j
    fi
    
    # Wait and validate
    sleep 15
    if curl -f http://localhost:7474 >/dev/null 2>&1; then
        log_success "Database service rollback completed and validated"
    else
        log_warning "Database service rollback completed but health check failed"
    fi
}

# Function to rollback configuration only
rollback_config() {
    local backup_dir="$1"
    local dry_run="$2"
    
    log_info "Rolling back configuration files..."
    
    if [ "$dry_run" = "true" ]; then
        log_info "Would perform the following actions:"
        log_info "1. Restore root configuration files from: $backup_dir"
        log_info "2. Restore service configuration files from: $backup_dir"
        log_info "3. Restore config directory from: $backup_dir"
        return 0
    fi
    
    # Restore root configuration files
    log_info "Restoring root configuration files..."
    cp "$backup_dir"/.env* . 2>/dev/null || log_warning "No root .env files to restore"
    cp "$backup_dir"/docker-compose.yml . 2>/dev/null || log_warning "No docker-compose.yml to restore"
    cp "$backup_dir"/package.json . 2>/dev/null || log_warning "No package.json to restore"
    cp "$backup_dir"/requirements.txt . 2>/dev/null || log_warning "No requirements.txt to restore"
    
    # Restore service configurations
    log_info "Restoring service configuration files..."
    if [ -d "360t-kg-api" ]; then
        cp "$backup_dir"/.env* 360t-kg-api/ 2>/dev/null || log_warning "No API .env files to restore"
    fi
    if [ -d "360t-kg-ui" ]; then
        cp "$backup_dir"/.env* 360t-kg-ui/ 2>/dev/null || log_warning "No UI .env files to restore"
    fi
    if [ -d "proxy-server" ]; then
        cp "$backup_dir"/.env* proxy-server/ 2>/dev/null || log_warning "No proxy .env files to restore"
    fi
    
    # Restore config directory if it exists in backup
    if [ -d "$backup_dir/config" ]; then
        log_info "Restoring config directory..."
        rm -rf config/
        cp -r "$backup_dir/config" .
    fi
    
    log_success "Configuration rollback completed"
    log_info "You may need to restart services for changes to take effect"
}

# Main function
main() {
    local service="$1"
    local backup_dir="$2"
    local dry_run="$3"
    local force="$4"
    
    if [ -z "$service" ]; then
        log_error "Service not specified"
        show_usage
        exit 1
    fi
    
    # Set default backup directory if not specified
    if [ -z "$backup_dir" ]; then
        if [ -L "backups/latest" ]; then
            backup_dir="backups/$(readlink backups/latest)"
        else
            # Find the most recent backup
            local latest_backup=$(ls -t backups/ | grep -E '^[0-9]{8}_[0-9]{6}$' | head -n1)
            if [ -n "$latest_backup" ]; then
                backup_dir="backups/$latest_backup"
            else
                log_error "No backup directory found. Please specify --backup-dir"
                exit 1
            fi
        fi
    fi
    
    # Validate backup directory
    if [ ! -d "$backup_dir" ]; then
        log_error "Backup directory not found: $backup_dir"
        exit 1
    fi
    
    if [ "$dry_run" != "true" ] && [ "$force" != "true" ]; then
        echo
        log_warning "This will rollback the $service service from backup: $backup_dir"
        echo
        read -p "Are you sure you want to proceed? (yes/no): " confirm
        if [ "$confirm" != "yes" ]; then
            log_info "Rollback cancelled by user"
            exit 0
        fi
    fi
    
    # Change to project root
    cd "$PROJECT_ROOT"
    
    # Perform service-specific rollback
    case "$service" in
        "api")
            rollback_api "$backup_dir" "$dry_run"
            ;;
        "ui")
            rollback_ui "$backup_dir" "$dry_run"
            ;;
        "proxy")
            rollback_proxy "$backup_dir" "$dry_run"
            ;;
        "database")
            rollback_database "$backup_dir" "$dry_run"
            ;;
        "config")
            rollback_config "$backup_dir" "$dry_run"
            ;;
        *)
            log_error "Unknown service: $service"
            log_info "Available services: api, ui, proxy, database, config"
            exit 1
            ;;
    esac
}

# Parse command line arguments
if [ $# -eq 0 ]; then
    show_usage
    exit 1
fi

SERVICE="$1"
shift

BACKUP_DIR=""
DRY_RUN="false"
FORCE="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        --backup-dir)
            BACKUP_DIR="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN="true"
            shift
            ;;
        --force)
            FORCE="true"
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Run main function
main "$SERVICE" "$BACKUP_DIR" "$DRY_RUN" "$FORCE"
