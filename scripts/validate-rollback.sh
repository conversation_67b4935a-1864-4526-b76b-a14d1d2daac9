#!/bin/bash
set -e

# Rollback Validation Script
# Validates that rollback procedures completed successfully

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to show usage
show_usage() {
    cat << EOF
🔍 Rollback Validation Script

Usage: $0 [OPTIONS]

Options:
    --service SERVICE   Validate specific service only (api|ui|proxy|database|all)
    --timeout SECONDS   Set timeout for service checks (default: 30)
    --verbose          Show detailed validation output
    --help             Show this help message

Examples:
    $0                          # Validate all services
    $0 --service api            # Validate API service only
    $0 --timeout 60 --verbose   # Extended timeout with detailed output

This script validates that services are running correctly after a rollback.
EOF
}

# Function to check if a service is running
check_service_running() {
    local service="$1"
    local timeout="${2:-30}"
    
    case "$service" in
        "api")
            log_info "Checking API service..."
            local retries=0
            local max_retries=$((timeout / 2))
            
            while [ $retries -lt $max_retries ]; do
                if curl -f http://localhost:3002/api/health >/dev/null 2>&1; then
                    log_success "API service is healthy"
                    return 0
                fi
                sleep 2
                retries=$((retries + 1))
            done
            
            log_error "API service is not responding"
            return 1
            ;;
            
        "ui")
            log_info "Checking UI service..."
            local retries=0
            local max_retries=$((timeout / 2))
            
            while [ $retries -lt $max_retries ]; do
                if curl -f http://localhost:5173 >/dev/null 2>&1; then
                    log_success "UI service is healthy"
                    return 0
                fi
                sleep 2
                retries=$((retries + 1))
            done
            
            log_warning "UI service is not responding (may need manual start)"
            return 1
            ;;
            
        "proxy")
            log_info "Checking proxy service..."
            local retries=0
            local max_retries=$((timeout / 2))
            
            while [ $retries -lt $max_retries ]; do
                if curl -f http://localhost:3001/health >/dev/null 2>&1; then
                    log_success "Proxy service is healthy"
                    return 0
                fi
                sleep 2
                retries=$((retries + 1))
            done
            
            log_error "Proxy service is not responding"
            return 1
            ;;
            
        "database")
            log_info "Checking database service..."
            local retries=0
            local max_retries=$((timeout / 2))
            
            while [ $retries -lt $max_retries ]; do
                if curl -f http://localhost:7474 >/dev/null 2>&1; then
                    # Also check if we can connect via Cypher
                    if docker exec kg_neo4j cypher-shell -u neo4j -p development_password "RETURN 1" >/dev/null 2>&1; then
                        log_success "Database service is healthy"
                        return 0
                    fi
                fi
                sleep 2
                retries=$((retries + 1))
            done
            
            log_error "Database service is not responding"
            return 1
            ;;
            
        *)
            log_error "Unknown service: $service"
            return 1
            ;;
    esac
}

# Function to check Docker containers
check_docker_containers() {
    log_info "Checking Docker containers..."
    
    local containers=("kg_neo4j" "kg_api")
    local all_healthy=true
    
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "$container.*Up"; then
            log_success "Container $container is running"
        else
            log_error "Container $container is not running"
            all_healthy=false
        fi
    done
    
    return $all_healthy
}

# Function to check configuration files
check_configuration() {
    log_info "Checking configuration files..."
    
    local config_files=(
        ".env"
        "docker-compose.yml"
        "package.json"
        "requirements.txt"
    )
    
    local all_present=true
    
    for file in "${config_files[@]}"; do
        if [ -f "$file" ]; then
            log_success "Configuration file $file is present"
        else
            log_warning "Configuration file $file is missing"
            all_present=false
        fi
    done
    
    # Check service-specific configurations
    if [ -d "360t-kg-api" ]; then
        if [ -f "360t-kg-api/.env" ] || [ -f "360t-kg-api/.env.example" ]; then
            log_success "API service configuration is present"
        else
            log_warning "API service configuration is missing"
            all_present=false
        fi
    fi
    
    if [ -d "360t-kg-ui" ]; then
        if [ -f "360t-kg-ui/.env" ] || [ -f "360t-kg-ui/.env.example" ]; then
            log_success "UI service configuration is present"
        else
            log_warning "UI service configuration is missing"
            all_present=false
        fi
    fi
    
    if [ -d "proxy-server" ]; then
        if [ -f "proxy-server/.env" ] || [ -f "proxy-server/.env.example" ]; then
            log_success "Proxy service configuration is present"
        else
            log_warning "Proxy service configuration is missing"
            all_present=false
        fi
    fi
    
    return $all_present
}

# Function to check database connectivity and data
check_database_data() {
    log_info "Checking database connectivity and data..."
    
    if ! docker exec kg_neo4j cypher-shell -u neo4j -p development_password "RETURN 1" >/dev/null 2>&1; then
        log_error "Cannot connect to database"
        return 1
    fi
    
    # Check if database has data
    local node_count=$(docker exec kg_neo4j cypher-shell -u neo4j -p development_password "MATCH (n) RETURN count(n) as count" --format plain 2>/dev/null | tail -n1 | tr -d '"' || echo "0")
    
    if [ "$node_count" -gt 0 ]; then
        log_success "Database contains $node_count nodes"
    else
        log_warning "Database appears to be empty"
    fi
    
    return 0
}

# Function to run comprehensive health checks
run_health_checks() {
    log_info "Running comprehensive health checks..."
    
    if [ -f "$SCRIPT_DIR/health-check.cjs" ]; then
        if node "$SCRIPT_DIR/health-check.cjs" >/dev/null 2>&1; then
            log_success "All health checks passed"
            return 0
        else
            log_warning "Some health checks failed"
            
            # Run health check with output for details
            log_info "Health check details:"
            node "$SCRIPT_DIR/health-check.cjs" || true
            return 1
        fi
    else
        log_warning "Health check script not found, skipping comprehensive checks"
        return 0
    fi
}

# Function to validate specific service
validate_service() {
    local service="$1"
    local timeout="$2"
    local verbose="$3"
    
    log_info "Validating $service service rollback..."
    
    local validation_passed=true
    
    # Check if service is running
    if ! check_service_running "$service" "$timeout"; then
        validation_passed=false
    fi
    
    # Service-specific validations
    case "$service" in
        "database")
            if ! check_database_data; then
                validation_passed=false
            fi
            ;;
        "api")
            # Additional API-specific checks could go here
            ;;
        "ui")
            # Additional UI-specific checks could go here
            ;;
        "proxy")
            # Additional proxy-specific checks could go here
            ;;
    esac
    
    if [ "$validation_passed" = true ]; then
        log_success "$service service rollback validation passed"
        return 0
    else
        log_error "$service service rollback validation failed"
        return 1
    fi
}

# Function to validate all services
validate_all() {
    local timeout="$1"
    local verbose="$2"
    
    log_info "Validating complete system rollback..."
    
    local overall_validation=true
    
    # Check configuration files
    if ! check_configuration; then
        overall_validation=false
    fi
    
    # Check Docker containers
    if ! check_docker_containers; then
        overall_validation=false
    fi
    
    # Check individual services
    local services=("database" "api" "proxy" "ui")
    for service in "${services[@]}"; do
        if ! validate_service "$service" "$timeout" "$verbose"; then
            overall_validation=false
        fi
    done
    
    # Run comprehensive health checks
    if ! run_health_checks; then
        overall_validation=false
    fi
    
    if [ "$overall_validation" = true ]; then
        log_success "Complete system rollback validation passed"
        return 0
    else
        log_error "System rollback validation failed"
        return 1
    fi
}

# Main function
main() {
    local service="$1"
    local timeout="$2"
    local verbose="$3"
    
    cd "$PROJECT_ROOT"
    
    echo
    log_info "🔍 Starting rollback validation..."
    log_info "Timestamp: $(date)"
    echo
    
    if [ -z "$service" ] || [ "$service" = "all" ]; then
        validate_all "$timeout" "$verbose"
    else
        validate_service "$service" "$timeout" "$verbose"
    fi
    
    local exit_code=$?
    
    echo
    if [ $exit_code -eq 0 ]; then
        log_success "🎉 Rollback validation completed successfully"
    else
        log_error "💥 Rollback validation failed"
        echo
        log_info "Troubleshooting steps:"
        log_info "1. Check service logs: docker-compose logs [service]"
        log_info "2. Verify configuration files are correct"
        log_info "3. Ensure all required environment variables are set"
        log_info "4. Try restarting services: docker-compose restart"
        log_info "5. Run health checks manually: node scripts/health-check.cjs"
    fi
    
    return $exit_code
}

# Parse command line arguments
SERVICE=""
TIMEOUT="30"
VERBOSE="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        --service)
            SERVICE="$2"
            shift 2
            ;;
        --timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        --verbose)
            VERBOSE="true"
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Run main function
main "$SERVICE" "$TIMEOUT" "$VERBOSE"
