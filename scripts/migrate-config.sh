#!/bin/bash
set -e

# Configuration Migration Script
# Migrates from legacy configuration to unified configuration system

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to show usage
show_usage() {
    cat << EOF
🔧 Configuration Migration Script

Usage: $0 [OPTIONS]

Options:
    --dry-run          Show what would be done without executing
    --backup           Create backup before migration (default: true)
    --force            Skip confirmation prompts
    --environment ENV  Target environment (development|staging|production|test)
    --help             Show this help message

Examples:
    $0                                    # Interactive migration with backup
    $0 --dry-run                          # Preview migration changes
    $0 --environment production --force   # Production migration without prompts

This script migrates from legacy configuration files to the unified configuration system.
EOF
}

# Function to detect current configuration
detect_current_config() {
    log_info "Detecting current configuration..."
    
    local config_files=()
    
    # Check for root configuration files
    if [ -f ".env" ]; then
        config_files+=(".env")
    fi
    
    if [ -f ".env.example" ]; then
        config_files+=(".env.example")
    fi
    
    # Check for service-specific configurations
    if [ -f "360t-kg-api/.env" ]; then
        config_files+=("360t-kg-api/.env")
    fi
    
    if [ -f "360t-kg-ui/.env" ]; then
        config_files+=("360t-kg-ui/.env")
    fi
    
    if [ -f "proxy-server/.env" ]; then
        config_files+=("proxy-server/.env")
    fi
    
    # Check for Python configuration
    if [ -f "config/environment.py" ]; then
        config_files+=("config/environment.py")
    fi
    
    if [ ${#config_files[@]} -eq 0 ]; then
        log_warning "No configuration files found"
        return 1
    fi
    
    log_success "Found ${#config_files[@]} configuration files:"
    for file in "${config_files[@]}"; do
        log_info "  - $file"
    done
    
    return 0
}

# Function to validate unified configuration
validate_unified_config() {
    log_info "Validating unified configuration..."
    
    # Check if TypeScript files exist
    local required_files=(
        "shared/config/types.ts"
        "shared/config/config-loader.ts"
        "shared/config/legacy/adapter.ts"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "Required file missing: $file"
            return 1
        fi
    done
    
    log_success "Unified configuration files are present"
    
    # Test configuration loading (if Node.js is available)
    if command -v node >/dev/null 2>&1; then
        log_info "Testing configuration loading..."
        
        # Create a simple test script
        cat > /tmp/test-config.js << 'EOF'
const fs = require('fs');

// Mock TypeScript modules for testing
const mockConfigLoader = {
  load: () => {
    // Apply legacy adapter
    const mappings = {
      'NEO4J_USER': 'NEO4J_USERNAME',
      'PORT': 'API_PORT',
      'PROXY_PORT': 'PROXY_SERVER_PORT'
    };

    Object.entries(mappings).forEach(([oldKey, newKey]) => {
      if (process.env[oldKey] && !process.env[newKey]) {
        process.env[newKey] = process.env[oldKey];
      }
    });

    return {
      environment: process.env.NODE_ENV || 'development',
      database: {
        uri: process.env.NEO4J_URI || 'neo4j://localhost:7687',
        username: process.env.NEO4J_USERNAME || 'neo4j',
        password: process.env.NEO4J_PASSWORD || 'password',
        database: process.env.NEO4J_DATABASE || 'neo4j'
      },
      api: {
        port: parseInt(process.env.API_PORT || process.env.PORT || '3002'),
        cors: { origins: ['*'] }
      }
    };
  },
  
  validate: (config) => {
    const errors = [];
    if (!config.database.uri) errors.push('Missing database URI');
    if (!config.database.username) errors.push('Missing database username');
    if (!config.database.password) errors.push('Missing database password');
    return { valid: errors.length === 0, errors };
  }
};

try {
  const config = mockConfigLoader.load();
  const validation = mockConfigLoader.validate(config);
  
  if (validation.valid) {
    console.log('✅ Configuration validation passed');
    process.exit(0);
  } else {
    console.log('❌ Configuration validation failed:', validation.errors.join(', '));
    process.exit(1);
  }
} catch (error) {
  console.log('❌ Configuration loading failed:', error.message);
  process.exit(1);
}
EOF
        
        if node /tmp/test-config.js; then
            log_success "Configuration loading test passed"
        else
            log_error "Configuration loading test failed"
            return 1
        fi
        
        rm -f /tmp/test-config.js
    else
        log_warning "Node.js not available, skipping configuration loading test"
    fi
    
    return 0
}

# Function to create unified environment file
create_unified_env() {
    local environment="$1"
    local dry_run="$2"
    
    log_info "Creating unified .env file for $environment environment..."
    
    if [ "$dry_run" = "true" ]; then
        log_info "Would create unified .env file with the following structure:"
        log_info "  - Database configuration (NEO4J_*)"
        log_info "  - API configuration (API_*, CORS_*)"
        log_info "  - LLM configuration (LLM_*)"
        log_info "  - Proxy configuration (PROXY_*)"
        log_info "  - Security configuration (SESSION_SECRET, etc.)"
        log_info "  - Logging configuration (LOG_*)"
        log_info "  - Monitoring configuration (HEALTH_*, METRICS_*, ALERTS_*)"
        return 0
    fi
    
    # Create unified .env file
    cat > .env.unified << EOF
# Unified Configuration for KnowledgeGraphVisualizer
# Generated by configuration migration script on $(date)

# Environment
NODE_ENV=$environment

# Database Configuration
NEO4J_URI=${NEO4J_URI:-neo4j://localhost:7687}
NEO4J_USERNAME=${NEO4J_USERNAME:-${NEO4J_USER:-neo4j}}
NEO4J_PASSWORD=${NEO4J_PASSWORD:-development_password}
NEO4J_DATABASE=${NEO4J_DATABASE:-neo4j}

# API Configuration
API_PORT=${API_PORT:-${PORT:-3002}}
CORS_ORIGINS=${CORS_ORIGINS:-http://localhost:3000,http://localhost:5173,http://localhost:3001}

# LLM Configuration
LLM_PRIMARY_PROVIDER=${LLM_PRIMARY_PROVIDER:-ollama}
LLM_OLLAMA_BASE_URL=${LLM_OLLAMA_BASE_URL:-${OLLAMA_BASE_URL:-http://localhost:11434}}
LLM_ANTHROPIC_API_KEY=${LLM_ANTHROPIC_API_KEY:-${ANTHROPIC_API_KEY:-}}
LLM_GOOGLE_API_KEY=${LLM_GOOGLE_API_KEY:-${GOOGLE_API_KEY:-}}
LLM_AZURE_API_KEY=${LLM_AZURE_API_KEY:-${AZURE_OPENAI_API_KEY:-}}
LLM_AZURE_ENDPOINT=${LLM_AZURE_ENDPOINT:-${AZURE_OPENAI_ENDPOINT:-}}

# Proxy Configuration
PROXY_SERVER_PORT=${PROXY_SERVER_PORT:-${PROXY_PORT:-3001}}
CHAT_API_URL=${CHAT_API_URL:-${FASTAPI_URL:-http://localhost:8000}}

# Frontend Configuration
VITE_API_URL=${VITE_API_URL:-http://localhost:3002/api}

# Security Configuration
SESSION_SECRET=${SESSION_SECRET:-development-secret-change-in-production}

# Logging Configuration
LOG_LEVEL=${LOG_LEVEL:-INFO}
LOG_CONSOLE_ENABLED=${LOG_CONSOLE_ENABLED:-true}
LOG_CONSOLE_COLORIZE=${LOG_CONSOLE_COLORIZE:-true}

# Monitoring Configuration
HEALTH_CHECK_ENABLED=${HEALTH_CHECK_ENABLED:-true}
HEALTH_CHECK_INTERVAL=${HEALTH_CHECK_INTERVAL:-30000}
METRICS_ENABLED=${METRICS_ENABLED:-false}
ALERTS_ENABLED=${ALERTS_ENABLED:-false}
EOF
    
    log_success "Created unified .env file: .env.unified"
    
    # Show differences if original .env exists
    if [ -f ".env" ]; then
        log_info "Differences from current .env:"
        diff .env .env.unified || true
    fi
}

# Function to migrate service configurations
migrate_service_configs() {
    local dry_run="$1"
    
    log_info "Migrating service configurations..."
    
    local services=("360t-kg-api" "360t-kg-ui" "proxy-server")
    
    for service in "${services[@]}"; do
        if [ -d "$service" ] && [ -f "$service/.env" ]; then
            log_info "Processing $service configuration..."
            
            if [ "$dry_run" = "true" ]; then
                log_info "Would migrate $service/.env to unified configuration"
            else
                # Create backup
                cp "$service/.env" "$service/.env.backup.$(date +%Y%m%d_%H%M%S)"
                
                # Create service-specific .env that sources unified config
                cat > "$service/.env.unified" << EOF
# Service-specific configuration for $service
# Sources from unified configuration

# Load unified configuration
$(cat .env.unified | grep -E "^(NEO4J_|API_|LLM_|PROXY_|VITE_|SESSION_|LOG_|HEALTH_|METRICS_|ALERTS_)")

# Service-specific overrides can be added below
EOF
                
                log_success "Migrated $service configuration"
            fi
        fi
    done
}

# Function to update Docker Compose
update_docker_compose() {
    local dry_run="$1"
    
    log_info "Updating Docker Compose configuration..."
    
    if [ "$dry_run" = "true" ]; then
        log_info "Would update docker-compose.yml to use unified .env file"
        return 0
    fi
    
    if [ -f "docker-compose.yml" ]; then
        # Create backup
        cp docker-compose.yml docker-compose.yml.backup.$(date +%Y%m%d_%H%M%S)
        
        # Update env_file references
        sed -i.bak 's|env_file: \.env|env_file: .env.unified|g' docker-compose.yml
        
        log_success "Updated Docker Compose configuration"
    else
        log_warning "docker-compose.yml not found"
    fi
}

# Main migration function
perform_migration() {
    local environment="$1"
    local dry_run="$2"
    local backup="$3"
    local force="$4"
    
    log_info "🔧 Starting configuration migration..."
    log_info "Environment: $environment"
    log_info "Dry run: $dry_run"
    log_info "Backup: $backup"
    
    # Detect current configuration
    if ! detect_current_config; then
        log_error "No configuration files found to migrate"
        return 1
    fi
    
    # Validate unified configuration system
    if ! validate_unified_config; then
        log_error "Unified configuration system validation failed"
        return 1
    fi
    
    if [ "$dry_run" != "true" ] && [ "$force" != "true" ]; then
        echo
        log_warning "This will migrate your configuration to the unified system"
        log_warning "Current configuration files will be backed up"
        echo
        read -p "Are you sure you want to proceed? (yes/no): " confirm
        if [ "$confirm" != "yes" ]; then
            log_info "Migration cancelled by user"
            return 0
        fi
    fi
    
    # Create backup if requested
    if [ "$backup" = "true" ] && [ "$dry_run" != "true" ]; then
        log_info "Creating configuration backup..."
        ./scripts/backup-system.sh >/dev/null
        log_success "Configuration backup created"
    fi
    
    # Create unified environment file
    create_unified_env "$environment" "$dry_run"
    
    # Migrate service configurations
    migrate_service_configs "$dry_run"
    
    # Update Docker Compose
    update_docker_compose "$dry_run"
    
    if [ "$dry_run" = "true" ]; then
        log_success "🎉 Migration preview completed successfully"
        log_info "Run without --dry-run to execute the migration"
    else
        log_success "🎉 Configuration migration completed successfully"
        log_info "Next steps:"
        log_info "1. Review the unified .env file: .env.unified"
        log_info "2. Test the configuration: node scripts/test-config-compatibility.cjs"
        log_info "3. Replace .env with .env.unified when ready"
        log_info "4. Update service configurations to use unified format"
    fi
}

# Parse command line arguments
ENVIRONMENT="development"
DRY_RUN="false"
BACKUP="true"
FORCE="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        --environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN="true"
            shift
            ;;
        --backup)
            BACKUP="true"
            shift
            ;;
        --no-backup)
            BACKUP="false"
            shift
            ;;
        --force)
            FORCE="true"
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
case "$ENVIRONMENT" in
    development|staging|production|test)
        ;;
    *)
        log_error "Invalid environment: $ENVIRONMENT"
        log_info "Valid environments: development, staging, production, test"
        exit 1
        ;;
esac

# Change to project root
cd "$PROJECT_ROOT"

# Load environment variables if available
if [ -f ".env" ]; then
    set -a
    source .env
    set +a
fi

# Perform migration
perform_migration "$ENVIRONMENT" "$DRY_RUN" "$BACKUP" "$FORCE"
