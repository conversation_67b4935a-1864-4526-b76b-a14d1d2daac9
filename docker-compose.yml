version: '3.8'

services:
  # Neo4j Database Service
  neo4j:
    image: neo4j:5.15-community
    container_name: kg_neo4j
    environment:
      - NEO4J_AUTH=neo4j/development_password
      - NEO4J_PLUGINS=["apoc", "graph-data-science"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*,gds.*
      - NEO4J_server_memory_heap_initial__size=1G
      - NEO4J_server_memory_heap_max__size=2G
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    healthcheck:
      test: ["CMD-SHELL", "cypher-shell -u neo4j -p development_password 'RETURN 1'"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

  # Node.js API Service
  api:
    build:
      context: ./360t-kg-api
      dockerfile: Dockerfile
    container_name: kg_api
    environment:
      - NODE_ENV=development
      - PORT=3002
      - NEO4J_URI=neo4j://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=development_password
    ports:
      - "3002:3002"
    volumes:
      - ./360t-kg-api:/app
      - /app/node_modules
    depends_on:
      neo4j:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3002/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # React UI Service
  ui:
    build:
      context: ./360t-kg-ui
      dockerfile: Dockerfile
    container_name: kg_ui
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:3002/api
    ports:
      - "5173:5173"
    volumes:
      - ./360t-kg-ui:/app
      - /app/node_modules
    depends_on:
      api:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5173 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped

  # Python FastAPI Service (for future chat feature)
  chat-api:
    build:
      context: .
      dockerfile: Dockerfile.python
    container_name: kg_chat_api
    environment:
      - PYTHONPATH=/app
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - PERPLEXITY_API_KEY=${PERPLEXITY_API_KEY}
      - NEO4J_URI=neo4j://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=development_password
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - chat_api_cache:/app/.cache
    depends_on:
      neo4j:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

volumes:
  neo4j_data:
  neo4j_logs:
  neo4j_import:
  neo4j_plugins:
  chat_api_cache:

networks:
  default:
    name: kg_network 